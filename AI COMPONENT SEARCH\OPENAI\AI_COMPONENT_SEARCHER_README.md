# 🤖 AI-Powered Electronics Component Search & Analysis

**Version 2.0.0** - Professional AI-enhanced component sourcing application

## 🌟 Overview

An advanced electronics component search application that combines AI-powered analysis with intelligent supplier sourcing. Designed for professional electronics engineers, hobbyists, and procurement specialists who need reliable component sourcing with AI assistance.

### 🎯 Key Features

- **🌐 Web-based AI**: No local installation! Uses DeepSeek, Chat<PERSON><PERSON>, <PERSON>, Perplexity via browser automation
- **🤖 AI-Powered Analysis**: Intelligent component classification and specification extraction
- **📄 Datasheet Intelligence**: Automatic datasheet downloading and AI-powered parameter extraction
- **🇮🇳 Indian Supplier Priority**: Cost-effective sourcing with local supplier preference
- **🔍 Smart Search**: Domain-specific questions and intelligent search refinements
- **📊 Quality Scoring**: Advanced data validation and sourcing readiness assessment
- **🏭 Manufacturer Recommendations**: Top 5 manufacturer suggestions with reputation analysis
- **⚡ Multiple Search Modes**: AI-enhanced, Smart, and Quick search options

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- Windows 10/11 (primary support)
- Internet connection for supplier searches
- Optional: Ollama for local AI (recommended)

### Installation

1. **Automated Installation** (Recommended):
   ```bash
   # Run the installation script
   install_ai_component_searcher.bat
   ```

2. **Manual Installation**:
   ```bash
   # Install dependencies
   pip install -r requirements.txt
   
   # Create directories
   mkdir datasheets exports cache logs
   
   # Run application
   python component_searcher.py
   ```

### AI Setup (Ready to Use!)

#### 🌐 Web-based AI (Default - No Setup Required!)
- **DeepSeek Chat**: Free, fast, no login required ✅
- **Perplexity AI**: Free, research-focused, no login required ✅
- **You.com Chat**: Free, general purpose, no login required ✅
- **ChatGPT Free**: Requires free OpenAI account
- **Claude Free**: Requires free Anthropic account

#### ⚙️ Configuration
1. Run application: `python component_searcher.py`
2. Go to **Tools → AI Configuration**
3. Enable your preferred providers
4. For login providers: Enter your credentials
5. Test providers to ensure they work

#### 🔧 Local AI (Optional)
```bash
# Only if you prefer local AI (slower):
# Install Ollama from https://ollama.ai
ollama pull qwen2:7b
```

## 📖 User Guide

### Search Modes

#### 🤖 AI-Enhanced Search
- **Best for**: Complex components, professional use
- **Features**: Full AI analysis, datasheet integration, manufacturer recommendations
- **Process**: 
  1. Enter component name
  2. AI analyzes and classifies component
  3. Provides manufacturer recommendations
  4. Downloads and analyzes datasheets
  5. Intelligent search across suppliers

#### 🔍 Smart Search
- **Best for**: Standard components with domain intelligence
- **Features**: Intelligent questions, quality scoring, package recommendations
- **Process**:
  1. Enter component name
  2. Answer domain-specific questions
  3. Get search refinements and package suggestions
  4. Search with quality validation

#### ⚡ Quick Search
- **Best for**: Simple searches, known components
- **Features**: Fast results from top suppliers only
- **Process**:
  1. Enter component name
  2. Immediate search across top 5 suppliers
  3. Basic price sorting

### Component Types Supported

- **Arduino Ecosystem**: Boards, shields, modules, accessories
- **Passive Components**: Resistors, capacitors, inductors
- **Active Components**: Transistors, diodes, ICs, microcontrollers
- **Sensors**: Temperature, humidity, motion, pressure sensors
- **Displays**: LCD, OLED, TFT displays
- **Connectors**: Headers, sockets, cables
- **Motors**: Servo, stepper, DC motors

### Quality Scoring System

- **🟢 Green (80-100%)**: High quality, sourcing ready
- **🟡 Yellow (60-79%)**: Good quality, minor issues
- **🔴 Red (0-59%)**: Quality issues, review required

## 🏭 Supplier Network

### Indian Suppliers (Priority)

#### Tier 1 (Most Reliable)
- Robu (Pune)
- Evelta (Mumbai)
- ElectronicsComp (Bangalore)
- Sunrom (Ahmedabad)
- Probots (Bangalore)
- Rhydolabz (Cochin)
- FabtoLab (Bangalore)

#### Tier 2 (Good Options)
- CrazyPi, RoboCraze, Robokits
- Ktron, Module143, ThinkRobotics
- FlyRobo, Quartz Components
- And 10+ more suppliers

### International Suppliers (Fallback)
- Digikey India
- Mouser India
- Element14 India
- RS Components India
- LCSC

## 🤖 AI Features

### Component Analysis
- **Type Classification**: Automatic component type detection
- **Specification Extraction**: Key parameters and values
- **Package Recommendations**: Suitable package types
- **Alternative Suggestions**: Compatible components
- **Confidence Scoring**: AI analysis reliability

### Datasheet Intelligence
- **Auto-Discovery**: Find datasheets from multiple sources
- **Download Management**: Organized storage and caching
- **Parameter Extraction**: AI-powered specification parsing
- **Comparison Tables**: Multi-manufacturer analysis
- **Validation**: Cross-reference with search results

### Manufacturer Intelligence
- **Top 5 Recommendations**: Best manufacturers for component type
- **Reputation Analysis**: Quality and reliability assessment
- **Availability Scoring**: Stock and supply chain status
- **Cost Analysis**: Price competitiveness evaluation

## ⚙️ Configuration

### AI Configuration (`ai_config.json`)
```json
{
  "default_provider": "ollama_local",
  "providers": {
    "ollama_local": {
      "base_url": "http://localhost:11434",
      "model": "qwen2:7b",
      "enabled": true
    },
    "openai_web": {
      "enabled": false,
      "email": "<EMAIL>",
      "session_token": "your-session-token"
    }
  },
  "analysis_settings": {
    "max_retries": 3,
    "timeout": 30,
    "confidence_threshold": 0.7
  }
}
```

### Supplier Configuration (`suppliers.json`)
- Automatically created with default suppliers
- Easily extensible for custom suppliers
- Enable/disable suppliers as needed

## 📊 Export Features

### Results Export
- **CSV Format**: Spreadsheet-compatible results
- **Quality Reports**: Detailed analysis and recommendations
- **Datasheet Archive**: Downloaded datasheets with metadata
- **Search History**: Previous searches and configurations

### Professional Reports
- **Sourcing Summary**: Executive overview
- **Quality Analysis**: Detailed quality metrics
- **Cost Comparison**: Price analysis across suppliers
- **Availability Report**: Stock status and lead times

## 🔧 Advanced Features

### Quality Validation
- **Data Completeness**: Missing information detection
- **Price Validation**: Reasonable price range checking
- **Stock Verification**: Availability confirmation
- **Supplier Reliability**: Historical performance scoring

### Search Intelligence
- **Domain Knowledge**: Electronics-specific understanding
- **Context Awareness**: Component type-specific questions
- **Search Refinement**: Intelligent term suggestions
- **Package Matching**: Appropriate package recommendations

## 🆘 Troubleshooting

### Common Issues

#### AI Not Working
- Check Ollama installation and model availability
- Verify network connectivity for web-based AI
- Review `ai_config.json` configuration

#### No Search Results
- Check internet connectivity
- Verify supplier websites are accessible
- Try different search terms or quick search mode

#### Slow Performance
- Use Quick Search for faster results
- Check system resources and close other applications
- Consider using local AI instead of web-based

### Support
- Use Help menu in application for detailed guides
- Check logs in `logs/` directory for error details
- Review configuration files for proper setup

## 🔄 Version History

### Version 2.0.0 (Current)
- ✅ AI-powered component analysis
- ✅ Datasheet downloading and analysis
- ✅ Enhanced UI with version management
- ✅ Multiple search modes
- ✅ Professional quality scoring
- ✅ Comprehensive help system

### Version 1.0.0 (Previous)
- Basic component search
- Indian supplier prioritization
- Simple quality validation
- Export functionality

## 📄 License

MIT License - See LICENSE file for details

## 🤝 Contributing

Contributions welcome! Please read CONTRIBUTING.md for guidelines.

---

**Developed for professional electronics sourcing with AI assistance**
*© 2025 - AI-Powered Component Search*
