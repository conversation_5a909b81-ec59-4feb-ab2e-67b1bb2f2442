#!/usr/bin/env python3
"""
Component Intelligence System
Domain-specific knowledge for electronics components
Provides intelligent search assistance and component classification
"""

import re
from typing import Dict, List, Tuple, Optional

class ComponentIntelligence:
    """Domain-specific intelligence for electronics components"""
    
    def __init__(self):
        self.component_database = self._build_component_database()
        self.package_database = self._build_package_database()
        self.search_patterns = self._build_search_patterns()
    
    def analyze_search_term(self, search_term: str) -> Dict:
        """Analyze search term and provide intelligent suggestions"""
        search_term = search_term.lower().strip()
        
        # Classify the component type
        component_type = self._classify_component(search_term)
        
        # Get relevant packages for this component type
        relevant_packages = self._get_relevant_packages(component_type)
        
        # Generate clarifying questions
        questions = self._generate_questions(search_term, component_type)
        
        # Suggest search refinements
        refinements = self._suggest_refinements(search_term, component_type)
        
        return {
            'component_type': component_type,
            'relevant_packages': relevant_packages,
            'questions': questions,
            'refinements': refinements,
            'search_strategy': self._get_search_strategy(component_type)
        }
    
    def _classify_component(self, search_term: str) -> str:
        """Classify the component type based on search term"""
        
        # Arduino ecosystem
        if 'arduino' in search_term:
            if any(x in search_term for x in ['shield', 'hat', 'cape']):
                return 'arduino_shield'
            elif any(x in search_term for x in ['uno', 'nano', 'mega', 'leonardo', 'micro']):
                return 'arduino_board'
            elif any(x in search_term for x in ['compatible', 'clone']):
                return 'arduino_compatible'
            else:
                return 'arduino_general'
        
        # Microcontrollers
        elif any(x in search_term for x in ['stm32', 'esp32', 'esp8266', 'atmega', 'pic', 'microcontroller']):
            return 'microcontroller'
        
        # Passive components
        elif any(x in search_term for x in ['resistor', 'ohm', 'kohm', 'mohm']):
            return 'resistor'
        elif any(x in search_term for x in ['capacitor', 'cap', 'farad', 'uf', 'pf', 'nf']):
            return 'capacitor'
        elif any(x in search_term for x in ['inductor', 'coil', 'henry', 'uh', 'mh']):
            return 'inductor'
        
        # Active components
        elif any(x in search_term for x in ['transistor', 'mosfet', 'bjt', 'fet']):
            return 'transistor'
        elif any(x in search_term for x in ['diode', 'led', 'zener']):
            return 'diode'
        elif any(x in search_term for x in ['ic', 'chip', 'processor']):
            return 'integrated_circuit'
        
        # Connectors and mechanical
        elif any(x in search_term for x in ['connector', 'header', 'socket', 'plug']):
            return 'connector'
        elif any(x in search_term for x in ['switch', 'button', 'relay']):
            return 'switch'
        
        # Sensors
        elif any(x in search_term for x in ['sensor', 'temperature', 'humidity', 'pressure', 'accelerometer']):
            return 'sensor'
        
        # Displays
        elif any(x in search_term for x in ['display', 'lcd', 'oled', 'tft', 'screen']):
            return 'display'
        
        # Motors and actuators
        elif any(x in search_term for x in ['motor', 'servo', 'stepper', 'actuator']):
            return 'motor'
        
        return 'general'
    
    def _get_relevant_packages(self, component_type: str) -> List[str]:
        """Get relevant package types for component"""
        return self.package_database.get(component_type, ['Any'])
    
    def _generate_questions(self, search_term: str, component_type: str) -> List[Dict]:
        """Generate intelligent clarifying questions"""
        questions = []
        
        if component_type == 'arduino_general':
            questions.extend([
                {
                    'question': 'What type of Arduino product are you looking for?',
                    'options': [
                        'Arduino Board (Uno, Nano, Mega)',
                        'Arduino Shield/Hat',
                        'Arduino-compatible board',
                        'Arduino accessories/cables',
                        'Arduino starter kit'
                    ],
                    'type': 'single_choice'
                }
            ])
        
        elif component_type == 'arduino_board':
            questions.extend([
                {
                    'question': 'Which Arduino board specifically?',
                    'options': [
                        'Arduino Uno R3',
                        'Arduino Nano',
                        'Arduino Mega 2560',
                        'Arduino Leonardo',
                        'Arduino Micro',
                        'Any Arduino board'
                    ],
                    'type': 'single_choice'
                },
                {
                    'question': 'Do you need original Arduino or compatible is fine?',
                    'options': ['Original Arduino', 'Compatible/Clone OK', 'No preference'],
                    'type': 'single_choice'
                }
            ])
        
        elif component_type == 'resistor':
            questions.extend([
                {
                    'question': 'What resistance value do you need?',
                    'type': 'text_input',
                    'placeholder': 'e.g., 10k, 1.2k, 470 ohm'
                },
                {
                    'question': 'What power rating?',
                    'options': ['1/8W', '1/4W', '1/2W', '1W', '2W', 'Any'],
                    'type': 'single_choice'
                },
                {
                    'question': 'Tolerance required?',
                    'options': ['1%', '5%', '10%', 'Any'],
                    'type': 'single_choice'
                }
            ])
        
        elif component_type == 'capacitor':
            questions.extend([
                {
                    'question': 'What capacitance value?',
                    'type': 'text_input',
                    'placeholder': 'e.g., 100uF, 22pF, 0.1uF'
                },
                {
                    'question': 'What voltage rating?',
                    'options': ['5V', '12V', '25V', '50V', '100V', 'Any'],
                    'type': 'single_choice'
                },
                {
                    'question': 'Capacitor type?',
                    'options': ['Ceramic', 'Electrolytic', 'Tantalum', 'Film', 'Any'],
                    'type': 'single_choice'
                }
            ])
        
        elif component_type == 'microcontroller':
            questions.extend([
                {
                    'question': 'Which microcontroller family?',
                    'options': [
                        'STM32 (ARM Cortex)',
                        'ESP32 (WiFi/Bluetooth)',
                        'ESP8266 (WiFi)',
                        'ATmega (Arduino-compatible)',
                        'PIC',
                        'Any'
                    ],
                    'type': 'single_choice'
                },
                {
                    'question': 'Do you need a development board or just the chip?',
                    'options': ['Development board', 'Just the microcontroller chip', 'Both'],
                    'type': 'single_choice'
                }
            ])
        
        elif component_type == 'sensor':
            questions.extend([
                {
                    'question': 'What type of sensor?',
                    'options': [
                        'Temperature sensor',
                        'Humidity sensor',
                        'Pressure sensor',
                        'Motion/Accelerometer',
                        'Light sensor',
                        'Gas sensor',
                        'Distance/Ultrasonic',
                        'Other'
                    ],
                    'type': 'single_choice'
                }
            ])
        
        return questions
    
    def _suggest_refinements(self, search_term: str, component_type: str) -> List[str]:
        """Suggest more specific search terms"""
        refinements = []
        
        if component_type == 'arduino_general':
            refinements = [
                'Arduino Uno R3',
                'Arduino Nano ATmega328P',
                'Arduino Mega 2560',
                'Arduino sensor shield',
                'Arduino starter kit'
            ]
        
        elif component_type == 'resistor':
            if not any(x in search_term for x in ['k', 'ohm', 'meg']):
                refinements = [
                    '10k resistor',
                    '1k resistor',
                    '470 ohm resistor',
                    '100k resistor'
                ]
        
        elif component_type == 'capacitor':
            if not any(x in search_term for x in ['uf', 'pf', 'nf']):
                refinements = [
                    '100uF capacitor',
                    '22pF capacitor',
                    '0.1uF capacitor',
                    '1000uF capacitor'
                ]
        
        return refinements
    
    def _get_search_strategy(self, component_type: str) -> Dict:
        """Get search strategy for component type"""
        strategies = {
            'arduino_board': {
                'categories': ['development-boards', 'arduino', 'microcontroller-boards'],
                'keywords': ['arduino', 'atmega328p', 'development board'],
                'filters': ['original', 'compatible', 'clone']
            },
            'resistor': {
                'categories': ['passive-components', 'resistors'],
                'keywords': ['resistor', 'ohm', 'carbon film', 'metal film'],
                'filters': ['through-hole', 'smd', 'power rating', 'tolerance']
            },
            'capacitor': {
                'categories': ['passive-components', 'capacitors'],
                'keywords': ['capacitor', 'ceramic', 'electrolytic', 'tantalum'],
                'filters': ['voltage rating', 'capacitance', 'package']
            }
        }
        
        return strategies.get(component_type, {
            'categories': ['general'],
            'keywords': [component_type],
            'filters': []
        })
    
    def _build_component_database(self) -> Dict:
        """Build comprehensive component database"""
        return {
            'arduino_boards': {
                'uno': {'pins': 14, 'analog': 6, 'mcu': 'ATmega328P'},
                'nano': {'pins': 14, 'analog': 8, 'mcu': 'ATmega328P'},
                'mega': {'pins': 54, 'analog': 16, 'mcu': 'ATmega2560'}
            },
            'microcontrollers': {
                'stm32f103': {'core': 'ARM Cortex-M3', 'flash': '64KB'},
                'esp32': {'core': 'Xtensa LX6', 'wifi': True, 'bluetooth': True},
                'atmega328p': {'core': 'AVR', 'flash': '32KB'}
            }
        }
    
    def _build_package_database(self) -> Dict:
        """Build package database for each component type"""
        return {
            'arduino_board': ['Any', 'Development Board'],
            'arduino_shield': ['Any', 'Shield Form Factor'],
            'resistor': ['Any', 'Through-hole', 'SMD', '0603', '0805', '1206', '1210'],
            'capacitor': ['Any', 'Through-hole', 'SMD', '0603', '0805', '1206', '1210', 'Radial', 'Axial'],
            'microcontroller': ['Any', 'DIP', 'QFP', 'LQFP', 'BGA', 'QFN', 'SOIC', 'Development Board'],
            'transistor': ['Any', 'TO-92', 'TO-220', 'SOT-23', 'SOT-223', 'TO-263'],
            'diode': ['Any', 'DO-41', 'SOD-123', 'SOT-23', 'Through-hole', 'SMD'],
            'integrated_circuit': ['Any', 'DIP', 'SOIC', 'QFP', 'BGA', 'QFN'],
            'connector': ['Any', 'Through-hole', 'SMD', 'Wire-to-board', 'Board-to-board'],
            'sensor': ['Any', 'DIP', 'SOIC', 'QFN', 'Module', 'Breakout Board'],
            'display': ['Any', 'Module', 'Breakout Board'],
            'motor': ['Any', 'Standard', 'Micro', 'Servo'],
            'general': ['Any', 'Through-hole', 'SMD', 'Module']
        }
    
    def _build_search_patterns(self) -> Dict:
        """Build search patterns for better matching"""
        return {
            'value_patterns': {
                'resistance': r'(\d+\.?\d*)\s*(k|m|meg|ohm|Ω)',
                'capacitance': r'(\d+\.?\d*)\s*(p|n|u|m)?f',
                'voltage': r'(\d+\.?\d*)\s*v',
                'current': r'(\d+\.?\d*)\s*(m|u)?a',
                'frequency': r'(\d+\.?\d*)\s*(k|m|g)?hz'
            }
        }

# Global instance
component_intelligence = ComponentIntelligence()

def analyze_component_search(search_term: str) -> Dict:
    """Main function to analyze component search"""
    return component_intelligence.analyze_search_term(search_term)

if __name__ == "__main__":
    # Test the intelligence system
    test_terms = ['arduino', 'arduino uno', '10k resistor', 'stm32', 'sensor']
    
    for term in test_terms:
        print(f"\n🔍 Analyzing: '{term}'")
        analysis = analyze_component_search(term)
        print(f"   Type: {analysis['component_type']}")
        print(f"   Packages: {analysis['relevant_packages'][:3]}...")
        print(f"   Questions: {len(analysis['questions'])} clarifying questions")
        print(f"   Refinements: {analysis['refinements'][:2]}...")
