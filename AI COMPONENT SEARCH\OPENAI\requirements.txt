# AI-Powered Electronics Component Search & Analysis Requirements

# Core dependencies
requests>=2.28.0
beautifulsoup4>=4.11.0
lxml>=4.9.0

# GUI framework
tkinterdnd2>=0.3.0

# Web scraping and automation for AI chatbots
selenium>=4.0.0
undetected-chromedriver>=3.5.0

# Data handling and analysis
pandas>=1.5.0
numpy>=1.21.0

# HTTP client enhancements
urllib3>=1.26.0

# HTML parsing
html5lib>=1.1

# PDF processing for datasheet analysis
PyPDF2>=3.0.0
pdfplumber>=0.7.0

# AI/LLM integration
google-generativeai>=0.3.0
openai>=1.0.0
anthropic>=0.3.0

# Optional: Local LLM support
langchain>=0.1.0
langchain-openai>=0.0.5

# Optional: For handling different encodings
chardet>=5.0.0

# Optional: For handling cookies and sessions
http-cookiejar>=0.1.3

# Optional: For advanced web automation
undetected-chromedriver>=3.5.0

# Optional: For image processing (component images)
Pillow>=9.0.0

# Optional: For data visualization
matplotlib>=3.5.0

# Optional: For configuration management
pyyaml>=6.0
