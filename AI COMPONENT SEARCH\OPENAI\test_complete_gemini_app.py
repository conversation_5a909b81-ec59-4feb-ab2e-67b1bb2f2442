#!/usr/bin/env python3
"""
Test Complete Gemini Integration
Test the complete AI-powered component search application with Gemini
"""

import os
import json

def setup_gemini_config():
    """Setup Gemini configuration with the provided API key"""
    print("🔧 Setting up Gemini configuration...")
    
    config = {
        "gemini_api_key": "AIzaSyAHl-ePSQ6RqY2wi0IUj17JRcoALefRmlg",
        "working_model": "gemini-1.5-flash",
        "test_date": "2025-01-27",
        "status": "working"
    }
    
    with open("gemini_config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("✅ Gemini configuration saved")
    return True

def test_gemini_analyzer():
    """Test the Gemini AI analyzer"""
    print("\n🤖 Testing Gemini AI Analyzer...")
    
    try:
        from gemini_ai_analyzer import get_gemini_analyzer
        
        analyzer = get_gemini_analyzer()
        
        if not analyzer.is_available():
            print("❌ Gemini analyzer not available")
            return False
        
        print("✅ Gemini analyzer initialized")
        
        # Test component analysis
        test_component = "arduino uno"
        print(f"🔍 Analyzing: {test_component}")
        
        analysis = analyzer.analyze_component(test_component)
        
        print(f"📋 Results:")
        print(f"  Component Type: {analysis.component_type}")
        print(f"  Manufacturer: {analysis.manufacturer}")
        print(f"  Confidence: {analysis.confidence_score:.2f}")
        print(f"  Packages: {', '.join(analysis.package_options[:3])}")
        print(f"  Applications: {', '.join(analysis.applications[:3])}")
        
        # Test manufacturer recommendations
        print(f"\n🏭 Testing manufacturer recommendations...")
        manufacturers = analyzer.get_manufacturer_recommendations(analysis.component_type, test_component)
        
        for i, mfg in enumerate(manufacturers[:3], 1):
            print(f"  {i}. {mfg['name']} - {mfg['reputation']} - {mfg['availability']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini analyzer test failed: {e}")
        return False

def test_gemini_search_dialog():
    """Test the Gemini search dialog"""
    print("\n🖥️ Testing Gemini Search Dialog...")
    
    try:
        import tkinter as tk
        from gemini_search_dialog import GeminiSearchDialog
        
        # Create a test root window
        root = tk.Tk()
        root.withdraw()  # Hide it
        
        print("✅ Gemini search dialog can be imported")
        print("📝 Note: Full dialog test requires GUI interaction")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Gemini search dialog test failed: {e}")
        return False

def test_main_application_integration():
    """Test main application integration"""
    print("\n🖥️ Testing Main Application Integration...")
    
    try:
        # Test import without running GUI
        import component_searcher
        
        print("✅ Main application imports successfully")
        print(f"📋 App Version: {component_searcher.APP_VERSION}")
        
        # Check if Gemini integration is present
        if hasattr(component_searcher, 'GeminiSearchDialog'):
            print("✅ Gemini search dialog integrated")
        
        return True
        
    except Exception as e:
        print(f"❌ Main application test failed: {e}")
        return False

def show_usage_instructions():
    """Show how to use the complete application"""
    print("\n" + "=" * 60)
    print("🚀 Complete Gemini AI Component Search Application")
    print("=" * 60)
    
    print("\n📋 QUICK START:")
    print("1. Run: python component_searcher.py")
    print("2. Enter component name (e.g., 'arduino uno')")
    print("3. Click '🤖 Gemini AI Search' button")
    print("4. AI analyzes component and enhances search")
    print("5. Get intelligent results with quality scoring")
    
    print("\n🤖 GEMINI AI FEATURES:")
    print("• Component type classification")
    print("• Specification extraction")
    print("• Package recommendations")
    print("• Manufacturer suggestions")
    print("• Alternative components")
    print("• Application identification")
    print("• Confidence scoring")
    
    print("\n🔧 AI CONFIGURATION:")
    print("• Go to Tools → AI Configuration")
    print("• Enter/update your Gemini API key")
    print("• Test the connection")
    print("• API key is saved for future use")
    
    print("\n🌟 BENEFITS:")
    print("• ⚡ Fast AI analysis (2-3 seconds)")
    print("• 🆓 Free usage (1,500 requests/day)")
    print("• 🧠 Smart component understanding")
    print("• 📊 Enhanced search quality")
    print("• 🏭 Professional manufacturer recommendations")
    print("• 🎯 Accurate component classification")
    
    print("\n📊 SEARCH MODES:")
    print("• 🤖 Gemini AI Search: Full AI analysis + enhanced search")
    print("• 🔍 Smart Search: Intelligent questions + quality scoring")
    print("• ⚡ Quick Search: Fast results from top suppliers")
    
    print("\n🇮🇳 SUPPLIER NETWORK:")
    print("• 25+ Indian suppliers prioritized")
    print("• Tier-based organization (most reliable first)")
    print("• Real-time stock and pricing")
    print("• International suppliers as fallback")

def main():
    """Run complete application test"""
    print("🚀 Complete Gemini AI Component Search Test")
    print("=" * 50)
    
    # Setup configuration
    if not setup_gemini_config():
        print("❌ Failed to setup configuration")
        return False
    
    # Run tests
    tests = [
        ("Gemini AI Analyzer", test_gemini_analyzer),
        ("Gemini Search Dialog", test_gemini_search_dialog),
        ("Main Application Integration", test_main_application_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Gemini AI application is ready!")
        show_usage_instructions()
        
        print(f"\n🚀 READY TO USE!")
        print(f"Your AI-powered component search application is complete.")
        print(f"Run: python component_searcher.py")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        print("\n🔧 Common solutions:")
        print("   - Install dependencies: pip install google-generativeai")
        print("   - Check internet connectivity")
        print("   - Verify Gemini API key is working")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    exit(0 if success else 1)
