<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Digi-Eye: Architecture & Plan</title>
    <style>
        body { font-family: sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 20px auto; padding: 0 20px; }
        h1, h2, h3 { color: #2c3e50; border-bottom: 2px solid #ecf0f1; padding-bottom: 10px; }
        h1 { font-size: 2.5em; }
        h2 { font-size: 2em; }
        h3 { font-size: 1.5em; border-bottom: 1px solid #ecf0f1; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        code { background-color: #ecf0f1; padding: 2px 5px; border-radius: 4px; font-family: monospace; }
        pre { background-color: #ecf0f1; padding: 15px; border-radius: 5px; white-space: pre-wrap; word-wrap: break-word; }
        .project-name { text-align: center; font-size: 2em; color: #3498db; margin-bottom: 0; }
        .tagline { text-align: center; font-style: italic; color: #7f8c8d; margin-top: 0; }
    </style>
</head>
<body>

    <h1 class="project-name">Project Digi-Eye</h1>
    <p class="tagline">A catchy, professional, and clear name for a digital system with a visual component.</p>

    <h2>1. Product Requirements Document (PRD)</h2>

    <h3>1.1. Introduction & Vision</h3>
    <p><strong>Project Digi-Eye</strong> is a proof-of-concept demonstration system designed to showcase a robust, non-contact method for reading and verifying digital displays using computer vision. The system will control a remote 7-segment LCD, then use a camera and a local OCR engine to read the displayed value in real-time.</p>
    <p>The vision is to provide a compelling, interactive demo for visiting customers, highlighting our capabilities in automation, remote telemetry, and custom hardware-software integration. It will serve as a foundation for future projects involving both digital and analog meter reading.</p>

    <h3>1.2. User Personas & Scenarios</h3>
    <ul>
        <li><strong>Persona 1: The Demo Operator (Internal Engineer)</strong>
            <p><em>Scenario:</em> An engineer is demonstrating the system to a potential client. They use the UI to cycle through pre-programmed sequences (count up, count down, random), then enter a number suggested by the client. The client sees the number appear on the physical display and almost instantly sees the same number recognized on the NUC's screen, along with a confidence score.</p>
        </li>
        <li><strong>Persona 2: The Potential Client (Visitor)</strong>
            <p><em>Scenario:</em> The client is evaluating our company's technical capabilities. They are impressed by the seamless, real-time interaction between the software command, the physical hardware, and the vision recognition system. They challenge the system by asking for specific numbers to be displayed, and witness its speed and accuracy firsthand.</p>
        </li>
    </ul>

    <h3>1.3. Functional Requirements (Features)</h3>
    <table>
        <thead>
            <tr>
                <th>Feature ID</th>
                <th>Feature Name</th>
                <th>Description</th>
                <th>Priority</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>F-01</td>
                <td><strong>UI Control Panel</strong></td>
                <td>A clean, professional GUI on the NUC that serves as the main control interface.</td>
                <td>Must-have</td>
            </tr>
            <tr>
                <td>F-02</td>
                <td><strong>Live Camera Feed</strong></td>
                <td>The UI must display a real-time video feed from the endoscope camera.</td>
                <td>Must-have</td>
            </tr>
            <tr>
                <td>F-03</td>
                <td><strong>Display Control: Sequential</strong></td>
                <td>The UI will have buttons to start/stop a sequential count (e.g., 0.00, 0.01, 0.02...). Configurable speed.</td>
                <td>Must-have</td>
            </tr>
            <tr>
                <td>F-04</td>
                <td><strong>Display Control: Random</strong></td>
                <td>The UI will have a button to display a random number within the 3.5-digit range.</td>
                <td>Must-have</td>
            </tr>
            <tr>
                <td>F-05</td>
                <td><strong>Display Control: Manual Input</strong></td>
                <td>The UI will have a text box and a "Send" button for the operator to type a specific number to be shown on the display.</td>
                <td>Must-have</td>
            </tr>
            <tr>
                <td>F-06</td>
                <td><strong>Local OCR Engine</strong></td>
                <td>The software will capture frames from the camera feed and use a local engine to recognize the digits. <strong>No cloud dependency.</strong></td>
                <td>Must-have</td>
            </tr>
            <tr>
                <td>F-07</td>
                <td><strong>Recognized Value Display</strong></td>
                <td>The UI will clearly display the number recognized by the OCR engine.</td>
                <td>Must-have</td>
            </tr>
            <tr>
                <td>F-08</td>
                <td><strong>Accuracy/Confidence Score</strong></td>
                <td>The UI will display a metric indicating the confidence of the OCR recognition.</td>
                <td>Should-have</td>
            </tr>
            <tr>
                <td>F-09</td>
                <td><strong>RP2040 Firmware</strong></td>
                <td>The RP2040 must run firmware that can receive commands via USB (Virtual COM) and drive the 7-segment display accordingly.</td>
                <td>Must-have</td>
            </tr>
        </tbody>
    </table>

    <h3>1.4. Non-Functional Requirements</h3>
    <table>
        <thead>
            <tr>
                <th>Requirement ID</th>
                <th>Requirement</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>NF-01</td>
                <td><strong>Performance</strong></td>
                <td>The end-to-end latency (from command sent to value recognized) should be under 500ms to feel "instant" in a demo setting.</td>
            </tr>
            <tr>
                <td>NF-02</td>
                <td><strong>Reliability</strong></td>
                <td>The OCR recognition accuracy should be >99.5% under the controlled lighting conditions of the hood.</td>
            </tr>
            <tr>
                <td>NF-03</td>
                <td><strong>Modularity</strong></td>
                <td>The OCR module should be easily swappable to accommodate future analog meter reading.</td>
            </tr>
            <tr>
                <td>NF-04</td>
                <td><strong>Platform</strong></td>
                <td>The entire software stack must run on a Windows-based NUC.</td>
            </tr>
        </tbody>
    </table>

    <h2>2. System Architecture & Analysis</h2>
    <p>The proposed architecture is sound. The separation of concerns between the NUC (high-level logic, UI, vision) and the RP2040 (real-time hardware control) is a robust design pattern.</p>
    <h3>Architecture Diagram</h3>
    <pre>
graph TD
    subgraph "NUC (Windows Host)"
        A[Frontend UI - Python/PyQt/CustomTkinter]
        B[Backend Orchestrator]
        C[Camera Manager - OpenCV]
        D[7-Segment OCR Engine]
        E[Serial Port Manager - pySerial]
    end

    subgraph "RP2040 Display Controller"
        F[Firmware - C/C++ Pico SDK]
        G[USB CDC (Virtual COM Port)]
        H[Display Driver Logic]
    end

    subgraph "Physical Hardware"
        I[3.5 Digit 7-Segment LCD]
        J[Endoscope Camera & Light Hood]
    end

    %% Data Flow
    A -- "User Commands (e.g., 'Set 12.34')" --> B
    B -- "Process & Format Command" --> E
    E -- "Write('S12.34\\n')" --> G

    G -- "Serial Data Received" --> F
    F -- "Parse Command & Drive GPIOs" --> H
    H -- "Electrical Signals" --> I

    J -- "USB Video Stream" --> C
    C -- "Capture Frame" --> D
    D -- "Recognized Digits: '12.34'" --> B
    B -- "Update UI State" --> A
    </pre>

    <h3>Architectural Issues & Mitigations:</h3>
    <ol>
        <li><strong>Issue:</strong> Potential for a laggy UI if camera processing and serial communication happen on the main UI thread.
            <ul><li><strong>Mitigation:</strong> A multi-threaded application on the NUC (UI thread, Camera/OCR worker, Serial worker).</li></ul>
        </li>
        <li><strong>Issue:</strong> Reliability of serial communication.
            <ul><li><strong>Mitigation:</strong> The NUC software must include error handling for COM port disconnection/reconnection.</li></ul>
        </li>
        <li><strong>Issue:</strong> Generic OCR engines struggling with 7-segment displays.
            <ul><li><strong>Mitigation:</strong> Use a custom, rule-based vision algorithm with OpenCV instead of a generic engine.</li></ul>
        </li>
    </ol>

    <h2>3. RP2040 Firmware Implementation Steps</h2>
    <ol>
        <li><strong>Setup Project:</strong> Use the Pico SDK with C/C++.</li>
        <li><strong>Enable USB CDC:</strong> Configure `tinyusb` to create a virtual COM port.</li>
        <li><strong>Define a Simple Serial Protocol:</strong> e.g., <code>S{value}\n</code>, <code>R\n</code>, <code>U{ms}\n</code>, <code>D{ms}\n</code>, <code>P\n</code>.</li>
        <li><strong>Implement Command Parser:</strong> Read from the USB serial buffer and parse commands.</li>
        <li><strong>Abstract Display Driver:</strong> Create functions like <code>display_set_digit(pos, num)</code>.</li>
        <li><strong>GPIO Control:</strong> Write code to translate digits into GPIO signals for the display driver.</li>
    </ol>

    <h2>4. Digital OCR Strategy (Local & Reliable)</h2>
    <ol>
        <li><strong>Technology:</strong> Python with <strong>OpenCV</strong>.</li>
        <li><strong>Preprocessing Pipeline:</strong> Isolate display, apply perspective transform, threshold to black and white, and segment into digit regions.</li>
        <li><strong>7-Segment Recognition Logic:</strong> For each digit, check the pixel intensity of 7 predefined segment zones. Use a lookup table to map the on/off state of the 7 segments to a number (0-9).</li>
    </ol>

    <h2>5. NUC Frontend & Demo Software</h2>
    <ul>
        <li><strong>Language/Framework:</strong> Python 3 with <strong>CustomTkinter</strong> for the UI.</li>
        <li><strong>Core Libraries:</strong> <code>opencv-python</code>, <code>pyserial</code>, <code>numpy</code>.</li>
        <li><strong>UI Layout:</strong> A two-pane layout with a live camera feed on the left and a control panel on the right.</li>
    </ul>

</body>
</html>