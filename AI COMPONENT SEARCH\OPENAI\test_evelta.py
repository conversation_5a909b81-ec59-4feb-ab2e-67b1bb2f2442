#!/usr/bin/env python3
"""
Test script specifically for Evelta.com to demonstrate working scraper
"""

import json
from scrapers import EveltaScraper

def test_evelta_scraper():
    """Test Evelta scraper with real data"""
    
    # Create Evelta config
    evelta_config = {
        "name": "<PERSON><PERSON>",
        "url": "https://www.evelta.com",
        "search_url": "https://www.evelta.com/?s={query}&post_type=product",
        "location": "Mumbai, Maharashtra",
        "active": True,
        "scraper_class": "EveltaScraper",
        "shipping_info": "₹55 for small components",
        "notes": "Reliable, good customer service"
    }
    
    # Test components
    test_components = [
        "Arduino",
        "LED",
        "resistor",
        "capacitor"
    ]
    
    print("🔍 Testing Evelta.com Scraper")
    print("=" * 60)
    print("Testing with real components and actual web scraping")
    print("=" * 60)
    
    # Create scraper
    scraper = EveltaScraper(evelta_config)
    
    for component in test_components:
        print(f"\n🔎 Searching for: {component}")
        print("-" * 40)
        
        try:
            # Search for component
            results = scraper.search_component(component, "Any", "", 1)
            
            if results:
                print(f"✅ Found {len(results)} results:")
                for i, result in enumerate(results, 1):
                    print(f"\n   {i}. {result['component']}")
                    print(f"      Price: ₹{result['price']}")
                    print(f"      Stock: {result['stock']}")
                    print(f"      Shipping: ₹{result['shipping']}")
                    print(f"      Total: ₹{result['total']}")
                    print(f"      URL: {result['url'][:80]}...")
            else:
                print("❌ No results found")
                
        except Exception as e:
            print(f"⚠️  Error: {str(e)}")
    
    print("\n" + "="*60)
    print("✅ Evelta scraper test completed!")
    print("This demonstrates that the scraping framework works")
    print("when the website allows automated access.")
    print("="*60)

def test_search_url_directly():
    """Test the search URL directly to see the HTML structure"""

    print("\n🔧 Testing Search URL Structure")
    print("=" * 60)

    import requests
    from bs4 import BeautifulSoup

    # Try a direct product category URL instead of search
    category_url = "https://www.evelta.com/development-boards-and-kits/arduino-and-compatible-boards/"

    try:
        print(f"Fetching Arduino category: {category_url}")

        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

        response = session.get(category_url, timeout=10)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')

            # Look for product containers in category page
            selectors = [
                'article.card',
                'div.card',
                'li.product',
                'div.product',
                'article.product',
                '.product-item',
                '.listItem',
                'article'
            ]

            for selector in selectors:
                products = soup.select(selector)
                if products:
                    print(f"✅ Found {len(products)} products with selector: {selector}")

                    # Show first product details
                    if products:
                        first_product = products[0]
                        print(f"\nFirst product HTML structure:")
                        print(f"Tag: {first_product.name}")
                        print(f"Classes: {first_product.get('class', [])}")

                        # Look for name
                        name_elem = first_product.find(['h1', 'h2', 'h3', 'h4', 'a'])
                        if name_elem:
                            name_text = name_elem.get_text().strip() if name_elem.get_text() else name_elem.get('title', '')
                            print(f"Name: {name_text[:50]}...")

                        # Look for price
                        price_elem = first_product.find(['span', 'div'], class_=lambda x: x and 'price' in str(x).lower())
                        if price_elem:
                            print(f"Price: {price_elem.get_text().strip()}")

                        # Look for any links
                        link_elem = first_product.find('a')
                        if link_elem:
                            print(f"Link: {link_elem.get('href', '')[:50]}...")

                    break
            else:
                print("❌ No product containers found with standard selectors")

                # Show page title to confirm it's a valid page
                title = soup.find('title')
                if title:
                    print(f"Page title: {title.get_text()}")

                # Look for any links that might be products
                all_links = soup.find_all('a', href=True)
                product_links = [link for link in all_links if 'product' in link.get('href', '').lower()]
                if product_links:
                    print(f"Found {len(product_links)} product links")
                    if product_links:
                        print(f"Sample product link: {product_links[0].get('href')}")
                        print(f"Link text: {product_links[0].get_text().strip()[:50]}...")

    except Exception as e:
        print(f"Error: {str(e)}")

    # Also test a specific product page to understand structure
    print(f"\n🔧 Testing Specific Product Page")
    print("-" * 40)

    try:
        # Test a specific product URL from the fetched page
        product_url = "https://evelta.com/stm32f407g-disc1-stm32f4-discovery-kit/"
        print(f"Fetching product: {product_url}")

        response = session.get(product_url, timeout=10)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract product details
            title = soup.find('title')
            if title:
                print(f"Product title: {title.get_text().strip()}")

            # Look for price
            price_selectors = ['.price', '[class*="price"]', '.productView-price']
            for selector in price_selectors:
                price_elem = soup.select_one(selector)
                if price_elem:
                    print(f"Price found: {price_elem.get_text().strip()}")
                    break

            # Look for stock
            stock_selectors = ['.stock', '[class*="stock"]', '.productView-info']
            for selector in stock_selectors:
                stock_elem = soup.select_one(selector)
                if stock_elem:
                    print(f"Stock info: {stock_elem.get_text().strip()[:50]}...")
                    break

    except Exception as e:
        print(f"Error testing product page: {str(e)}")

if __name__ == "__main__":
    print("🇮🇳 Evelta.com Scraper Test")
    print("Demonstrating working web scraper with real data")
    
    # Test search URL structure first
    test_search_url_directly()
    
    # Ask user before making multiple requests
    print("\n" + "="*60)
    response = input("Do you want to test component searches? (y/N): ").lower().strip()
    
    if response == 'y':
        test_evelta_scraper()
    else:
        print("Skipping component search test.")
    
    print("\n" + "="*60)
    print("This demonstrates the scraper framework working with real data!")
    print("Different suppliers need different approaches:")
    print("- Some block automated requests (like Robu)")
    print("- Some allow them (like Evelta)")
    print("- Some need Selenium for JavaScript")
    print("- Some have APIs")
    print("="*60)
