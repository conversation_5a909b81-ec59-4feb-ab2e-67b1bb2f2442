#!/usr/bin/env python3
"""
Interactive Visual Testing
Test ComponentAI with VISIBLE browser automation that you can watch
"""

import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import json

class VisualTester:
    """Visual testing that you can watch"""
    
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        self.driver = None
        self.test_results = []
        self.failed_tests = []
        
    def setup_visible_browser(self):
        """Setup Chrome browser that you can see"""
        print("🌐 Setting up VISIBLE Chrome browser...")
        print("   📺 You will see the browser window open")
        print("   👀 Watch the automated testing happen")
        
        options = Options()
        # Make browser VISIBLE and large
        options.add_argument("--start-maximized")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        try:
            self.driver = webdriver.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ Browser opened - you should see Chrome window")
            time.sleep(2)  # Give you time to see it
            return True
            
        except Exception as e:
            print(f"❌ Failed to open browser: {e}")
            return False
    
    def announce_test(self, test_name, description):
        """Announce what test is about to run"""
        print(f"\n🧪 TESTING: {test_name}")
        print(f"   📋 {description}")
        print(f"   👀 Watch the browser...")
        time.sleep(1)  # Give you time to read
    
    def test_page_load(self, path, page_name):
        """Test page loading with visual feedback"""
        self.announce_test(f"Page Load: {page_name}", f"Loading {path}")
        
        try:
            full_url = f"{self.base_url}{path}"
            print(f"   🔗 Navigating to: {full_url}")
            
            self.driver.get(full_url)
            time.sleep(2)  # Let you see the page load
            
            # Check if page loaded
            if "404" in self.driver.title or "Not Found" in self.driver.page_source:
                print(f"   ❌ Page not found: {page_name}")
                self.failed_tests.append(f"{page_name}: 404 Not Found")
                return False
            
            # Check for basic content
            body = self.driver.find_element(By.TAG_NAME, "body")
            if len(body.text) < 100:
                print(f"   ⚠️  Page seems empty: {page_name}")
                self.failed_tests.append(f"{page_name}: Page content too short")
                return False
            
            print(f"   ✅ Page loaded successfully: {page_name}")
            self.test_results.append(f"✅ {page_name} - Loaded OK")
            return True
            
        except Exception as e:
            print(f"   ❌ Error loading {page_name}: {str(e)}")
            self.failed_tests.append(f"{page_name}: {str(e)}")
            return False
    
    def test_navigation_links(self):
        """Test navigation links with visual feedback"""
        self.announce_test("Navigation Links", "Testing all navigation menu links")
        
        try:
            # Go to dashboard first
            self.driver.get(self.base_url)
            time.sleep(2)
            
            # Find navigation links
            nav_links = self.driver.find_elements(By.CSS_SELECTOR, ".navbar-nav .nav-link")
            
            print(f"   🔗 Found {len(nav_links)} navigation links")
            
            for i, link in enumerate(nav_links):
                try:
                    link_text = link.text.strip()
                    link_href = link.get_attribute('href')
                    
                    if not link_text or not link_href:
                        continue
                    
                    print(f"   🖱️  Clicking: {link_text}")
                    
                    # Scroll to link and click
                    self.driver.execute_script("arguments[0].scrollIntoView();", link)
                    time.sleep(0.5)
                    link.click()
                    time.sleep(2)  # Let you see the page change
                    
                    # Check if navigation worked
                    current_url = self.driver.current_url
                    if "404" not in self.driver.page_source:
                        print(f"   ✅ Navigation successful: {link_text}")
                        self.test_results.append(f"✅ Navigation: {link_text}")
                    else:
                        print(f"   ❌ Navigation failed: {link_text}")
                        self.failed_tests.append(f"Navigation {link_text}: 404 error")
                    
                    # Go back to dashboard for next test
                    self.driver.get(self.base_url)
                    time.sleep(1)
                    
                    # Re-find nav links (page refreshed)
                    nav_links = self.driver.find_elements(By.CSS_SELECTOR, ".navbar-nav .nav-link")
                    
                except Exception as e:
                    print(f"   ❌ Error testing link {i}: {str(e)}")
                    self.failed_tests.append(f"Navigation link {i}: {str(e)}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Navigation test failed: {str(e)}")
            self.failed_tests.append(f"Navigation test: {str(e)}")
            return False
    
    def test_form_interaction(self):
        """Test form interactions with visual feedback"""
        self.announce_test("Form Interaction", "Testing Add Component form")
        
        try:
            # Go to add component page
            self.driver.get(f"{self.base_url}/components/add")
            time.sleep(2)
            
            print("   📝 Filling out component form...")
            
            # Fill form fields
            name_field = self.driver.find_element(By.ID, "name")
            name_field.clear()
            name_field.send_keys("Visual Test Component")
            time.sleep(0.5)
            
            type_field = self.driver.find_element(By.ID, "type")
            type_field.send_keys("Test Type")
            time.sleep(0.5)
            
            desc_field = self.driver.find_element(By.ID, "description")
            desc_field.clear()
            desc_field.send_keys("This component was created during visual testing")
            time.sleep(0.5)
            
            print("   ✅ Form filled successfully")
            print("   💾 Submitting form...")
            
            # Submit form
            submit_btn = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
            submit_btn.click()
            time.sleep(3)  # Wait for submission
            
            # Check if redirected to components list
            if "/components" in self.driver.current_url and "/add" not in self.driver.current_url:
                print("   ✅ Form submission successful - redirected to components list")
                self.test_results.append("✅ Form Submission - Success")
                return True
            else:
                print("   ❌ Form submission failed - no redirect")
                self.failed_tests.append("Form submission: No redirect after submit")
                return False
                
        except Exception as e:
            print(f"   ❌ Form test failed: {str(e)}")
            self.failed_tests.append(f"Form interaction: {str(e)}")
            return False
    
    def test_ai_research_interface(self):
        """Test AI Research Lab interface"""
        self.announce_test("AI Research Lab", "Testing AI research interface")
        
        try:
            # Go to AI Research Lab
            self.driver.get(f"{self.base_url}/ai-research")
            time.sleep(2)
            
            print("   🔬 Testing AI Research Lab interface...")
            
            # Find component name input
            component_input = self.driver.find_element(By.ID, "componentName")
            component_input.clear()
            component_input.send_keys("Arduino Uno")
            time.sleep(1)
            
            print("   🤖 Starting AI research...")
            
            # Click research button
            research_btn = self.driver.find_element(By.ID, "researchBtn")
            research_btn.click()
            
            print("   ⏳ Waiting for AI research to complete...")
            time.sleep(5)  # Give AI time to respond
            
            # Check if research process is visible
            process_div = self.driver.find_element(By.ID, "researchProcess")
            if process_div.is_displayed():
                print("   ✅ AI Research interface working - process visible")
                self.test_results.append("✅ AI Research Lab - Interface working")
                return True
            else:
                print("   ❌ AI Research interface not responding")
                self.failed_tests.append("AI Research Lab: Interface not responding")
                return False
                
        except Exception as e:
            print(f"   ❌ AI Research test failed: {str(e)}")
            self.failed_tests.append(f"AI Research Lab: {str(e)}")
            return False
    
    def run_visual_tests(self):
        """Run all visual tests"""
        print("🎬 STARTING VISUAL TESTING SESSION")
        print("=" * 60)
        print("👀 WATCH THE BROWSER - All testing will be visible!")
        print("🚫 Please don't click in the browser during testing")
        print("=" * 60)
        
        # Setup browser
        if not self.setup_visible_browser():
            print("❌ Cannot start visual testing - browser setup failed")
            return False
        
        try:
            # Test server availability
            print("\n🔍 Checking if server is running...")
            response = requests.get(self.base_url, timeout=5)
            print("✅ Server is responding")
            
            # Run visual tests
            tests = [
                (lambda: self.test_page_load("/", "Dashboard"), "Dashboard Page"),
                (lambda: self.test_page_load("/components", "Components List"), "Components Page"),
                (lambda: self.test_page_load("/components/add", "Add Component"), "Add Component Page"),
                (lambda: self.test_page_load("/ai-research", "AI Research Lab"), "AI Research Lab"),
                (lambda: self.test_page_load("/analytics", "Analytics"), "Analytics Page"),
                (lambda: self.test_page_load("/system-info", "System Info"), "System Info Page"),
                (self.test_navigation_links, "Navigation Links"),
                (self.test_form_interaction, "Form Interaction"),
                (self.test_ai_research_interface, "AI Research Interface")
            ]
            
            print(f"\n🧪 Running {len(tests)} visual tests...")
            
            for i, (test_func, test_name) in enumerate(tests, 1):
                print(f"\n📋 Test {i}/{len(tests)}: {test_name}")
                try:
                    test_func()
                except Exception as e:
                    print(f"   ❌ Test crashed: {str(e)}")
                    self.failed_tests.append(f"{test_name}: Test crashed - {str(e)}")
                
                time.sleep(1)  # Pause between tests
            
            # Show results
            self.show_results()
            
            # Keep browser open for review
            print(f"\n🔍 TESTING COMPLETE")
            print(f"📺 Browser will stay open for 10 seconds for your review...")
            time.sleep(10)
            
            return len(self.failed_tests) == 0
            
        except Exception as e:
            print(f"❌ Visual testing failed: {str(e)}")
            return False
        
        finally:
            if self.driver:
                print("🔒 Closing browser...")
                self.driver.quit()
    
    def show_results(self):
        """Show test results"""
        print("\n" + "=" * 60)
        print("📊 VISUAL TESTING RESULTS")
        print("=" * 60)
        
        total_tests = len(self.test_results) + len(self.failed_tests)
        success_rate = (len(self.test_results) / total_tests * 100) if total_tests > 0 else 0
        
        print(f"📋 Total Tests: {total_tests}")
        print(f"✅ Passed: {len(self.test_results)}")
        print(f"❌ Failed: {len(self.failed_tests)}")
        print(f"📊 Success Rate: {success_rate:.1f}%")
        
        if self.test_results:
            print(f"\n✅ PASSED TESTS:")
            for result in self.test_results:
                print(f"   {result}")
        
        if self.failed_tests:
            print(f"\n❌ FAILED TESTS:")
            for failure in self.failed_tests:
                print(f"   ❌ {failure}")
        
        if len(self.failed_tests) == 0:
            print(f"\n🎉 ALL TESTS PASSED - SYSTEM READY!")
        else:
            print(f"\n🔧 ISSUES FOUND - NEEDS FIXING")

def main():
    """Run visual testing"""
    print("🎬 ComponentAI Visual Testing")
    print("=" * 40)
    print("This will open a Chrome browser and test the UI visually")
    print("You can watch everything happen in real-time!")
    print("=" * 40)
    
    # Ask for permission
    response = input("\n🚀 Ready to start visual testing? (y/n): ").lower().strip()
    if response != 'y':
        print("Testing cancelled.")
        return False
    
    tester = VisualTester()
    success = tester.run_visual_tests()
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
