import os
import re

# --- Unified Patterns based on "(rev "CONTENT")" structure ---
# This should work for both .kicad_sch and .kicad_pcb if they use this common field
KICAD_COMMON_REV_PATTERNS = [
    re.compile(r'\(\s*rev\s+"([^"]*)"\s*\)', re.IGNORECASE), 
    re.compile(r'\(\s*property\s+"(?:Revision|Version)"\s+"([^"]*)"\s*\)', re.IGNORECASE),
    # Fallback, less specific, might need removal if it causes false positives
    re.compile(r'(?:Rev|Revision|Version)\s*[:\s]\s*([^\s")]+)', re.IGNORECASE), 
]

def find_kicad_files(project_folder_path):
    """Finds .kicad_sch and .kicad_pcb files in a project folder."""
    sch_files = []
    pcb_files = []
    try:
        for filename in os.listdir(project_folder_path):
            full_path = os.path.join(project_folder_path, filename)
            if os.path.isfile(full_path):
                if filename.endswith(".kicad_sch"):
                    if '-bak' not in filename.lower() and '-cache' not in filename.lower():
                        sch_files.insert(0, full_path)
                    else:
                        sch_files.append(full_path)
                elif filename.endswith(".kicad_pcb"):
                    if '-bak' not in filename.lower(): # PCBs don't usually have -cache like schematics
                        pcb_files.insert(0, full_path)
                    else:
                        pcb_files.append(full_path)
    except FileNotFoundError:
        print(f"  Error: Directory not found: {project_folder_path}")
    except Exception as e:
        print(f"  Error listing files in {project_folder_path}: {e}")
    return sch_files, pcb_files

def extract_version_from_file_content(content, patterns):
    """Attempts to extract a version string from text content using regex patterns."""
    for pattern in patterns:
        matches = pattern.finditer(content)
        for match in matches:
            if match.groups() and match.group(1) and match.group(1).strip():
                return match.group(1).strip() 
    return None

def get_project_versions(project_folder_path):
    """Gets schematic and PCB versions for a KiCad project folder."""
    versions = {"schematic": "N/A", "pcb": "N/A"}
    
    if not os.path.isdir(project_folder_path):
        print(f"Error: Project folder not found or not a directory: {project_folder_path}")
        return versions

    sch_files, pcb_files = find_kicad_files(project_folder_path)

    # Process Schematic
    if sch_files:
        target_sch_file = sch_files[0] 
        print(f"  Processing schematic file: {os.path.basename(target_sch_file)}")
        try:
            with open(target_sch_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                sch_ver = extract_version_from_file_content(content, KICAD_COMMON_REV_PATTERNS)
                if sch_ver:
                    versions["schematic"] = sch_ver
                else:
                    print(f"    Schematic version not found in {os.path.basename(target_sch_file)} with current patterns.")
        except Exception as e:
            print(f"    Error reading schematic file {os.path.basename(target_sch_file)}: {e}")
    else:
        print(f"  No .kicad_sch files found in {project_folder_path}")

    # Process PCB
    if pcb_files:
        target_pcb_file = pcb_files[0]
        print(f"  Processing PCB file: {os.path.basename(target_pcb_file)}")
        try:
            with open(target_pcb_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                pcb_ver = extract_version_from_file_content(content, KICAD_COMMON_REV_PATTERNS)
                if pcb_ver:
                    versions["pcb"] = pcb_ver
                else:
                    print(f"    PCB version not found in {os.path.basename(target_pcb_file)} with current patterns.")
        except Exception as e:
            print(f"    Error reading PCB file {os.path.basename(target_pcb_file)}: {e}")
    else:
        print(f"  No .kicad_pcb files found in {project_folder_path}")
        
    return versions

if __name__ == "__main__":
    # === IMPORTANT: ENSURE THIS PATH IS CORRECT ===
    example_project_path = r"P:\P-0278-PCB_DETECTRA-Lite v1-CARRIERBOARD-W-ESP32-S3-LoRA"
    # example_project_path = r"P:\Path\To\Your\Example\KiCad_Project_Folder" # Default placeholder
    # =============================================
    
    print(f"Attempting to extract SCH and PCB versions from project folder: '{example_project_path}'")
    
    if "P:\\Path\\To\\Your\\Example\\KiCad_Project_Folder" in example_project_path or not os.path.isdir(example_project_path):
        print("\n!!! ERROR: 'example_project_path' in the script is not set to a valid KiCad project folder. !!!")
        print("!!! Please edit the script and update this path. !!!")
    else:
        extracted_versions = get_project_versions(example_project_path)
        print("\n--- Extracted Versions ---")
        sch_display = extracted_versions['schematic']
        pcb_display = extracted_versions['pcb']
        print(f"  Schematic Raw: {sch_display}")
        print(f"  PCB Raw      : {pcb_display}")
        
        combined_parts = []
        if sch_display and sch_display != "N/A":
            combined_parts.append(f"SCH-{sch_display}")
        if pcb_display and pcb_display != "N/A":
            if combined_parts and sch_display == pcb_display: # If versions are same, show combined
                 combined_parts = [f"SCH/PCB-{sch_display}"]
            elif combined_parts: # If different and sch exists, append pcb
                combined_parts.append(f"PCB-{pcb_display}")
            else: # Only pcb exists
                combined_parts.append(f"PCB-{pcb_display}")

        final_display_version = ", ".join(combined_parts) if combined_parts else "N/A"
        print(f"  Combined Display : {final_display_version}")