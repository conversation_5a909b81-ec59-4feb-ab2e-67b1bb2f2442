# P:\KiCadProjectBrowserTool\project_browser\config.py
import os
import re

# --- Application Version ---
SERVER_VERSION = "0.3.0" # Version of the backend application

# --- Path Configuration ---
# SCRIPT_DIR is the directory containing this config.py file (project_browser/)
APP_DIR = os.path.dirname(os.path.abspath(__file__))

# TOOL_ROOT_DIR is the parent of APP_DIR (KiCadProjectBrowserTool/)
TOOL_ROOT_DIR = os.path.dirname(APP_DIR)

# ROOT_DIR_TO_SCAN is the parent of TOOL_ROOT_DIR (e.g., P:\)
# This assumes KiCadProjectBrowserTool is directly under the master KiCad project folder.
ROOT_DIR_TO_SCAN = os.path.dirname(TOOL_ROOT_DIR)

# Index file for caching project data
INDEX_FILE_NAME = 'project_index.json'
INDEX_FILE_PATH = os.path.join(TOOL_ROOT_DIR, INDEX_FILE_NAME) # Cache file in KiCadProjectBrowserTool/

# --- Scanner Configuration ---
# Regex for project folders
PROJECT_FOLDER_PATTERN = re.compile(r"^(P-[\w\d]+)[-\s_]+(.+)$")

# Folders to explicitly skip during scanning.
# These are checked against folder names at each level of os.walk.
EXPLICITLY_SKIP_FOLDERS = {
    "KiCadProjectBrowserTool",  # Always skip the tool's own folder (relative to ROOT_DIR_TO_SCAN)
    ".git",
    "node_modules",
    "$RECYCLE.BIN", # Common Windows recycle bin
    "System Volume Information", # Common Windows system folder
    # --- User-specific folders to skip ---
    # Add names of other large, non-KiCad related folders that exist
    # directly under ROOT_DIR_TO_SCAN (e.g., P:\) or are common subfolders
    # you want to universally skip.
    # Examples (uncomment and customize if needed):
    # "Windows",
    # "Program Files",
    # "Software_Installers",
    # "Video_Archive",
    # "My Large Backup Folder",
}

# --- Flask Configuration ---
DEBUG_MODE = True # Set to False for a "production" like setup, True for development
SERVER_HOST = '127.0.0.1'
SERVER_PORT = 5000
USE_RELOADER = False # Important if scan_on_startup is enabled with debug mode

# --- Logging Configuration (Placeholder for now) ---
LOG_LEVEL = "INFO"

# --- UI Configuration (Placeholder, can be used by JS later if needed) ---
UI_VERSION = "0.3.0" # Version for the HTML/JS frontend (manual sync for now)

if __name__ == '__main__':
    # This part is just for testing/viewing the config values if you run this file directly
    print("--- Configuration Values ---")
    print(f"SERVER_VERSION: {SERVER_VERSION}")
    print(f"APP_DIR (project_browser/): {APP_DIR}")
    print(f"TOOL_ROOT_DIR (KiCadProjectBrowserTool/): {TOOL_ROOT_DIR}")
    print(f"ROOT_DIR_TO_SCAN: {ROOT_DIR_TO_SCAN}")
    print(f"INDEX_FILE_PATH: {INDEX_FILE_PATH}")
    print(f"PROJECT_FOLDER_PATTERN: {PROJECT_FOLDER_PATTERN.pattern}")
    print(f"EXPLICITLY_SKIP_FOLDERS: {EXPLICITLY_SKIP_FOLDERS}")
    print(f"DEBUG_MODE: {DEBUG_MODE}")
    print(f"SERVER_HOST: {SERVER_HOST}")
    print(f"SERVER_PORT: {SERVER_PORT}")
    print(f"UI_VERSION: {UI_VERSION}")