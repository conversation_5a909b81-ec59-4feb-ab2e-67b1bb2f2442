@echo off
title Setup Gemini AI Component Search Environment

echo ========================================
echo  Setup Gemini AI Component Search
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found

echo.
echo Checking virtual environment...
if not exist ".venv" (
    echo Creating virtual environment...
    python -m venv .venv
    echo ✅ Virtual environment created
) else (
    echo ✅ Virtual environment exists
)

echo.
echo Activating virtual environment...
call .venv\Scripts\activate

echo.
echo Installing/updating dependencies...
pip install --upgrade pip
pip install google-generativeai
pip install requests beautifulsoup4 lxml
pip install selenium undetected-chromedriver
pip install tkinterdnd2

echo.
echo Verifying Gemini AI library...
python -c "import google.generativeai; print('✅ Google Generative AI library working')"

echo.
echo Creating Gemini configuration...
if not exist "gemini_config.json" (
    echo {> gemini_config.json
    echo   "gemini_api_key": "AIzaSyAHl-ePSQ6RqY2wi0IUj17JRcoALefRmlg",>> gemini_config.json
    echo   "working_model": "gemini-1.5-flash",>> gemini_config.json
    echo   "status": "configured">> gemini_config.json
    echo }>> gemini_config.json
    echo ✅ Gemini configuration created with your API key
) else (
    echo ✅ Gemini configuration exists
)

echo.
echo Testing Gemini AI connection...
python -c "from gemini_ai_analyzer import get_gemini_analyzer; analyzer = get_gemini_analyzer(); print('✅ Gemini AI ready:', analyzer.is_available())"

echo.
echo ========================================
echo  Setup Complete!
echo ========================================
echo.
echo Your Gemini AI Component Search is ready to use!
echo.
echo TO RUN THE APPLICATION:
echo   1. Double-click: run_gemini_app.bat
echo   OR
echo   2. Run manually:
echo      - .venv\Scripts\activate
echo      - python component_searcher.py
echo.
echo FEATURES READY:
echo   ✅ Gemini AI component analysis
echo   ✅ 35+ Indian suppliers
echo   ✅ Professional quality scoring
echo   ✅ Real-time progress tracking
echo   ✅ Smart search normalization
echo.
echo Your API key is pre-configured and ready!
echo.
pause
