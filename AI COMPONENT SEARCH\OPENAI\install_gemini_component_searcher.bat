@echo off
title Gemini AI-Powered Component Search Installation

echo ========================================
echo  Gemini AI Component Search v2.0.0
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found

echo.
echo Installing core dependencies...
pip install requests beautifulsoup4 lxml urllib3

echo.
echo Installing GUI dependencies...
pip install tkinterdnd2

echo.
echo Installing web scraping dependencies...
pip install selenium html5lib chardet

echo.
echo Installing Gemini AI dependencies...
pip install google-generativeai

echo.
echo Installing optional dependencies...
pip install undetected-chromedriver Pillow matplotlib pyyaml http-cookiejar

echo.
echo Creating application directories...
if not exist "datasheets" mkdir datasheets
if not exist "datasheets\downloaded" mkdir datasheets\downloaded
if not exist "datasheets\analyzed" mkdir datasheets\analyzed
if not exist "exports" mkdir exports
if not exist "cache" mkdir cache
if not exist "logs" mkdir logs

echo.
echo Creating Gemini AI configuration...
echo {> gemini_config.json
echo   "gemini_api_key": "",>> gemini_config.json
echo   "working_model": "gemini-1.5-flash",>> gemini_config.json
echo   "status": "not_configured">> gemini_config.json
echo }>> gemini_config.json

echo.
echo Creating run script...
echo @echo off> run_gemini_component_searcher.bat
echo echo Starting Gemini AI-Powered Component Search...>> run_gemini_component_searcher.bat
echo python component_searcher.py>> run_gemini_component_searcher.bat
echo pause>> run_gemini_component_searcher.bat

echo.
echo Creating test script...
echo @echo off> test_gemini_setup.bat
echo echo Testing Gemini AI Setup...>> test_gemini_setup.bat
echo python test_complete_gemini_app.py>> test_gemini_setup.bat

echo.
echo ========================================
echo Installation Complete!
echo ========================================
echo.
echo NEXT STEPS:
echo.
echo 1. GET GEMINI API KEY (FREE):
echo    - Go to: https://aistudio.google.com/
echo    - Sign in with Google account
echo    - Click "Get API key" or "Create API key"
echo    - Copy the key (starts with 'AIza...')
echo.
echo 2. CONFIGURE THE APPLICATION:
echo    - Run: python component_searcher.py
echo    - Go to Tools → AI Configuration
echo    - Enter your Gemini API key
echo    - Test the connection
echo.
echo 3. START USING AI-POWERED SEARCH:
echo    - Enter component name (e.g., 'arduino uno')
echo    - Click '🤖 Gemini AI Search'
echo    - Get intelligent component analysis
echo    - Enhanced search across Indian suppliers
echo.
echo 4. FEATURES AVAILABLE:
echo    - 🤖 Gemini AI component analysis
echo    - 📊 Smart component classification
echo    - 🏭 Manufacturer recommendations
echo    - 📈 Quality scoring and validation
echo    - 🇮🇳 Indian supplier prioritization
echo    - ⚡ Multiple search modes
echo.
echo 5. GEMINI AI BENEFITS:
echo    - ⚡ Fast analysis (2-3 seconds)
echo    - 🆓 Free usage (1,500 requests/day)
echo    - 🧠 Smart component understanding
echo    - 📋 Structured data extraction
echo    - 🔄 Always up-to-date AI model
echo.
echo QUICK START:
echo   1. Get API key: https://aistudio.google.com/
echo   2. Run: run_gemini_component_searcher.bat
echo   3. Configure API key in Tools menu
echo   4. Start searching with AI power!
echo.
echo TEST YOUR SETUP:
echo   Run: test_gemini_setup.bat
echo.
pause
