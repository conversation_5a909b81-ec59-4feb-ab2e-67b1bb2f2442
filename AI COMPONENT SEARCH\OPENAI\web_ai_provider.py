#!/usr/bin/env python3
"""
Web-based AI Provider
Automates interaction with web-based chatbots like ChatGPT, DeepSeek, Claude, etc.
Uses Selenium for browser automation to send queries and get responses.
"""

import time
import json
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import undetected_chromedriver as uc

@dataclass
class WebAIProvider:
    """Configuration for a web-based AI provider"""
    name: str
    url: str
    input_selector: str
    send_selector: str
    output_selector: str
    wait_selector: str
    login_required: bool = False
    login_url: str = ""
    email_selector: str = ""
    password_selector: str = ""

class WebAIAutomator:
    """Automates interaction with web-based AI chatbots"""
    
    def __init__(self):
        self.driver = None
        self.current_provider = None
        self.session_active = False
        
        # Define supported providers
        self.providers = {
            "deepseek": WebAIProvider(
                name="DeepSeek Chat",
                url="https://chat.deepseek.com/",
                input_selector="textarea[placeholder*='Message']",
                send_selector="button[type='submit']",
                output_selector="div[class*='message'] div[class*='content']",
                wait_selector="div[class*='typing']",
                login_required=False
            ),
            "chatgpt_free": WebAIProvider(
                name="ChatGPT Free",
                url="https://chat.openai.com/",
                input_selector="textarea[placeholder*='Message']",
                send_selector="button[data-testid='send-button']",
                output_selector="div[data-message-author-role='assistant'] div[class*='markdown']",
                wait_selector="div[class*='result-thinking']",
                login_required=True,
                login_url="https://chat.openai.com/auth/login"
            ),
            "claude_free": WebAIProvider(
                name="Claude Free",
                url="https://claude.ai/",
                input_selector="div[contenteditable='true']",
                send_selector="button[aria-label*='Send']",
                output_selector="div[class*='font-claude-message']",
                wait_selector="div[class*='thinking']",
                login_required=True,
                login_url="https://claude.ai/login"
            ),
            "perplexity": WebAIProvider(
                name="Perplexity AI",
                url="https://www.perplexity.ai/",
                input_selector="textarea[placeholder*='Ask anything']",
                send_selector="button[aria-label*='Submit']",
                output_selector="div[class*='prose'] div",
                wait_selector="div[class*='loading']",
                login_required=False
            ),
            "you_chat": WebAIProvider(
                name="You.com Chat",
                url="https://you.com/search?q=&tbm=youchat",
                input_selector="textarea[data-testid='youchat-text-input']",
                send_selector="button[data-testid='youchat-send-button']",
                output_selector="div[data-testid='youchat-text'] p",
                wait_selector="div[class*='loading']",
                login_required=False
            )
        }
        
        self.load_config()
    
    def load_config(self):
        """Load web AI configuration"""
        config_file = "web_ai_config.json"
        default_config = {
            "default_provider": "deepseek",
            "browser_settings": {
                "headless": False,
                "window_size": "1280,720",
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "timeout": 30
            },
            "credentials": {
                "chatgpt_email": "",
                "chatgpt_password": "",
                "claude_email": "",
                "claude_password": ""
            },
            "automation_settings": {
                "typing_delay": 0.1,
                "response_timeout": 60,
                "retry_attempts": 3
            }
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    self.config = json.load(f)
            except Exception:
                self.config = default_config
        else:
            self.config = default_config
            self.save_config()
    
    def save_config(self):
        """Save web AI configuration"""
        with open("web_ai_config.json", 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def setup_browser(self, headless: bool = None) -> bool:
        """Setup Chrome browser with undetected-chromedriver"""
        try:
            if headless is None:
                headless = self.config["browser_settings"]["headless"]
            
            # Chrome options
            options = uc.ChromeOptions()
            
            if headless:
                options.add_argument("--headless")
            
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # Set window size
            window_size = self.config["browser_settings"]["window_size"]
            options.add_argument(f"--window-size={window_size}")
            
            # Create undetected Chrome driver
            self.driver = uc.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            return True
            
        except Exception as e:
            print(f"Failed to setup browser: {e}")
            return False
    
    def connect_to_provider(self, provider_name: str) -> Dict[str, Any]:
        """Connect to a web AI provider"""
        if provider_name not in self.providers:
            return {"success": False, "error": f"Provider {provider_name} not supported"}
        
        provider = self.providers[provider_name]
        
        try:
            if not self.driver:
                if not self.setup_browser():
                    return {"success": False, "error": "Failed to setup browser"}
            
            print(f"Connecting to {provider.name}...")
            self.driver.get(provider.url)
            
            # Wait for page to load
            time.sleep(3)
            
            # Handle login if required
            if provider.login_required:
                login_result = self.handle_login(provider, provider_name)
                if not login_result["success"]:
                    return login_result
            
            # Wait for chat interface
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, provider.input_selector))
                )
                
                self.current_provider = provider
                self.session_active = True
                
                return {
                    "success": True,
                    "provider": provider.name,
                    "status": "Connected and ready"
                }
                
            except TimeoutException:
                return {"success": False, "error": "Chat interface not found"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def handle_login(self, provider: WebAIProvider, provider_name: str) -> Dict[str, Any]:
        """Handle login for providers that require it"""
        try:
            # Check if already logged in
            try:
                self.driver.find_element(By.CSS_SELECTOR, provider.input_selector)
                return {"success": True, "message": "Already logged in"}
            except NoSuchElementException:
                pass
            
            # Get credentials
            email_key = f"{provider_name.split('_')[0]}_email"
            password_key = f"{provider_name.split('_')[0]}_password"
            
            email = self.config["credentials"].get(email_key, "")
            password = self.config["credentials"].get(password_key, "")
            
            if not email or not password:
                return {
                    "success": False, 
                    "error": f"Credentials not configured for {provider.name}. Please update web_ai_config.json"
                }
            
            # Navigate to login page if needed
            if provider.login_url and provider.login_url not in self.driver.current_url:
                self.driver.get(provider.login_url)
                time.sleep(2)
            
            # Find and fill email
            if provider.email_selector:
                email_field = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, provider.email_selector))
                )
                email_field.clear()
                email_field.send_keys(email)
            
            # Find and fill password
            password_field = self.driver.find_element(By.CSS_SELECTOR, "input[type='password']")
            password_field.clear()
            password_field.send_keys(password)
            
            # Submit login
            login_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
            login_button.click()
            
            # Wait for login to complete
            time.sleep(5)
            
            # Check if login was successful
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, provider.input_selector))
                )
                return {"success": True, "message": "Login successful"}
            except TimeoutException:
                return {"success": False, "error": "Login failed or took too long"}
            
        except Exception as e:
            return {"success": False, "error": f"Login error: {str(e)}"}
    
    def send_query(self, query: str) -> Dict[str, Any]:
        """Send a query to the current AI provider"""
        if not self.session_active or not self.current_provider:
            return {"success": False, "error": "No active session"}
        
        try:
            provider = self.current_provider
            
            # Find input field
            input_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, provider.input_selector))
            )
            
            # Clear and type query
            input_field.clear()
            time.sleep(0.5)
            
            # Type with delay to avoid detection
            for char in query:
                input_field.send_keys(char)
                time.sleep(self.config["automation_settings"]["typing_delay"])
            
            # Send the message
            try:
                send_button = self.driver.find_element(By.CSS_SELECTOR, provider.send_selector)
                send_button.click()
            except NoSuchElementException:
                # Try Enter key if send button not found
                input_field.send_keys(Keys.RETURN)
            
            # Wait for response
            response = self.wait_for_response()
            
            return {
                "success": True,
                "query": query,
                "response": response,
                "provider": provider.name
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def wait_for_response(self) -> str:
        """Wait for and extract the AI response"""
        try:
            provider = self.current_provider
            timeout = self.config["automation_settings"]["response_timeout"]
            
            # Wait for typing indicator to disappear (if exists)
            if provider.wait_selector:
                try:
                    WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, provider.wait_selector))
                    )
                    WebDriverWait(self.driver, timeout).until_not(
                        EC.presence_of_element_located((By.CSS_SELECTOR, provider.wait_selector))
                    )
                except TimeoutException:
                    pass
            
            # Wait a bit for response to appear
            time.sleep(2)
            
            # Get the latest response
            response_elements = self.driver.find_elements(By.CSS_SELECTOR, provider.output_selector)
            
            if response_elements:
                # Get the last response (most recent)
                latest_response = response_elements[-1]
                return latest_response.text.strip()
            else:
                return "No response received"
                
        except Exception as e:
            return f"Error getting response: {str(e)}"
    
    def test_provider(self, provider_name: str) -> Dict[str, Any]:
        """Test a provider with a simple query"""
        connect_result = self.connect_to_provider(provider_name)
        if not connect_result["success"]:
            return connect_result
        
        # Send test query
        test_query = "Hello! Please respond with just 'AI connection successful' to confirm you're working."
        response_result = self.send_query(test_query)
        
        if response_result["success"]:
            return {
                "success": True,
                "provider": provider_name,
                "test_response": response_result["response"][:100] + "..." if len(response_result["response"]) > 100 else response_result["response"]
            }
        else:
            return response_result
    
    def close_session(self):
        """Close the browser session"""
        if self.driver:
            self.driver.quit()
            self.driver = None
        self.session_active = False
        self.current_provider = None
    
    def get_available_providers(self) -> List[str]:
        """Get list of available providers"""
        return list(self.providers.keys())

# Global instance
web_ai_automator = WebAIAutomator()

def get_web_ai_automator() -> WebAIAutomator:
    """Get the global web AI automator instance"""
    return web_ai_automator

if __name__ == "__main__":
    # Test the web AI automator
    automator = get_web_ai_automator()
    
    print("🌐 Web AI Automator Test")
    print("Available providers:", automator.get_available_providers())
    
    # Test DeepSeek (no login required)
    print("\n🧪 Testing DeepSeek...")
    result = automator.test_provider("deepseek")
    print(f"Result: {result}")
    
    automator.close_session()
