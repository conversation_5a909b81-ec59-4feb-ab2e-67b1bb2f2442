<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ComponentAI Admin{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-cpu"></i> ComponentAI
            </a>
            
            <div class="navbar-nav">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="bi bi-house"></i> Dashboard
                </a>
                <a class="nav-link" href="{{ url_for('components_list') }}">
                    <i class="bi bi-collection"></i> Components
                </a>
                <a class="nav-link" href="{{ url_for('add_component') }}">
                    <i class="bi bi-plus-circle"></i> Add Component
                </a>
                <a class="nav-link" href="{{ url_for('ai_research_lab') }}">
                    <i class="bi bi-robot"></i> AI Research Lab
                </a>
                <a class="nav-link" href="{{ url_for('analytics') }}">
                    <i class="bi bi-graph-up"></i> Analytics
                </a>
                <a class="nav-link" href="{{ url_for('system_info') }}">
                    <i class="bi bi-info-circle"></i> System Info
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <h1>{% block page_title %}ComponentAI Admin{% endblock %}</h1>
        
        {% block content %}
        <p>Welcome to ComponentAI Admin Interface</p>
        {% endblock %}
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
