#!/usr/bin/env python3
"""
AI Features Demo
Demonstrates the key AI capabilities without GUI
"""

import json
from ai_analyzer import get_ai_analyzer
from datasheet_manager import get_datasheet_manager
from component_intelligence import analyze_component_search

def demo_component_intelligence():
    """Demo the component intelligence system"""
    print("🧠 Component Intelligence Demo")
    print("=" * 40)
    
    test_components = [
        "arduino uno",
        "10k resistor", 
        "LM358 op amp",
        "ESP32 development board",
        "100uF electrolytic capacitor"
    ]
    
    for component in test_components:
        print(f"\n🔍 Analyzing: '{component}'")
        analysis = analyze_component_search(component)
        
        print(f"  📋 Component Type: {analysis['component_type'].replace('_', ' ').title()}")
        print(f"  📦 Relevant Packages: {', '.join(analysis['relevant_packages'][:4])}")
        print(f"  ❓ Clarifying Questions: {len(analysis['questions'])}")
        
        if analysis['questions']:
            for i, q in enumerate(analysis['questions'][:2], 1):
                print(f"     {i}. {q['question']}")
        
        if analysis['refinements']:
            print(f"  🎯 Suggested Refinements: {', '.join(analysis['refinements'][:3])}")
        
        print(f"  🔧 Search Strategy: {analysis['search_strategy'].get('categories', ['general'])}")

def demo_manufacturer_recommendations():
    """Demo manufacturer recommendation system"""
    print("\n\n🏭 Manufacturer Recommendations Demo")
    print("=" * 45)
    
    manager = get_datasheet_manager()
    
    component_types = ["resistor", "capacitor", "microcontroller", "arduino"]
    
    for comp_type in component_types:
        print(f"\n📋 Top Manufacturers for {comp_type.title()}:")
        manufacturers = manager.get_top_manufacturers(comp_type, "")
        
        for i, mfg in enumerate(manufacturers, 1):
            reputation_icon = "🌟" if mfg["reputation"] == "Excellent" else "⭐"
            availability_icon = "🟢" if mfg["availability"] == "High" else "🟡"
            print(f"  {i}. {mfg['name']} {reputation_icon} {availability_icon}")
            print(f"     Reputation: {mfg['reputation']}, Availability: {mfg['availability']}")

def demo_datasheet_search():
    """Demo datasheet search capabilities"""
    print("\n\n📄 Datasheet Search Demo")
    print("=" * 30)
    
    manager = get_datasheet_manager()
    
    test_components = [
        ("LM358", "Texas Instruments"),
        ("Arduino Uno", "Arduino"),
        ("ESP32", "Espressif")
    ]
    
    for component, manufacturer in test_components:
        print(f"\n🔍 Searching datasheets for: {component} ({manufacturer})")
        urls = manager.search_datasheet_urls(component, manufacturer)
        
        print(f"  Found {len(urls)} potential sources:")
        for i, url in enumerate(urls[:4], 1):
            print(f"    {i}. {url}")

def demo_ai_analyzer():
    """Demo AI analyzer capabilities"""
    print("\n\n🤖 AI Analyzer Demo")
    print("=" * 25)
    
    analyzer = get_ai_analyzer()
    
    print(f"📊 Configuration:")
    print(f"  Active Provider: {analyzer.active_provider or 'None'}")
    print(f"  Available Providers: {list(analyzer.providers.keys())}")
    
    # Test provider connections
    print(f"\n🔗 Provider Status:")
    for provider_name in analyzer.providers:
        result = analyzer.test_provider_connection(provider_name)
        status = "✅ Online" if result["success"] else f"❌ {result['error']}"
        print(f"  {provider_name}: {status}")
    
    # Demo component analysis
    if analyzer.active_provider:
        print(f"\n🔬 Component Analysis Demo:")
        test_query = "arduino uno r3"
        print(f"  Analyzing: '{test_query}'")
        
        try:
            analysis = analyzer.analyze_component_query(test_query)
            print(f"  📋 Type: {analysis.component_type}")
            print(f"  🏭 Manufacturer: {analysis.manufacturer or 'Unknown'}")
            print(f"  📦 Packages: {', '.join(analysis.package_options[:3])}")
            print(f"  📊 Confidence: {analysis.confidence_score:.2f}")
            print(f"  📝 Notes: {analysis.analysis_notes[:100]}...")
            
            if analysis.alternatives:
                print(f"  🔄 Alternatives: {', '.join(analysis.alternatives[:3])}")
                
        except Exception as e:
            print(f"  ❌ Analysis failed: {e}")
    else:
        print(f"\n⚠️ No AI provider available for analysis demo")
        print(f"  Install Ollama and run 'ollama pull qwen2:7b' for local AI")

def demo_search_configuration():
    """Demo search configuration options"""
    print("\n\n⚙️ Search Configuration Demo")
    print("=" * 35)
    
    # Demo AI search config
    ai_config = {
        'search_term': 'arduino uno r3',
        'original_term': 'arduino',
        'component_type': 'arduino_board',
        'package': 'Development Board',
        'quality_threshold': 70,
        'use_ai_validation': True,
        'search_strategy': 'ai_enhanced'
    }
    
    print("🤖 AI-Enhanced Search Configuration:")
    for key, value in ai_config.items():
        print(f"  {key.replace('_', ' ').title()}: {value}")
    
    # Demo smart search config
    smart_config = {
        'search_term': '10k resistor 1/4w',
        'component_type': 'resistor',
        'package': 'Through-hole',
        'quality_threshold': 60,
        'search_strategy': 'intelligent'
    }
    
    print(f"\n🔍 Smart Search Configuration:")
    for key, value in smart_config.items():
        print(f"  {key.replace('_', ' ').title()}: {value}")
    
    # Demo quick search config
    quick_config = {
        'search_term': 'ESP32',
        'component_type': 'general',
        'quality_threshold': 0,
        'search_strategy': 'quick'
    }
    
    print(f"\n⚡ Quick Search Configuration:")
    for key, value in quick_config.items():
        print(f"  {key.replace('_', ' ').title()}: {value}")

def main():
    """Run all demos"""
    print("🚀 AI-Powered Component Search - Feature Demo")
    print("=" * 50)
    print("This demo showcases the AI capabilities without running the full GUI")
    print("=" * 50)
    
    try:
        demo_component_intelligence()
        demo_manufacturer_recommendations()
        demo_datasheet_search()
        demo_ai_analyzer()
        demo_search_configuration()
        
        print("\n" + "=" * 50)
        print("🎉 Demo Complete!")
        print("=" * 50)
        print("\n🚀 To use the full application:")
        print("  1. Run: python component_searcher.py")
        print("  2. Or use: run_ai_component_searcher.bat")
        print("\n🤖 AI Features Available:")
        print("  ✅ Component intelligence and classification")
        print("  ✅ Manufacturer recommendations")
        print("  ✅ Datasheet search and management")
        print("  ✅ Quality scoring and validation")
        print("  ✅ Multiple search modes (AI, Smart, Quick)")
        print("\n📚 For help: Use the Help menu in the application")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        print("🔧 Try running: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
