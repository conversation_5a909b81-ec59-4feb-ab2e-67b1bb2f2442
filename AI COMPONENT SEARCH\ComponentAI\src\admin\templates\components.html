{% extends "base.html" %}

{% block title %}Components - ComponentAI Admin{% endblock %}
{% block page_title %}Components{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h3>Component Database</h3>
    <a href="{{ url_for('add_component') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> Add Component
    </a>
</div>

<div class="table-responsive">
    <table class="table table-striped">
        <thead>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Type</th>
                <th>Description</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for component in components %}
            <tr>
                <td>{{ component.id }}</td>
                <td>{{ component.name }}</td>
                <td>{{ component.type }}</td>
                <td>{{ component.description }}</td>
                <td>
                    <a href="{{ url_for('view_component', component_id=component.id) }}" class="btn btn-sm btn-info">
                        <i class="bi bi-eye"></i> View
                    </a>
                    <a href="{{ url_for('edit_component', component_id=component.id) }}" class="btn btn-sm btn-warning">
                        <i class="bi bi-pencil"></i> Edit
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
