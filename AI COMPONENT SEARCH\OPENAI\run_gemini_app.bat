@echo off
title Gemini AI Component Search v2.0.0

echo ========================================
echo  Gemini AI Component Search v2.0.0
echo ========================================
echo.

echo Activating virtual environment...
call .venv\Scripts\activate

echo.
echo Checking Gemini AI library...
python -c "import google.generativeai; print('✅ Google Generative AI library available')" 2>nul
if errorlevel 1 (
    echo ❌ Google Generative AI library not found in virtual environment
    echo Installing library...
    pip install google-generativeai
    echo.
)

echo.
echo Starting Gemini AI Component Search Application...
echo.
echo QUICK GUIDE:
echo   1. Configure Gemini AI: Tools - AI Configuration
echo   2. Enter your API key: AIzaSyAHl-ePSQ6RqY2wi0IUj17JRcoALefRmlg
echo   3. Search components with AI power!
echo.
echo Starting application...
echo.

python component_searcher.py

echo.
echo Application closed.
pause
