<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KiCad Project Browser</title>
    <link rel="stylesheet" href="{{ url_for('static_files_route', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <header class="header">
            <!-- Main Title and Icon -->
            <div class="header-main-title">
                 <h1>
                    <svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 2L2 8.5V15.5L12 22L22 15.5V8.5L12 2ZM12 4.33L19.13 9.5V14.5L12 19.67L4.87 14.5V9.5L12 4.33Z"></path></svg>
                    KiCad Project Browser
                </h1>
                <!-- =========== ADDED: Credits Line =========== -->
                <div class="header-credits">Built by <PERSON><PERSON> - assisted by <PERSON>!</div>
                <!-- ========================================== -->
            </div>
            <!-- Tool UI Version (stays on the right) -->
            <div id="toolVersionInfo" class="tool-version-info"></div>
        </header>

        <div class="title-text">Projects</div>

        <div class="search-bar-container">
            <svg viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
            <input type="text" id="searchInput" class="search-bar" placeholder="Search by SKU or description...">
        </div>
        
        <div class="controls">
            <span id="projectCount" class="project-count"></span>
            <button id="refreshButton" class="button">
                <svg viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M15.312 11.424a5.5 5.5 0 01-9.204 4.301l-.979.978a.75.75 0 01-1.06-1.06l.978-.979a5.502 5.502 0 017.733-8.483l1.137-1.137a.75.75 0 011.06 1.06l-1.137 1.137a5.485 5.485 0 011.472 4.283zm-2.237-5.708a.75.75 0 011.06 0l1.137 1.137a5.502 5.502 0 01-7.733 8.483l-.978.979a.75.75 0 11-1.06-1.06l.979-.978a5.5 5.5 0 019.204-4.301l1.137-1.137a.75.75 0 010-1.06z" clip-rule="evenodd"></path></svg>
                Refresh Projects
            </button>
        </div>

        <div id="mainDisplayArea">
            <div id="scanInProgressUI" style="display: none;">
                <div class="scan-progress-section">
                    <h4>Scan Status:</h4>
                    <div id="scanStatusOutput" class="scan-output-box">
                        <ul id="scanStatusList"></ul>
                    </div>
                </div>
                <div class="scan-progress-section">
                    <h4>Projects Found (<span id="foundProjectsCount">0</span>):</h4>
                    <div id="scanFoundProjectsOutput" class="scan-output-box">
                        <ul id="scanFoundProjectsList"></ul>
                    </div>
                </div>
                <div class="scan-controls" style="text-align: center; margin-top: 15px;">
                    <button id="acceptProjectsButton" class="button" style="display: none;">Accept Projects & Show Table</button>
                </div>
            </div>

            <table id="projectsTable" style="display: none;">
                <thead>
                    <tr>
                        <th>#</th> 
                        <th>SKU</th>
                        <th>Description</th>
                        <th>Last Modified</th>
                        <th>File Size</th>
                        <th>Version</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>

            <div id="statusMessages" class="status-message-container" style="display: none;">
                 <div id="loadingMessage" class="loading-message" style="display: none;">
                    <div class="spinner"></div>
                    <span>Loading projects...</span>
                </div>
                <div id="errorMessage" class="error-message" style="display: none;"></div>
                <div id="noProjectsMessage" class="no-projects-message" style="display: none;">No projects found. Click 'Refresh Projects' to perform initial scan.</div>
                <div id="noResultsMessage" class="no-results-message" style="display: none;">No projects match your search criteria.</div>
            </div>
        </div>

        <footer class="footer-info">
            <span id="scanInfo"></span>
        </footer>
    </div>
    <script src="{{ url_for('static_files_route', filename='script.js') }}" defer></script>
</body>
</html>