#!/usr/bin/env python3
"""
Intelligent Search Dialog
Asks domain-specific questions before searching
"""

import tkinter as tk
from tkinter import ttk, messagebox
from component_intelligence import analyze_component_search

class IntelligentSearchDialog:
    """Dialog that asks intelligent questions before searching"""
    
    def __init__(self, parent, initial_search_term=""):
        self.parent = parent
        self.result = None
        self.search_term = initial_search_term
        
        # Analyze the search term
        self.analysis = analyze_component_search(initial_search_term)
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("🧠 Intelligent Component Search")
        self.dialog.geometry("700x600")
        self.dialog.configure(bg='#f8f9fa')
        self.dialog.resizable(True, True)
        
        # Center the dialog
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.setup_ui()
        
        # Focus on dialog
        self.dialog.focus_set()
    
    def setup_ui(self):
        """Setup the dialog UI"""
        
        # Header
        header_frame = tk.Frame(self.dialog, bg='#007bff', height=60)
        header_frame.pack(fill='x', padx=5, pady=5)
        header_frame.pack_propagate(False)
        
        header_label = tk.Label(header_frame, 
                               text=f"🧠 Intelligent Search for: '{self.search_term}'",
                               font=('Arial', 14, 'bold'), fg='white', bg='#007bff')
        header_label.pack(expand=True)
        
        # Component type info
        info_frame = tk.Frame(self.dialog, bg='#f8f9fa')
        info_frame.pack(fill='x', padx=10, pady=5)
        
        component_type = self.analysis['component_type'].replace('_', ' ').title()
        tk.Label(info_frame, text=f"📋 Detected Component Type: {component_type}",
                font=('Arial', 11, 'bold'), bg='#f8f9fa', fg='#495057').pack(anchor='w')
        
        # Main content area
        main_frame = tk.Frame(self.dialog, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Create notebook for different sections
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True)
        
        # Questions tab
        questions_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(questions_frame, text="🤔 Clarifying Questions")
        self.setup_questions_tab(questions_frame)
        
        # Package options tab
        package_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(package_frame, text="📦 Package Options")
        self.setup_package_tab(package_frame)
        
        # Search refinements tab
        refinements_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(refinements_frame, text="🎯 Search Suggestions")
        self.setup_refinements_tab(refinements_frame)
        
        # Quality settings tab
        quality_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(quality_frame, text="⚙️ Quality Settings")
        self.setup_quality_tab(quality_frame)
        
        # Buttons
        button_frame = tk.Frame(self.dialog, bg='#f8f9fa')
        button_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(button_frame, text="🔍 Search with Intelligence", 
                 command=self.search_intelligent, bg='#28a745', fg='white',
                 font=('Arial', 11, 'bold'), padx=20).pack(side='left', padx=5)
        
        tk.Button(button_frame, text="🚀 Quick Search (Original)", 
                 command=self.search_original, bg='#007bff', fg='white',
                 font=('Arial', 11)).pack(side='left', padx=5)
        
        tk.Button(button_frame, text="❌ Cancel", 
                 command=self.cancel, bg='#6c757d', fg='white',
                 font=('Arial', 11)).pack(side='right', padx=5)
    
    def setup_questions_tab(self, parent):
        """Setup the questions tab"""
        
        # Scrollable frame
        canvas = tk.Canvas(parent, bg='#ffffff')
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#ffffff')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Add questions
        self.question_vars = {}
        
        if not self.analysis['questions']:
            tk.Label(scrollable_frame, text="✅ No additional questions needed for this component type.",
                    font=('Arial', 11), bg='#ffffff', fg='#28a745').pack(pady=20)
        else:
            for i, question in enumerate(self.analysis['questions']):
                self.add_question(scrollable_frame, i, question)
    
    def add_question(self, parent, index, question):
        """Add a question to the UI"""
        
        question_frame = tk.LabelFrame(parent, text=f"Question {index + 1}",
                                     font=('Arial', 10, 'bold'), bg='#ffffff',
                                     fg='#495057', padx=10, pady=10)
        question_frame.pack(fill='x', padx=10, pady=5)
        
        # Question text
        tk.Label(question_frame, text=question['question'],
                font=('Arial', 11), bg='#ffffff', fg='#212529',
                wraplength=500).pack(anchor='w', pady=(0, 10))
        
        if question['type'] == 'single_choice':
            var = tk.StringVar(value=question['options'][0] if question['options'] else "")
            self.question_vars[index] = var
            
            for option in question['options']:
                tk.Radiobutton(question_frame, text=option, variable=var, value=option,
                             font=('Arial', 10), bg='#ffffff', fg='#495057').pack(anchor='w')
        
        elif question['type'] == 'text_input':
            var = tk.StringVar()
            self.question_vars[index] = var
            
            entry = tk.Entry(question_frame, textvariable=var, font=('Arial', 11),
                           width=40)
            entry.pack(anchor='w', pady=5)
            
            if 'placeholder' in question:
                entry.insert(0, question['placeholder'])
                entry.config(fg='#6c757d')
                
                def on_focus_in(event, entry=entry, placeholder=question['placeholder']):
                    if entry.get() == placeholder:
                        entry.delete(0, tk.END)
                        entry.config(fg='#212529')
                
                def on_focus_out(event, entry=entry, placeholder=question['placeholder']):
                    if not entry.get():
                        entry.insert(0, placeholder)
                        entry.config(fg='#6c757d')
                
                entry.bind('<FocusIn>', on_focus_in)
                entry.bind('<FocusOut>', on_focus_out)
    
    def setup_package_tab(self, parent):
        """Setup the package options tab"""
        
        tk.Label(parent, text="📦 Available Package Types for this Component:",
                font=('Arial', 12, 'bold'), bg='#ffffff', fg='#495057').pack(pady=10)
        
        # Package selection
        self.package_var = tk.StringVar(value="Any")
        
        packages = self.analysis['relevant_packages']
        
        # Create a frame for package options
        package_frame = tk.Frame(parent, bg='#ffffff')
        package_frame.pack(fill='x', padx=20, pady=10)
        
        for i, package in enumerate(packages):
            row = i // 3
            col = i % 3
            
            tk.Radiobutton(package_frame, text=package, variable=self.package_var, 
                         value=package, font=('Arial', 10), bg='#ffffff',
                         fg='#495057').grid(row=row, column=col, sticky='w', padx=10, pady=2)
        
        # Package info
        info_text = self.get_package_info()
        if info_text:
            tk.Label(parent, text=info_text, font=('Arial', 10), bg='#ffffff',
                    fg='#6c757d', wraplength=500, justify='left').pack(pady=10)
    
    def setup_refinements_tab(self, parent):
        """Setup the search refinements tab"""
        
        tk.Label(parent, text="🎯 Suggested Search Terms:",
                font=('Arial', 12, 'bold'), bg='#ffffff', fg='#495057').pack(pady=10)
        
        self.refinement_var = tk.StringVar(value=self.search_term)
        
        # Original search term
        tk.Radiobutton(parent, text=f"Original: '{self.search_term}'",
                      variable=self.refinement_var, value=self.search_term,
                      font=('Arial', 11, 'bold'), bg='#ffffff', fg='#007bff').pack(anchor='w', padx=20)
        
        # Suggested refinements
        for refinement in self.analysis['refinements']:
            tk.Radiobutton(parent, text=f"Suggested: '{refinement}'",
                          variable=self.refinement_var, value=refinement,
                          font=('Arial', 10), bg='#ffffff', fg='#495057').pack(anchor='w', padx=20)
        
        # Custom search term
        tk.Label(parent, text="Or enter custom search term:",
                font=('Arial', 10), bg='#ffffff', fg='#495057').pack(anchor='w', padx=20, pady=(20, 5))
        
        self.custom_search_var = tk.StringVar()
        custom_entry = tk.Entry(parent, textvariable=self.custom_search_var,
                               font=('Arial', 11), width=50)
        custom_entry.pack(anchor='w', padx=20)
        
        def use_custom():
            if self.custom_search_var.get().strip():
                self.refinement_var.set(self.custom_search_var.get().strip())
        
        tk.Button(parent, text="Use Custom Term", command=use_custom,
                 bg='#17a2b8', fg='white', font=('Arial', 10)).pack(anchor='w', padx=20, pady=5)
    
    def setup_quality_tab(self, parent):
        """Setup quality settings tab"""
        
        tk.Label(parent, text="⚙️ Data Quality Settings:",
                font=('Arial', 12, 'bold'), bg='#ffffff', fg='#495057').pack(pady=10)
        
        # Quality threshold
        quality_frame = tk.LabelFrame(parent, text="Quality Threshold", bg='#ffffff')
        quality_frame.pack(fill='x', padx=20, pady=10)
        
        self.quality_threshold = tk.IntVar(value=60)
        
        tk.Label(quality_frame, text="Minimum quality score to show results:",
                font=('Arial', 10), bg='#ffffff').pack(anchor='w', padx=10, pady=5)
        
        quality_scale = tk.Scale(quality_frame, from_=0, to=100, orient='horizontal',
                               variable=self.quality_threshold, length=400)
        quality_scale.pack(padx=10, pady=5)
        
        # Quality labels
        labels_frame = tk.Frame(quality_frame, bg='#ffffff')
        labels_frame.pack(fill='x', padx=10)
        
        tk.Label(labels_frame, text="0 (Show all)", font=('Arial', 8),
                bg='#ffffff', fg='#dc3545').pack(side='left')
        tk.Label(labels_frame, text="60 (Sourcing grade)", font=('Arial', 8),
                bg='#ffffff', fg='#ffc107').pack()
        tk.Label(labels_frame, text="100 (Perfect)", font=('Arial', 8),
                bg='#ffffff', fg='#28a745').pack(side='right')
        
        # Show rejected results option
        self.show_rejected = tk.BooleanVar(value=True)
        tk.Checkbutton(parent, text="📋 Show rejected results in a separate section",
                      variable=self.show_rejected, font=('Arial', 10),
                      bg='#ffffff', fg='#495057').pack(anchor='w', padx=20, pady=10)
        
        # Export quality report
        self.export_quality = tk.BooleanVar(value=True)
        tk.Checkbutton(parent, text="📊 Include quality report in export",
                      variable=self.export_quality, font=('Arial', 10),
                      bg='#ffffff', fg='#495057').pack(anchor='w', padx=20)
    
    def get_package_info(self):
        """Get package information for the component type"""
        component_type = self.analysis['component_type']
        
        info_map = {
            'arduino_board': "Arduino boards typically come as complete development boards. Package selection doesn't apply.",
            'resistor': "Through-hole for breadboards/prototyping. SMD (0603, 0805, 1206) for PCB assembly.",
            'capacitor': "Through-hole for easy prototyping. SMD for compact designs. Radial/Axial for specific orientations.",
            'microcontroller': "DIP for prototyping. QFP/BGA for production. Development boards for easy use.",
            'transistor': "TO-92 for low power. TO-220 for higher power. SOT-23 for SMD applications."
        }
        
        return info_map.get(component_type, "Package selection depends on your specific application requirements.")
    
    def search_intelligent(self):
        """Perform intelligent search with all settings"""
        
        # Collect all answers
        answers = {}
        for index, var in self.question_vars.items():
            value = var.get()
            if value and not any(placeholder in value for placeholder in ['e.g.,', 'placeholder']):
                answers[index] = value
        
        # Build refined search term
        refined_search = self.refinement_var.get() or self.search_term
        
        # If custom search term is provided, use it
        if self.custom_search_var.get().strip():
            refined_search = self.custom_search_var.get().strip()
        
        self.result = {
            'search_term': refined_search,
            'original_term': self.search_term,
            'component_type': self.analysis['component_type'],
            'package': self.package_var.get(),
            'answers': answers,
            'quality_threshold': self.quality_threshold.get(),
            'show_rejected': self.show_rejected.get(),
            'export_quality': self.export_quality.get(),
            'search_strategy': self.analysis['search_strategy']
        }
        
        self.dialog.destroy()
    
    def search_original(self):
        """Perform original search without intelligence"""
        self.result = {
            'search_term': self.search_term,
            'original_term': self.search_term,
            'component_type': 'general',
            'package': 'Any',
            'answers': {},
            'quality_threshold': 0,  # Show all results
            'show_rejected': True,
            'export_quality': False,
            'search_strategy': {}
        }
        
        self.dialog.destroy()
    
    def cancel(self):
        """Cancel the dialog"""
        self.result = None
        self.dialog.destroy()
    
    def get_result(self):
        """Get the dialog result"""
        self.dialog.wait_window()
        return self.result

def show_intelligent_search_dialog(parent, search_term):
    """Show the intelligent search dialog"""
    dialog = IntelligentSearchDialog(parent, search_term)
    return dialog.get_result()

if __name__ == "__main__":
    # Test the dialog
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    result = show_intelligent_search_dialog(root, "arduino")
    
    if result:
        print("Search Result:")
        print(f"  Search Term: {result['search_term']}")
        print(f"  Component Type: {result['component_type']}")
        print(f"  Package: {result['package']}")
        print(f"  Quality Threshold: {result['quality_threshold']}")
        print(f"  Answers: {result['answers']}")
    else:
        print("Search cancelled")
    
    root.destroy()
