@echo off
echo ========================================
echo AI-Powered Component Search Installation
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

echo Python found. Checking version...
python -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python 3.8 or higher is required
    echo Please upgrade your Python installation
    pause
    exit /b 1
)

echo.
echo Installing core dependencies...
pip install requests beautifulsoup4 lxml urllib3

echo.
echo Installing GUI dependencies...
pip install tkinterdnd2

echo.
echo Installing web automation for AI chatbots...
pip install selenium undetected-chromedriver html5lib chardet

echo.
echo Installing data processing dependencies...
pip install pandas numpy

echo.
echo Installing PDF processing dependencies...
pip install PyPDF2 pdfplumber

echo.
echo Installing AI/LLM dependencies...
echo Note: Web-based AI providers are now the default (no API keys needed!)
pip install openai anthropic langchain langchain-openai

echo.
echo Installing optional dependencies...
pip install undetected-chromedriver Pillow matplotlib pyyaml http-cookiejar

echo.
echo Creating application directories...
if not exist "datasheets" mkdir datasheets
if not exist "datasheets\downloaded" mkdir datasheets\downloaded
if not exist "datasheets\analyzed" mkdir datasheets\analyzed
if not exist "exports" mkdir exports
if not exist "cache" mkdir cache
if not exist "logs" mkdir logs

echo.
echo Creating default configuration files...

echo Creating AI configuration with web-based providers...
echo {> ai_config.json
echo   "default_provider": "deepseek_web",>> ai_config.json
echo   "providers": {>> ai_config.json
echo     "deepseek_web": {>> ai_config.json
echo       "enabled": true,>> ai_config.json
echo       "name": "DeepSeek Chat (Free)">> ai_config.json
echo     },>> ai_config.json
echo     "perplexity": {>> ai_config.json
echo       "enabled": true,>> ai_config.json
echo       "name": "Perplexity AI (Free)">> ai_config.json
echo     },>> ai_config.json
echo     "you_chat": {>> ai_config.json
echo       "enabled": true,>> ai_config.json
echo       "name": "You.com Chat (Free)">> ai_config.json
echo     },>> ai_config.json
echo     "chatgpt_free": {>> ai_config.json
echo       "enabled": false,>> ai_config.json
echo       "name": "ChatGPT Free",>> ai_config.json
echo       "email": "",>> ai_config.json
echo       "password": "">> ai_config.json
echo     },>> ai_config.json
echo     "claude_free": {>> ai_config.json
echo       "enabled": false,>> ai_config.json
echo       "name": "Claude Free",>> ai_config.json
echo       "email": "",>> ai_config.json
echo       "password": "">> ai_config.json
echo     }>> ai_config.json
echo   },>> ai_config.json
echo   "web_automation": {>> ai_config.json
echo     "headless": false,>> ai_config.json
echo     "timeout": 30>> ai_config.json
echo   }>> ai_config.json
echo }>> ai_config.json

echo.
echo Creating run script...
echo @echo off> run_ai_component_searcher.bat
echo echo Starting AI-Powered Component Search Application...>> run_ai_component_searcher.bat
echo python component_searcher.py>> run_ai_component_searcher.bat
echo pause>> run_ai_component_searcher.bat

echo.
echo ========================================
echo Installation Complete!
echo ========================================
echo.
echo NEXT STEPS:
echo.
echo 1. AI SETUP (Ready to Use!):
echo    - Web-based AI providers are pre-configured
echo    - DeepSeek, Perplexity, You.com work immediately
echo    - For ChatGPT/Claude: Use Tools menu to configure login
echo.
echo 2. BROWSER SETUP (For web scraping):
echo    - Install Chrome browser
echo    - ChromeDriver will be managed automatically
echo.
echo 3. RUN THE APPLICATION:
echo    - Double-click: run_ai_component_searcher.bat
echo    - Or run: python component_searcher.py
echo.
echo 4. FEATURES AVAILABLE:
echo    - 🤖 AI-powered component analysis
echo    - 📄 Datasheet downloading and analysis
echo    - 🔍 Intelligent search with domain knowledge
echo    - 🇮🇳 Indian supplier prioritization
echo    - 📊 Quality scoring and validation
echo.
echo For help and documentation, use the Help menu in the application.
echo.
pause
