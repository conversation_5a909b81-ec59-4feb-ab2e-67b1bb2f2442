import threading
import serial
import serial.tools.list_ports
import time

class SerialThread(threading.Thread):
    def __init__(self, app_instance):
        """
        Initializes the serial communication thread.
        :param app_instance: The main application instance for UI updates.
        """
        super().__init__()
        self.daemon = True
        self.app = app_instance
        self.stop_event = app_instance.stop_event
        self.ser = None
        self.port = None

    def find_pico_port(self):
        """Automatically find the COM port for the Raspberry Pi Pico."""
        ports = serial.tools.list_ports.comports()
        for port in ports:
            # The Pico typically shows up with this VID:PID
            if port.vid == 0x2E8A and port.pid == 0x000A:
                print(f"Found Raspberry Pi Pico at {port.device}")
                return port.device
        print("Warning: Could not automatically find a Raspberry Pi Pico.")
        return None

    def run(self):
        """Main loop for the serial thread."""
        print("Serial thread started.")
        self.port = self.find_pico_port()

        if not self.port:
            self.app.after(0, self.update_status, "Pico Not Found", "red")
            return

        try:
            self.ser = serial.Serial(self.port, 115200, timeout=1)
            self.app.after(0, self.update_status, f"Connected to {self.port}", "green")
        except serial.SerialException as e:
            print(f"Error: Could not open serial port {self.port}: {e}")
            self.app.after(0, self.update_status, "Connection Failed", "red")
            return

        while not self.stop_event.is_set():
            # This loop can be used to read data from the Pico if needed in the future.
            # For now, it just keeps the thread alive.
            if self.ser and self.ser.in_waiting > 0:
                try:
                    line = self.ser.readline().decode('utf-8').strip()
                    if line:
                        print(f"Received from Pico: {line}")
                        # We can add UI updates here if the Pico sends status messages
                except serial.SerialException:
                    print("Error: Serial port disconnected.")
                    self.app.after(0, self.update_status, "Disconnected", "red")
                    break
            time.sleep(0.1)

        if self.ser and self.ser.is_open:
            self.ser.close()
            print("Serial port closed.")
        
        print("Serial thread stopped.")
        self.app.after(0, self.update_status, "Disconnected", "red")

    def send_command(self, command):
        """Sends a command to the serial port."""
        if self.ser and self.ser.is_open:
            try:
                full_command = command + '\n'
                self.ser.write(full_command.encode('utf-8'))
                print(f"Sent command: {command}")
            except serial.SerialException as e:
                print(f"Error sending command: {e}")
                self.app.after(0, self.update_status, "Disconnected", "red")
        else:
            print("Cannot send command: Serial port is not connected.")

    def update_status(self, status_text, color):
        """Updates the display status label in the main UI."""
        self.app.display_status_label.configure(text=status_text, text_color=color)