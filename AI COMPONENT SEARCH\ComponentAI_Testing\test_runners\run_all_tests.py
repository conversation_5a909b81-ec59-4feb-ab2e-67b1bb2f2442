#!/usr/bin/env python3
"""
COMPREHENSIVE TEST RUNNER
Runs all robot tests from the TESTING directory
SEPARATE from product code
"""

import sys
import os
import subprocess
import time
import requests

def check_product_server(product_url="http://localhost:8080"):
    """Check if ComponentAI product server is running"""
    print("🔍 Checking ComponentAI product server...")
    
    try:
        response = requests.get(product_url, timeout=5)
        print(f"✅ Product server running at {product_url}")
        return True
    except:
        print(f"❌ Product server NOT running at {product_url}")
        return False

def check_prerequisites():
    """Check testing prerequisites"""
    print("🔍 Checking testing prerequisites...")
    
    # Check selenium
    try:
        import selenium
        print("✅ Selenium is available")
        selenium_ok = True
    except ImportError:
        print("❌ Selenium is NOT installed")
        selenium_ok = False
    
    # Check Chrome
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        options = Options()
        options.add_argument("--headless")
        driver = webdriver.Chrome(options=options)
        driver.quit()
        print("✅ Chrome/ChromeDriver is available")
        chrome_ok = True
    except:
        print("❌ Chrome/ChromeDriver is NOT available")
        chrome_ok = False
    
    return selenium_ok, chrome_ok

def install_selenium():
    """Install selenium if needed"""
    print("📦 Installing Selenium...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "selenium"])
        print("✅ Selenium installed successfully")
        return True
    except:
        print("❌ Failed to install Selenium")
        return False

def run_real_data_robot_test():
    """Run the real data robot test"""
    print("\n🔬 RUNNING REAL DATA ROBOT TEST")
    print("=" * 50)
    
    try:
        # Get the path to the robot tester
        current_dir = os.path.dirname(os.path.abspath(__file__))
        robot_tester_path = os.path.join(current_dir, "..", "robot_testers", "real_data_robot_tester.py")
        
        # Run the robot tester with UTF-8 encoding
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        result = subprocess.run([sys.executable, robot_tester_path],
                              capture_output=True, text=True, timeout=600,
                              encoding='utf-8', env=env)
        
        print("📊 REAL DATA ROBOT TEST RESULTS:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️ Warnings/Errors:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Real data robot test timed out (10 minutes)")
        return False
    except Exception as e:
        print(f"❌ Failed to run real data robot test: {e}")
        return False

def run_comprehensive_robot_test():
    """Run the comprehensive robot test"""
    print("\n🤖 RUNNING COMPREHENSIVE ROBOT TEST")
    print("=" * 50)
    
    try:
        # Get the path to the comprehensive tester
        current_dir = os.path.dirname(os.path.abspath(__file__))
        comprehensive_tester_path = os.path.join(current_dir, "..", "robot_testers", "simple_comprehensive_robot_test.py")
        
        # Check if file exists, if not create a simple version
        if not os.path.exists(comprehensive_tester_path):
            print("⚠️ Comprehensive robot test not found, skipping...")
            return True
        
        # Run the comprehensive tester
        result = subprocess.run([sys.executable, comprehensive_tester_path], 
                              capture_output=True, text=True, timeout=600)
        
        print("📊 COMPREHENSIVE ROBOT TEST RESULTS:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️ Warnings/Errors:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Comprehensive robot test timed out (10 minutes)")
        return False
    except Exception as e:
        print(f"❌ Failed to run comprehensive robot test: {e}")
        return False

def generate_test_report(results):
    """Generate comprehensive test report"""
    print("\n📋 COMPREHENSIVE TEST REPORT")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r['success'])
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    print(f"📊 Total Test Suites: {total_tests}")
    print(f"✅ Passed: {passed_tests}")
    print(f"❌ Failed: {total_tests - passed_tests}")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    
    print(f"\n📋 Detailed Results:")
    for result in results:
        status = "✅ PASSED" if result['success'] else "❌ FAILED"
        print(f"   {result['test_name']}: {status}")
    
    if success_rate >= 100:
        print("\n🎉 ALL TESTS PASSED! ComponentAI is working perfectly!")
    elif success_rate >= 80:
        print("\n✅ MOST TESTS PASSED! ComponentAI is working well!")
    else:
        print("\n🔧 SOME TESTS FAILED! ComponentAI needs attention!")
    
    return success_rate >= 80

def main():
    """Main test runner"""
    print("🎯 COMPONENTAI COMPREHENSIVE TEST RUNNER")
    print("=" * 60)
    print("🧪 TESTING DIRECTORY VERSION")
    print("📁 Separate from product code")
    print("🤖 Comprehensive robot testing")
    print("=" * 60)
    
    # Step 1: Check product server
    if not check_product_server():
        print("\n❌ CANNOT PROCEED: Product server not running")
        print("\nTo start the product server:")
        print("1. Open another terminal")
        print("2. cd ComponentAI")
        print("3. python launch_admin.py")
        print("4. Then run this test again")
        return False
    
    # Step 2: Check prerequisites
    selenium_ok, chrome_ok = check_prerequisites()
    
    if not selenium_ok:
        if not install_selenium():
            print("❌ Cannot proceed without Selenium")
            return False
    
    if not chrome_ok:
        print("❌ Chrome/ChromeDriver is required")
        print("Please install Chrome and ChromeDriver")
        return False
    
    # Step 3: Run all tests
    test_results = []
    
    print(f"\n🚀 STARTING COMPREHENSIVE TESTING")
    print(f"⏰ Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test 1: Real Data Robot Test
    print("\n" + "="*60)
    real_data_success = run_real_data_robot_test()
    test_results.append({
        'test_name': 'Real Data Robot Test',
        'success': real_data_success
    })
    
    # Test 2: Comprehensive Robot Test
    print("\n" + "="*60)
    comprehensive_success = run_comprehensive_robot_test()
    test_results.append({
        'test_name': 'Comprehensive Robot Test',
        'success': comprehensive_success
    })
    
    # Step 4: Generate final report
    overall_success = generate_test_report(test_results)
    
    print(f"\n⏰ Completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    
    print(f"\n🎯 FINAL RESULT: {'SUCCESS' if success else 'FAILURE'}")
    
    input("\nPress Enter to exit...")
    exit(0 if success else 1)
