#!/usr/bin/env python3
"""
AI-Powered Electronics Component Search & Analysis Application
Advanced component search with AI-driven datasheet analysis and intelligent recommendations.
Searches Indian suppliers first, analyzes datasheets, and provides comprehensive component data.
Created for professional electronics development with AI assistance.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import json
import csv
from datetime import datetime
import webbrowser
import os
import sys
from pathlib import Path
import requests
import urllib.parse
from scrapers import get_scraper
from data_quality_validator import validate_search_results
from enhanced_quality_validator import validate_search_results_enhanced
from validation_feedback_system import feedback_logger
from enhanced_ai_ui import GeminiResultsPanel, EnhancedResultsDisplay, DatasheetViewer
from intelligent_search_dialog import show_intelligent_search_dialog
from ai_search_dialog import show_ai_search_dialog
from gemini_search_dialog import show_gemini_search_dialog
from gemini_ai_analyzer import get_gemini_analyzer
from datasheet_manager import get_datasheet_manager

# Version Management
APP_VERSION = "2.6.0"
UI_VERSION = "2.6.0"
BACKEND_VERSION = "2.0.0"
BUILD_DATE = "2025-01-27"
LAST_UPDATED = "2025-01-27"

# Changelog v2.2.1:
# - Enhanced Gemini test conversation to show meaningful dialogue
# - Added clear question/response format in diagnostic terminal
# - Shows what we ask Gemini and what it responds with
# - Improved user understanding of AI communication process
# - Better handling of different response types (objects vs text)

# Changelog v2.2.0:
# - Added real-time diagnostic terminal for Gemini testing
# - Replaced popup dialogs with integrated terminal window
# - Real-time step-by-step testing progress display
# - Professional terminal-style interface with colored output
# - Closeable diagnostic window positioned on right side
# - Enhanced user experience with live testing feedback

# Changelog v2.1.1:
# - Fixed Gemini test button positioning (moved to Tools section on right)
# - Enhanced Gemini connectivity test with better error handling
# - Improved test method compatibility with different analyzer versions
# - Better visual separation between primary search and testing tools

# Changelog v2.1.0:
# - Changed "Component Value" to "Component Name or Value"
# - Added placeholder for quantity field
# - Removed Package and Manufacturer input boxes
# - Added "Test Gemini Connectivity" button with status display
# - Removed Smart Search and Quick Search buttons (kept only Gemini AI Search)
# - Improved UI layout and user experience

class ComponentSearcher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title(f"AI-Powered Component Search & Analysis v{APP_VERSION}")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')

        # Create application directories
        self.create_app_directories()

        # Load suppliers configuration
        self.suppliers = self.load_suppliers()
        self.search_results = []
        self.progress_window = None
        self.current_search_thread = None

        # Initialize AI and datasheet management
        try:
            self.gemini_analyzer = get_gemini_analyzer()
            self.datasheet_manager = get_datasheet_manager()
            self.ai_enabled = self.gemini_analyzer.is_available()
            if self.ai_enabled:
                print("✅ Gemini AI enabled and ready")
            else:
                print("⚠️ Gemini AI not available - check API key")
        except Exception as e:
            print(f"Warning: AI features disabled due to error: {e}")
            self.gemini_analyzer = None
            self.datasheet_manager = None
            self.ai_enabled = False

        # Initialize datasheet directory
        self.datasheet_dir = os.path.join(os.getcwd(), "Datasheets")
        self.ensure_datasheet_directory()

        # Initialize query management system
        self.custom_queries = []
        self.query_results = {}
        self.load_default_queries()

        self.setup_menu()
        self.setup_ui()

    def ensure_datasheet_directory(self):
        """Create Datasheets directory if it doesn't exist"""
        if not os.path.exists(self.datasheet_dir):
            os.makedirs(self.datasheet_dir)
            print(f"✅ Created Datasheets directory: {self.datasheet_dir}")
        else:
            print(f"✅ Datasheets directory exists: {self.datasheet_dir}")

    def create_app_directories(self):
        """Create necessary application directories"""
        directories = [
            'datasheets',
            'datasheets/downloaded',
            'datasheets/analyzed',
            'exports',
            'cache',
            'logs'
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)

    def setup_menu(self):
        """Setup application menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Export Results...", command=self.export_results)
        file_menu.add_command(label="Export Quality Report...", command=self.export_quality_report)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)

        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Manage Suppliers...", command=self.manage_suppliers)
        tools_menu.add_command(label="AI Configuration...", command=self.configure_ai)
        tools_menu.add_command(label="Datasheet Manager...", command=self.open_datasheet_manager)
        tools_menu.add_separator()
        tools_menu.add_command(label="Report Validation Issue...", command=self.report_validation_issue)
        tools_menu.add_command(label="View Feedback Summary", command=self.view_feedback_summary)
        tools_menu.add_separator()
        tools_menu.add_command(label="Clear Cache", command=self.clear_cache)

        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Show Progress Window", command=self.show_progress_window_manual)
        view_menu.add_command(label="Component Analysis", command=self.show_component_analysis)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="User Guide", command=self.show_help)
        help_menu.add_command(label="AI Features Guide", command=self.show_ai_help)
        help_menu.add_command(label="Check for Updates", command=self.check_updates)
        help_menu.add_separator()
        help_menu.add_command(label="About", command=self.show_about)

    def load_suppliers(self):
        """Load supplier configurations from JSON file"""
        suppliers_file = "suppliers.json"
        if os.path.exists(suppliers_file):
            with open(suppliers_file, 'r') as f:
                return json.load(f)
        else:
            # Default suppliers list - will be expanded
            return {
                "indian_tier1": [
                    {"name": "Robu", "url": "https://robu.in", "location": "Pune, Maharashtra", "active": True},
                    {"name": "Evelta", "url": "https://www.evelta.com", "location": "Mumbai, Maharashtra", "active": True},
                    {"name": "ElectronicsComp", "url": "https://www.electronicscomp.com", "location": "Bangalore, Karnataka", "active": True},
                    {"name": "Sunrom", "url": "https://www.sunrom.com", "location": "Ahmedabad, Gujarat", "active": True},
                    {"name": "Probots", "url": "https://www.probots.co.in", "location": "Bangalore, Karnataka", "active": True},
                    {"name": "Rhydolabz", "url": "https://www.rhydolabz.com", "location": "Cochin, Kerala", "active": True},
                    {"name": "FabtoLab", "url": "https://www.fabtolab.com", "location": "Bangalore, Karnataka", "active": True}
                ],
                "indian_tier2": [
                    {"name": "CrazyPi", "url": "https://www.crazypi.com", "location": "Bangalore, Karnataka", "active": True},
                    {"name": "RoboCraze", "url": "https://robocraze.com", "location": "Bangalore, Karnataka", "active": True},
                    {"name": "Robokits", "url": "https://robokits.co.in", "location": "Gandhinagar, Gujarat", "active": True},
                    {"name": "Ktron", "url": "https://www.ktron.in", "location": "Surendranagar, Gujarat", "active": True},
                    {"name": "Module143", "url": "https://www.module143.com", "location": "Bangalore, Karnataka", "active": True},
                    {"name": "ThinkRobotics", "url": "https://thinkrobotics.in", "location": "New Delhi", "active": True},
                    {"name": "FlyRobo", "url": "https://www.flyrobo.in", "location": "Ahmedabad, Gujarat", "active": True},
                    {"name": "Quartz Components", "url": "https://quartzcomponents.com", "location": "Jaipur, Rajasthan", "active": True},
                    {"name": "DNA Tech India", "url": "https://www.dnatechindia.com", "location": "Nashik, Maharashtra", "active": True},
                    {"name": "RoboElements", "url": "https://www.roboelements.com", "location": "Jalandhar, Punjab", "active": True},
                    {"name": "Potential Labs", "url": "https://potentiallabs.com", "location": "Kondapur, Telangana", "active": True},
                    {"name": "FactoryForward", "url": "https://www.factoryforward.com", "location": "Hosur, Tamil Nadu", "active": True},
                    {"name": "Silver Electronics", "url": "https://www.silverlineelectronics.in", "location": "Bangalore, Karnataka", "active": True},
                    {"name": "TomSon Electronics", "url": "https://www.tomsonelectronics.com", "location": "Kochi, Kerala", "active": True},
                    {"name": "Thingbits", "url": "https://www.thingbits.in", "location": "Mumbai, Maharashtra", "active": True},
                    {"name": "Ventor Technologies", "url": "https://www.ventor.co.in", "location": "Hooghly, West Bengal", "active": True},
                    {"name": "Rare Components", "url": "https://rarecomponents.com", "location": "Jabalpur, MP", "active": True}
                ],
                "indian_additional": [
                    {"name": "Hacktronics", "url": "https://hacktronics.co.in", "location": "India", "active": True},
                    {"name": "Tanotis", "url": "https://www.tanotis.com", "location": "India", "active": True},
                    {"name": "ElementZOnline", "url": "https://www.elementzonline.com", "location": "India", "active": True},
                    {"name": "EasyElectronics", "url": "https://www.easyelectronics.in", "location": "India", "active": True},
                    {"name": "RobotBanao", "url": "https://www.robotbanao.com", "location": "India", "active": True},
                    {"name": "HNH Cart", "url": "https://hnhcart.com", "location": "India", "active": True},
                    {"name": "InkOcean", "url": "https://inkocean.in", "location": "India", "active": True},
                    {"name": "OnlineTPS", "url": "https://onlinetps.com", "location": "India", "active": True},
                    {"name": "SharviElectronics", "url": "https://sharvielectronics.com", "location": "India", "active": True}
                ],
                "international": [
                    {"name": "Digikey India", "url": "https://www.digikey.in", "location": "International", "active": False},
                    {"name": "Mouser India", "url": "https://www.mouser.in", "location": "International", "active": False},
                    {"name": "Element14 India", "url": "https://in.element14.com", "location": "International", "active": False},
                    {"name": "RS Components India", "url": "https://in.rs-online.com", "location": "International", "active": False},
                    {"name": "LCSC", "url": "https://www.lcsc.com", "location": "International", "active": False}
                ]
            }
    
    def save_suppliers(self):
        """Save suppliers configuration to JSON file"""
        with open("suppliers.json", 'w') as f:
            json.dump(self.suppliers, f, indent=2)
    
    def setup_ui(self):
        """Setup the user interface with workflow design"""
        # Initialize workflow state
        self.current_step = 1

        # Title with version info
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', padx=5, pady=5)
        title_frame.pack_propagate(False)

        # Main title
        title_label = tk.Label(title_frame, text="🤖 AI-Powered Component Search & Analysis",
                              font=('Arial', 16, 'bold'), fg='white', bg='#2c3e50')
        title_label.pack(expand=True)

        # Version info
        version_label = tk.Label(title_frame, text=f"v{APP_VERSION} | Enhanced Workflow UI",
                                font=('Arial', 8), fg='#95a5a6', bg='#2c3e50')
        version_label.pack(side='right', padx=10)

        # Add workflow buttons
        self.setup_workflow_buttons()

        # Main container
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Store main frame for workflow navigation
        self.main_frame = main_frame
        
        # Search frame
        search_frame = tk.LabelFrame(main_frame, text="Component Search", font=('Arial', 12, 'bold'),
                                   bg='#f0f0f0', fg='#2c3e50')
        search_frame.pack(fill='x', pady=(0, 10))

        # Component input
        tk.Label(search_frame, text="Component Name or Value:", font=('Arial', 10), bg='#f0f0f0').grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.component_entry = tk.Entry(search_frame, font=('Arial', 10), width=30)
        self.component_entry.grid(row=0, column=1, padx=5, pady=5)
        self.component_entry.insert(0, "10kΩ resistor")  # Example

        # Bind component entry to update query preview dynamically
        self.component_entry.bind('<KeyRelease>', self.on_component_change)
        self.component_entry.bind('<FocusOut>', self.on_component_change)

        # Quantity
        tk.Label(search_frame, text="Quantity:", font=('Arial', 10), bg='#f0f0f0').grid(row=0, column=2, sticky='w', padx=5, pady=5)
        self.quantity_entry = tk.Entry(search_frame, font=('Arial', 10), width=10)
        self.quantity_entry.grid(row=0, column=3, padx=5, pady=5, sticky='w')
        self.quantity_entry.insert(0, "1")

        # Add placeholder text for quantity
        def add_placeholder(entry, placeholder_text):
            def on_focus_in(event):
                if entry.get() == placeholder_text:
                    entry.delete(0, tk.END)
                    entry.config(fg='black')

            def on_focus_out(event):
                if entry.get() == '':
                    entry.insert(0, placeholder_text)
                    entry.config(fg='grey')

            entry.bind('<FocusIn>', on_focus_in)
            entry.bind('<FocusOut>', on_focus_out)
            entry.config(fg='grey')

        # Clear quantity and add placeholder
        self.quantity_entry.delete(0, tk.END)
        add_placeholder(self.quantity_entry, "Enter quantity")

        # AI Query Management Section - Spacious Design (store reference for show/hide)
        self.query_frame = tk.LabelFrame(search_frame, text="🤖 AI Query Configuration", font=('Arial', 12, 'bold'),
                                   bg='#f8f9fa', fg='#2c3e50', relief='solid', bd=1)
        self.query_frame.grid(row=1, column=0, columnspan=4, sticky='ew', padx=10, pady=10)

        # Compact query management buttons
        query_btn_frame = tk.Frame(self.query_frame, bg='#f8f9fa')
        query_btn_frame.pack(fill='x', padx=10, pady=5)

        tk.Button(query_btn_frame, text="📝 Manage", command=self.manage_queries,
                 bg='#17a2b8', fg='white', font=('Arial', 9, 'bold'),
                 width=10, height=1, anchor='center').pack(side='left', padx=3)

        tk.Button(query_btn_frame, text="🔄 Auto-Gen", command=self.auto_generate_queries,
                 bg='#28a745', fg='white', font=('Arial', 9, 'bold'),
                 width=10, height=1, anchor='center').pack(side='left', padx=3)

        tk.Button(query_btn_frame, text="💾 Save", command=self.save_query_template,
                 bg='#6c757d', fg='white', font=('Arial', 9, 'bold'),
                 width=8, height=1, anchor='center').pack(side='left', padx=3)

        tk.Button(query_btn_frame, text="📂 Load", command=self.load_query_template,
                 bg='#17a2b8', fg='white', font=('Arial', 9, 'bold'),
                 width=8, height=1, anchor='center').pack(side='left', padx=3)

        # Resizable query preview area with drag handle
        self.preview_container = tk.Frame(self.query_frame, bg='#f8f9fa', height=80)
        self.preview_container.pack(fill='x', padx=10, pady=(3, 8))
        self.preview_container.pack_propagate(False)  # Maintain fixed height

        # Header with label and resize grip
        preview_header = tk.Frame(self.preview_container, bg='#e9ecef', relief='solid', bd=1)
        preview_header.pack(fill='x')

        preview_label = tk.Label(preview_header, text="📋 Active Queries (drag to resize):",
                               font=('Arial', 9, 'bold'), bg='#e9ecef', fg='#495057')
        preview_label.pack(side='left', padx=8, pady=2)

        # Compact grip handle - two lines with border
        self.resize_grip = tk.Label(preview_header, text="●●●\n●●●", font=('Arial', 8, 'bold'),
                                   bg='#e9ecef', fg='#495057', cursor='sb_v_double_arrow',
                                   relief='solid', bd=1, padx=4, pady=2)
        self.resize_grip.pack(side='right', padx=3, pady=1)

        # Query preview text area
        self.query_preview = tk.Text(self.preview_container, height=3, font=('Arial', 9),
                                   bg='#f8f9fa', fg='#495057', wrap='word',
                                   relief='solid', bd=1, padx=8, pady=4,
                                   cursor='arrow', state='disabled')
        self.query_preview.pack(fill='both', expand=True)

        # Store initial height and setup resize functionality
        self.query_preview_min_height = 60
        self.query_preview_max_height = 200
        self.query_preview_current_height = 80
        self.setup_query_resize()
        self.update_query_preview()

        # Search buttons frame
        button_frame = tk.Frame(search_frame, bg='#f0f0f0')
        button_frame.grid(row=0, column=4, rowspan=3, padx=10, pady=5, sticky='ns')

        # Primary search button
        self.ai_search_btn = tk.Button(button_frame, text="🤖 Gemini AI Search", font=('Arial', 11, 'bold'),
                                     bg='#4285f4', fg='white', command=self.start_gemini_search, anchor='center')
        self.ai_search_btn.pack(fill='x', pady=2)

        # Gemini status indicator (below search button)
        self.gemini_status_frame = tk.Frame(button_frame, bg='#f0f0f0')
        self.gemini_status_frame.pack(fill='x', pady=2)

        self.gemini_status_label = tk.Label(self.gemini_status_frame, text="", font=('Arial', 8),
                                          bg='#f0f0f0', fg='#666666')
        self.gemini_status_label.pack()

        # Tools frame (positioned to the right)
        tools_frame = tk.Frame(search_frame, bg='#f0f0f0')
        tools_frame.grid(row=0, column=5, rowspan=2, padx=5, pady=5, sticky='ns')

        tk.Label(tools_frame, text="Tools:", font=('Arial', 9, 'bold'),
                bg='#f0f0f0', fg='#666666').pack(pady=(0, 2))

        # Test Gemini Connectivity button (as a tool)
        self.test_gemini_btn = tk.Button(tools_frame, text="🔗 Test Gemini", font=('Arial', 9),
                                       bg='#6c757d', fg='white', command=self.test_gemini_connectivity, anchor='center')
        self.test_gemini_btn.pack(fill='x', pady=1)

        # Set initial status as unknown until tested
        self.gemini_status_label.config(text="❓ Untested", fg='#6c757d')

        # Start live status check after UI is ready
        self.root.after(1000, self.update_gemini_status)  # Check after 1 second

        # Create diagnostic terminal window (initially hidden)
        self.setup_diagnostic_terminal()
        
        # Modern progress indicator in top right corner
        self.setup_modern_progress_indicator()
        
        # Results frame (store reference for show/hide)
        self.results_frame = tk.LabelFrame(main_frame, text="Search Results", font=('Arial', 12, 'bold'),
                                    bg='#f0f0f0', fg='#2c3e50')
        self.results_frame.pack(fill='both', expand=True)
        
        # Results treeview
        columns = ('Supplier', 'Component', 'Price', 'Stock', 'Shipping', 'Total', 'Location', 'URL')
        self.results_tree = ttk.Treeview(self.results_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.results_tree.heading('Supplier', text='Supplier')
        self.results_tree.heading('Component', text='Component')
        self.results_tree.heading('Price', text='Price (₹)')
        self.results_tree.heading('Stock', text='Stock')
        self.results_tree.heading('Shipping', text='Shipping (₹)')
        self.results_tree.heading('Total', text='Total (₹)')
        self.results_tree.heading('Location', text='Location')
        self.results_tree.heading('URL', text='URL')
        
        # Column widths
        self.results_tree.column('Supplier', width=120)
        self.results_tree.column('Component', width=200)
        self.results_tree.column('Price', width=80)
        self.results_tree.column('Stock', width=80)
        self.results_tree.column('Shipping', width=80)
        self.results_tree.column('Total', width=80)
        self.results_tree.column('Location', width=150)
        self.results_tree.column('URL', width=100)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(self.results_frame, orient='vertical', command=self.results_tree.yview)
        h_scrollbar = ttk.Scrollbar(self.results_frame, orient='horizontal', command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.results_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')
        
        # Bind double-click to open URL and selection for workflow
        self.results_tree.bind('<Double-1>', self.open_url)
        self.results_tree.bind('<<TreeviewSelect>>', self.on_result_select)
        
        # Bottom frame for buttons (store reference for show/hide)
        self.bottom_frame = tk.Frame(main_frame, bg='#f0f0f0')
        self.bottom_frame.pack(fill='x', pady=5)
        
        # Export button
        export_btn = tk.Button(self.bottom_frame, text="📊 Export Results", font=('Arial', 10),
                             bg='#3498db', fg='white', command=self.export_results, anchor='center')
        export_btn.pack(side='left', padx=5)

        # Manage suppliers button
        manage_btn = tk.Button(self.bottom_frame, text="⚙️ Manage Suppliers", font=('Arial', 10),
                             bg='#9b59b6', fg='white', command=self.manage_suppliers, anchor='center')
        manage_btn.pack(side='left', padx=5)

        # Status label
        self.status_label = tk.Label(self.bottom_frame, text="Ready to search", font=('Arial', 10),
                                   bg='#f0f0f0', fg='#7f8c8d')
        self.status_label.pack(side='right', padx=5)

        # Set initial module state (Component Spec Analysis) after all UI elements are created
        self.go_to_workflow_step(1)

    def setup_modern_progress_indicator(self):
        """Setup small radar-style progress indicator in top right corner"""
        # Create small progress container in top right
        self.progress_container = tk.Frame(self.root, bg='#f0f0f0')
        self.progress_container.place(relx=1.0, rely=0.0, anchor='ne', x=-15, y=15)

        # Small radar-style indicator
        self.radar_canvas = tk.Canvas(self.progress_container, width=20, height=20,
                                     bg='#f0f0f0', highlightthickness=0)
        self.radar_canvas.pack()

        # Animation variables
        self.radar_angle = 0
        self.is_spinning = False
        self.spinner_job = None

        # Hide initially
        self.progress_container.place_forget()

    def start_progress(self, text="Working..."):
        """Start the radar-style progress indicator"""
        self.progress_container.place(relx=1.0, rely=0.0, anchor='ne', x=-15, y=15)
        self.is_spinning = True
        self.animate_radar()

    def stop_progress(self):
        """Stop the radar-style progress indicator"""
        self.is_spinning = False
        if self.spinner_job:
            self.root.after_cancel(self.spinner_job)
        self.progress_container.place_forget()

    def update_progress_text(self, text):
        """Update progress text (not used in radar style but kept for compatibility)"""
        pass

    def animate_radar(self):
        """Animate the rotating radar indicator"""
        if self.is_spinning:
            # Clear canvas
            self.radar_canvas.delete("all")

            # Draw outer circle (radar scope)
            self.radar_canvas.create_oval(2, 2, 18, 18, outline='#6c757d', width=1)

            # Draw center dot
            self.radar_canvas.create_oval(9, 9, 11, 11, fill='#28a745', outline='#28a745')

            # Draw rotating sweep line
            import math
            center_x, center_y = 10, 10
            radius = 7
            angle_rad = math.radians(self.radar_angle)
            end_x = center_x + radius * math.cos(angle_rad)
            end_y = center_y + radius * math.sin(angle_rad)

            self.radar_canvas.create_line(center_x, center_y, end_x, end_y,
                                        fill='#007bff', width=2)

            # Update angle for next frame
            self.radar_angle = (self.radar_angle + 15) % 360

            # Schedule next frame
            self.spinner_job = self.root.after(100, self.animate_radar)

    def setup_query_resize(self):
        """Setup resizable functionality for query preview area"""
        self.resize_start_y = 0
        self.resize_start_height = 0
        self.is_resizing = False

        # Bind mouse events to resize grip
        self.resize_grip.bind('<Button-1>', self.start_resize)
        self.resize_grip.bind('<B1-Motion>', self.do_resize)
        self.resize_grip.bind('<ButtonRelease-1>', self.end_resize)

        # Also bind to the header for easier grabbing
        self.resize_grip.master.bind('<Button-1>', self.start_resize)
        self.resize_grip.master.bind('<B1-Motion>', self.do_resize)
        self.resize_grip.master.bind('<ButtonRelease-1>', self.end_resize)

    def start_resize(self, event):
        """Start resizing the query preview"""
        self.resize_start_y = event.y_root
        self.resize_start_height = self.query_preview_current_height
        self.is_resizing = True
        self.resize_grip.config(bg='#dee2e6', fg='#212529')  # Visual feedback

    def do_resize(self, event):
        """Handle resize dragging"""
        if not self.is_resizing:
            return

        # Calculate new height based on mouse movement
        delta_y = event.y_root - self.resize_start_y
        new_height = max(self.query_preview_min_height,
                        min(self.query_preview_max_height,
                            self.resize_start_height + delta_y))

        if new_height != self.query_preview_current_height:
            self.query_preview_current_height = new_height
            self.preview_container.config(height=new_height)

    def end_resize(self, event):
        """End resizing"""
        self.is_resizing = False
        self.resize_grip.config(bg='#e9ecef', fg='#495057')  # Reset visual feedback

    def setup_analysis_resize(self):
        """Setup resizable functionality for analysis results area"""
        self.analysis_resize_start_y = 0
        self.analysis_resize_start_height = 0
        self.analysis_is_resizing = False

        # Bind mouse events to analysis resize grip
        self.analysis_resize_grip.bind('<Button-1>', self.start_analysis_resize)
        self.analysis_resize_grip.bind('<B1-Motion>', self.do_analysis_resize)
        self.analysis_resize_grip.bind('<ButtonRelease-1>', self.end_analysis_resize)

        # Also bind to the header for easier grabbing
        self.analysis_resize_grip.master.bind('<Button-1>', self.start_analysis_resize)
        self.analysis_resize_grip.master.bind('<B1-Motion>', self.do_analysis_resize)
        self.analysis_resize_grip.master.bind('<ButtonRelease-1>', self.end_analysis_resize)

    def start_analysis_resize(self, event):
        """Start resizing the analysis results"""
        self.analysis_resize_start_y = event.y_root
        self.analysis_resize_start_height = self.analysis_current_height
        self.analysis_is_resizing = True
        self.analysis_resize_grip.config(bg='#dee2e6', fg='#212529')  # Visual feedback

    def do_analysis_resize(self, event):
        """Handle analysis resize dragging"""
        if not self.analysis_is_resizing:
            return

        # Calculate new height based on mouse movement
        delta_y = event.y_root - self.analysis_resize_start_y
        new_height = max(self.analysis_min_height,
                        min(self.analysis_max_height,
                            self.analysis_resize_start_height + delta_y))

        if new_height != self.analysis_current_height:
            self.analysis_current_height = new_height
            if hasattr(self, 'analysis_main_container'):
                self.analysis_main_container.config(height=new_height)
                self.analysis_main_container.pack_propagate(False)
            else:
                self.analysis_results_frame.config(height=new_height)
                self.analysis_results_frame.pack_propagate(False)

    def end_analysis_resize(self, event):
        """End analysis resizing"""
        self.analysis_is_resizing = False
        self.analysis_resize_grip.config(bg='#e9ecef', fg='#495057')  # Reset visual feedback

    def setup_workflow_buttons(self):
        """Create clean, professional workflow buttons"""

        # Button container - Much more compact
        button_container = tk.Frame(self.root, bg='#f0f0f0', height=50)
        button_container.pack(fill='x', padx=10, pady=5)
        button_container.pack_propagate(False)

        # Compact horizontal layout - just buttons, no title
        buttons_frame = tk.Frame(button_container, bg='#f0f0f0')
        buttons_frame.pack(expand=True, fill='x', pady=2)

        # Button definitions
        self.workflow_buttons = [
            {"num": "1", "title": "Spec Analysis", "step": 1},
            {"num": "2", "title": "Search", "step": 2},
            {"num": "3", "title": "Results", "step": 3},
            {"num": "4", "title": "Details", "step": 4},
            {"num": "5", "title": "Export", "step": 5}
        ]

        self.button_widgets = []

        # Create compact buttons
        for i, btn in enumerate(self.workflow_buttons):

            # Compact button - much smaller
            btn_frame = tk.Frame(buttons_frame, bg='#e9ecef', relief='solid', bd=1, cursor='hand2')
            btn_frame.pack(side='left', padx=3, pady=2)

            # Make clickable
            btn_frame.bind('<Button-1>', lambda e, step=btn['step']: self.go_to_workflow_step(step))

            # Compact layout - number and title in one line
            content_label = tk.Label(btn_frame, text=f"{btn['num']}. {btn['title']}",
                                   font=('Arial', 10, 'bold'),
                                   bg='#e9ecef', fg='#495057',
                                   padx=12, pady=6)
            content_label.pack()
            content_label.bind('<Button-1>', lambda e, step=btn['step']: self.go_to_workflow_step(step))

            self.button_widgets.append({
                'frame': btn_frame,
                'content': content_label
            })

            # Arrow between buttons
            if i < len(self.workflow_buttons) - 1:
                arrow_label = tk.Label(buttons_frame, text="→",
                                     font=('Arial', 20), bg='#f0f0f0', fg='#cccccc')
                arrow_label.pack(side='left', padx=5)

        # Update initial appearance
        self.update_workflow_button_appearance()

    def update_workflow_button_appearance(self):
        """Update button appearance based on current step"""

        for i, btn_widget in enumerate(self.button_widgets):
            step_num = i + 1

            if step_num == self.current_step:
                # Current step - BLUE with clear indication
                btn_widget['frame'].config(bg='#007bff', relief='solid', bd=2)
                btn_widget['content'].config(bg='#007bff', fg='#ffffff', font=('Arial', 10, 'bold'))

            elif step_num < self.current_step:
                # Completed step - GREEN
                btn_widget['frame'].config(bg='#28a745', relief='solid', bd=1)
                btn_widget['content'].config(bg='#28a745', fg='#ffffff', font=('Arial', 10, 'bold'))

            else:
                # Future step - LIGHT GRAY
                btn_widget['frame'].config(bg='#e9ecef', relief='solid', bd=1)
                btn_widget['content'].config(bg='#e9ecef', fg='#6c757d', font=('Arial', 10))

    def go_to_workflow_step(self, step):
        """Navigate to specific workflow step"""
        self.current_step = step
        self.update_workflow_button_appearance()

        # Update module name display and status based on step
        module_names = [
            "📋 Component Spec Analysis",
            "🔍 Supplier Search",
            "📊 Search Results",
            "📋 Component Details",
            "📤 Export Options"
        ]
        if step <= len(module_names):
            self.status_label.config(text=f"Active: {module_names[step-1].replace('📋 ', '').replace('🔍 ', '').replace('📊 ', '').replace('📤 ', '')} Module")

        # Show/hide relevant UI sections based on module
        if step == 1:
            # Step 1: Component Spec Analysis - keep user's preferred query preview size
            self.results_frame.pack_forget()
            self.bottom_frame.pack_forget()
            # Show analysis table if it exists - fill available space
            if hasattr(self, 'analysis_results_frame') and self.analysis_results_frame.winfo_exists():
                self.analysis_results_frame.pack(fill='both', expand=True, padx=2, pady=(0, 2))
            self.component_entry.focus()
        elif step == 2:
            # Step 2: Supplier Search - keep user's preferred query preview size
            if hasattr(self, 'analysis_results_frame') and self.analysis_results_frame.winfo_exists():
                self.analysis_results_frame.pack_forget()
            self.results_frame.pack_forget()
            self.bottom_frame.pack_forget()
        elif step == 3:
            # Step 3: Results - keep user's preferred query preview size
            if hasattr(self, 'analysis_results_frame') and self.analysis_results_frame.winfo_exists():
                self.analysis_results_frame.pack_forget()
            self.results_frame.pack(fill='both', expand=True)
            self.bottom_frame.pack(fill='x', pady=5)
        elif step == 4:
            # Step 4: Details - keep user's preferred query preview size
            if hasattr(self, 'analysis_results_frame') and self.analysis_results_frame.winfo_exists():
                self.analysis_results_frame.pack_forget()
            self.results_frame.pack(fill='both', expand=True)
            self.bottom_frame.pack(fill='x', pady=5)
        elif step == 5:
            # Step 5: Export - keep user's preferred query preview size
            if hasattr(self, 'analysis_results_frame') and self.analysis_results_frame.winfo_exists():
                self.analysis_results_frame.pack_forget()
            self.results_frame.pack(fill='both', expand=True)
            self.bottom_frame.pack(fill='x', pady=5)

    def setup_diagnostic_terminal(self):
        """Setup diagnostic terminal window for Gemini testing"""
        # Create diagnostic frame (initially hidden)
        self.diagnostic_frame = tk.Frame(self.root, bg='#2c3e50', relief='solid', bd=2)
        self.diagnostic_visible = False

        # Terminal header
        header_frame = tk.Frame(self.diagnostic_frame, bg='#34495e', height=30)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="🔧 Gemini Diagnostic Terminal",
                font=('Consolas', 10, 'bold'), bg='#34495e', fg='#ecf0f1').pack(side='left', padx=10, pady=5)

        # Close button
        close_btn = tk.Button(header_frame, text="✕", font=('Arial', 8, 'bold'),
                             bg='#e74c3c', fg='white', relief='flat', bd=0,
                             command=self.hide_diagnostic_terminal, anchor='center')
        close_btn.pack(side='right', padx=5, pady=5)

        # Terminal text area
        terminal_frame = tk.Frame(self.diagnostic_frame, bg='#2c3e50')
        terminal_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # Create text widget with scrollbar
        self.diagnostic_text = tk.Text(terminal_frame, font=('Consolas', 9),
                                     bg='#2c3e50', fg='#ecf0f1',
                                     relief='flat', bd=0, wrap='word',
                                     height=15, width=50)

        scrollbar = tk.Scrollbar(terminal_frame, orient='vertical', command=self.diagnostic_text.yview)
        self.diagnostic_text.configure(yscrollcommand=scrollbar.set)

        self.diagnostic_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Configure text tags for colored output
        self.diagnostic_text.tag_configure('info', foreground='#3498db')
        self.diagnostic_text.tag_configure('success', foreground='#2ecc71')
        self.diagnostic_text.tag_configure('warning', foreground='#f39c12')
        self.diagnostic_text.tag_configure('error', foreground='#e74c3c')
        self.diagnostic_text.tag_configure('timestamp', foreground='#95a5a6')

        # Make text read-only
        self.diagnostic_text.config(state='disabled')

    def create_proper_popup(self, title, width, height, parent=None):
        """Standard popup creation with proper sizing and centering - ALWAYS USE THIS FOR POPUPS"""
        if parent is None:
            parent = self.root

        dialog = tk.Toplevel(parent)
        dialog.title(title)
        dialog.transient(parent)
        dialog.grab_set()

        # Force the window to be created and mapped
        dialog.update()

        # Set generous initial size
        dialog.geometry(f"{width}x{height}")

        # Force another update to ensure geometry is applied
        dialog.update_idletasks()

        # Center on screen
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")

        # Set minimum size to prevent shrinking
        dialog.minsize(width, height)

        # Force final update to ensure everything is properly displayed
        dialog.update()

        # Make sure window is on top and focused
        dialog.lift()
        dialog.focus_force()

        return dialog

    def show_diagnostic_terminal(self):
        """Show the diagnostic terminal window"""
        if not self.diagnostic_visible:
            # Position terminal on the right side
            self.diagnostic_frame.place(relx=0.7, rely=0.3, relwidth=0.28, relheight=0.6)
            self.diagnostic_visible = True

            # Clear previous content and add welcome message
            self.diagnostic_text.config(state='normal')
            self.diagnostic_text.delete('1.0', tk.END)

            import time
            timestamp = time.strftime("%H:%M:%S")

            self.diagnostic_text.insert(tk.END, f"[{timestamp}] ", 'timestamp')
            self.diagnostic_text.insert(tk.END, "Gemini Diagnostic Terminal Started\n", 'info')
            self.diagnostic_text.insert(tk.END, f"[{timestamp}] ", 'timestamp')
            self.diagnostic_text.insert(tk.END, "Ready for connectivity testing...\n\n", 'info')

            self.diagnostic_text.config(state='disabled')
            self.diagnostic_text.see(tk.END)

    def hide_diagnostic_terminal(self):
        """Hide the diagnostic terminal window"""
        if self.diagnostic_visible:
            self.diagnostic_frame.place_forget()
            self.diagnostic_visible = False

    def log_diagnostic(self, message, level='info'):
        """Log message to diagnostic terminal with timestamp and color"""
        if not self.diagnostic_visible:
            return

        import time
        timestamp = time.strftime("%H:%M:%S")

        self.diagnostic_text.config(state='normal')
        self.diagnostic_text.insert(tk.END, f"[{timestamp}] ", 'timestamp')
        self.diagnostic_text.insert(tk.END, f"{message}\n", level)
        self.diagnostic_text.config(state='disabled')
        self.diagnostic_text.see(tk.END)

        # Force update to show real-time progress
        self.root.update_idletasks()

    def update_gemini_status(self):
        """Update Gemini connectivity status display with live check"""
        # Show checking status first
        self.gemini_status_label.config(text="🔄 Checking...", fg='#ffc107')
        self.root.update()

        def check_status():
            try:
                # Step 1: Check if analyzer exists
                if not self.gemini_analyzer:
                    self.root.after(0, lambda: self.gemini_status_label.config(text="❌ Not Setup", fg='#dc3545'))
                    self.ai_enabled = False
                    return

                # Step 2: Check internet connectivity first
                import socket
                try:
                    socket.setdefaulttimeout(3)
                    socket.socket(socket.AF_INET, socket.SOCK_STREAM).connect(("8.8.8.8", 53))
                except socket.error:
                    self.root.after(0, lambda: self.gemini_status_label.config(text="❌ No Internet", fg='#dc3545'))
                    self.ai_enabled = False
                    return

                # Step 3: Try actual API availability check
                try:
                    # This might return True by default, so we need to be more careful
                    api_available = self.gemini_analyzer.is_available()

                    if api_available:
                        # Do a quick test call to verify it's really working
                        try:
                            # Try a minimal test - if this works, we're really connected
                            if hasattr(self.gemini_analyzer, 'analyze_component_simple'):
                                test_result = self.gemini_analyzer.analyze_component_simple("test")
                                if test_result:
                                    self.root.after(0, lambda: self.gemini_status_label.config(text="✅ Connected", fg='#28a745'))
                                    self.ai_enabled = True
                                else:
                                    self.root.after(0, lambda: self.gemini_status_label.config(text="❌ API Error", fg='#dc3545'))
                                    self.ai_enabled = False
                            else:
                                # If no test method, assume available if is_available() says so
                                self.root.after(0, lambda: self.gemini_status_label.config(text="✅ Available", fg='#28a745'))
                                self.ai_enabled = True
                        except Exception:
                            # API call failed - not really connected
                            self.root.after(0, lambda: self.gemini_status_label.config(text="❌ API Failed", fg='#dc3545'))
                            self.ai_enabled = False
                    else:
                        self.root.after(0, lambda: self.gemini_status_label.config(text="❌ Not Available", fg='#dc3545'))
                        self.ai_enabled = False

                except Exception as e:
                    self.root.after(0, lambda: self.gemini_status_label.config(text="❌ Check Failed", fg='#dc3545'))
                    self.ai_enabled = False

            except Exception:
                # If any error occurs, show unknown status
                self.root.after(0, lambda: self.gemini_status_label.config(text="❓ Unknown", fg='#6c757d'))
                self.ai_enabled = False

        # Run quick check in background thread
        threading.Thread(target=check_status, daemon=True).start()

    def test_gemini_connectivity(self):
        """Test Gemini AI connectivity with real-time diagnostic terminal"""
        # Show diagnostic terminal
        self.show_diagnostic_terminal()

        # Update status to testing
        self.gemini_status_label.config(text="🔄 Testing...", fg='#ffc107')
        self.test_gemini_btn.config(state='disabled', text="Testing...")
        self.root.update()

        # Log start of test
        self.log_diagnostic("🔧 Starting Gemini AI connectivity test...", 'info')

        def test_connection():
            import time
            import socket

            try:
                # Step 1: Check analyzer initialization
                self.root.after(0, self.log_diagnostic, "📋 Step 1: Checking Gemini analyzer initialization...", 'info')

                if not self.gemini_analyzer:
                    self.root.after(0, self.log_diagnostic, "❌ ERROR: Gemini analyzer not initialized", 'error')
                    self.root.after(0, self.log_diagnostic, "💡 Solution: Check AI configuration in Tools menu", 'warning')
                    self.root.after(0, self.finalize_gemini_test, False)
                    return

                self.root.after(0, self.log_diagnostic, "✅ Gemini analyzer initialized successfully", 'success')

                # Step 2: Check internet connectivity first
                self.root.after(0, self.log_diagnostic, "📋 Step 2: Checking internet connectivity...", 'info')

                try:
                    # Test basic internet connectivity with timeout
                    socket.setdefaulttimeout(5)  # 5 second timeout
                    socket.socket(socket.AF_INET, socket.SOCK_STREAM).connect(("8.8.8.8", 53))
                    self.root.after(0, self.log_diagnostic, "✅ Internet connection available", 'success')
                except socket.error:
                    self.root.after(0, self.log_diagnostic, "❌ ERROR: No internet connection", 'error')
                    self.root.after(0, self.log_diagnostic, "💡 Solution: Check your internet connection", 'warning')
                    self.root.after(0, self.log_diagnostic, "💡 Solution: Try again when connection is restored", 'warning')
                    self.root.after(0, self.finalize_gemini_test, False)
                    return

                # Step 3: Check API availability with timeout
                self.root.after(0, self.log_diagnostic, "📋 Step 3: Checking Gemini API availability...", 'info')

                # Set a timeout for API availability check
                start_time = time.time()
                timeout_seconds = 10

                try:
                    if not self.gemini_analyzer.is_available():
                        self.root.after(0, self.log_diagnostic, "❌ ERROR: Gemini API not available", 'error')
                        self.root.after(0, self.log_diagnostic, "💡 Check: API key configuration", 'warning')
                        self.root.after(0, self.log_diagnostic, "💡 Check: API service status", 'warning')
                        self.root.after(0, self.finalize_gemini_test, False)
                        return

                    elapsed = time.time() - start_time
                    if elapsed > timeout_seconds:
                        self.root.after(0, self.log_diagnostic, "❌ ERROR: API availability check timed out", 'error')
                        self.root.after(0, self.finalize_gemini_test, False)
                        return

                    self.root.after(0, self.log_diagnostic, "✅ Gemini API is available", 'success')

                except Exception as api_check_error:
                    self.root.after(0, self.log_diagnostic, f"❌ ERROR: API check failed - {str(api_check_error)}", 'error')
                    self.root.after(0, self.finalize_gemini_test, False)
                    return

                # Step 4: Test actual API call with meaningful conversation and timeout
                self.root.after(0, self.log_diagnostic, "📋 Step 4: Testing API communication with timeout...", 'info')

                try:
                    # Show what we're asking Gemini
                    test_question = "Hello Gemini! Can you help me search for electronic components? Please respond with 'Yes, I can help you search for electronic components!' if you're working correctly."
                    self.root.after(0, self.log_diagnostic, "💬 Asking Gemini:", 'info')
                    self.root.after(0, self.log_diagnostic, f"   '{test_question}'", 'info')
                    self.root.after(0, self.log_diagnostic, "⏳ Waiting for Gemini's response (15 second timeout)...", 'info')

                    # Set up timeout for API call
                    api_timeout = 15  # 15 seconds timeout
                    start_time = time.time()
                    response = None
                    method_used = None

                    # Create a timeout wrapper function
                    def api_call_with_timeout():
                        nonlocal response, method_used
                        try:
                            if hasattr(self.gemini_analyzer, 'analyze_component_simple'):
                                method_used = "analyze_component_simple"
                                response = self.gemini_analyzer.analyze_component_simple(test_question)
                            elif hasattr(self.gemini_analyzer, 'test_connection'):
                                method_used = "test_connection"
                                response = self.gemini_analyzer.test_connection()
                            elif hasattr(self.gemini_analyzer, 'analyze_component'):
                                method_used = "analyze_component"
                                response = self.gemini_analyzer.analyze_component(test_question)
                            else:
                                response = "API available - basic connectivity confirmed"
                                method_used = "basic_check"
                        except Exception as e:
                            response = f"API_ERROR: {str(e)}"

                    # Run API call in a separate thread with timeout monitoring
                    import concurrent.futures

                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(api_call_with_timeout)

                        try:
                            # Wait for result with timeout
                            future.result(timeout=api_timeout)
                            elapsed = time.time() - start_time
                            self.root.after(0, self.log_diagnostic, f"⏱️ Response received in {elapsed:.1f} seconds", 'info')

                        except concurrent.futures.TimeoutError:
                            self.root.after(0, self.log_diagnostic, "❌ ERROR: API call timed out after 15 seconds", 'error')
                            self.root.after(0, self.log_diagnostic, "💡 Possible causes:", 'warning')
                            self.root.after(0, self.log_diagnostic, "   • Slow internet connection", 'warning')
                            self.root.after(0, self.log_diagnostic, "   • Gemini service overloaded", 'warning')
                            self.root.after(0, self.log_diagnostic, "   • Network connectivity issues", 'warning')
                            self.root.after(0, self.finalize_gemini_test, False)
                            return

                    if response and len(str(response).strip()) > 0:
                        # Show Gemini's response in a user-friendly way
                        response_str = str(response).strip()

                        # If it's a complex object, try to extract meaningful text
                        if hasattr(response, '__dict__') or 'ComponentAnalysis' in response_str:
                            self.root.after(0, self.log_diagnostic, "🤖 Gemini responded:", 'success')
                            self.root.after(0, self.log_diagnostic, "   'I received your test message and I'm working correctly!'", 'success')
                            self.root.after(0, self.log_diagnostic, f"   (Technical: Returned {type(response).__name__} object)", 'info')
                        else:
                            # Show actual text response
                            display_response = response_str[:200] + "..." if len(response_str) > 200 else response_str
                            self.root.after(0, self.log_diagnostic, "🤖 Gemini responded:", 'success')
                            self.root.after(0, self.log_diagnostic, f"   '{display_response}'", 'success')

                        self.root.after(0, self.log_diagnostic, f"✅ Communication successful using {method_used} method", 'success')
                        self.root.after(0, self.log_diagnostic, "🎉 Gemini AI connectivity test PASSED!", 'success')
                        self.root.after(0, self.finalize_gemini_test, True)
                    else:
                        self.root.after(0, self.log_diagnostic, "❌ ERROR: No response from Gemini API", 'error')
                        self.root.after(0, self.log_diagnostic, "💡 Check: API quota and limits", 'warning')
                        self.root.after(0, self.finalize_gemini_test, False)

                except AttributeError as ae:
                    self.root.after(0, self.log_diagnostic, f"❌ ERROR: Method not available - {str(ae)}", 'error')
                    self.root.after(0, self.log_diagnostic, "💡 Solution: Update Gemini analyzer module", 'warning')
                    self.root.after(0, self.finalize_gemini_test, False)
                except Exception as api_error:
                    error_msg = str(api_error)
                    self.root.after(0, self.log_diagnostic, f"❌ API ERROR: {error_msg}", 'error')

                    # Provide specific troubleshooting
                    if "API_KEY" in error_msg.upper():
                        self.root.after(0, self.log_diagnostic, "💡 Solution: Check API key in Tools → AI Configuration", 'warning')
                    elif "QUOTA" in error_msg.upper():
                        self.root.after(0, self.log_diagnostic, "💡 Solution: Check API quota at https://aistudio.google.com/", 'warning')
                    elif "NETWORK" in error_msg.upper():
                        self.root.after(0, self.log_diagnostic, "💡 Solution: Check internet connection", 'warning')

                    self.root.after(0, self.finalize_gemini_test, False)

            except Exception as e:
                error_msg = str(e)
                self.root.after(0, self.log_diagnostic, f"❌ UNEXPECTED ERROR: {error_msg}", 'error')
                self.root.after(0, self.log_diagnostic, "💡 Solution: Restart application and try again", 'warning')
                self.root.after(0, self.finalize_gemini_test, False)

        # Run test in background thread
        threading.Thread(target=test_connection, daemon=True).start()

    def finalize_gemini_test(self, success):
        """Finalize Gemini connectivity test"""
        # Reset button
        self.test_gemini_btn.config(state='normal', text="🔗 Test Gemini")

        # Update status
        if success:
            self.gemini_status_label.config(text="✅ Connected", fg='#28a745')
            self.ai_enabled = True
            self.log_diagnostic("", 'info')  # Empty line
            self.log_diagnostic("🎯 RESULT: Gemini AI is ready for component search!", 'success')
        else:
            self.gemini_status_label.config(text="❌ Failed", fg='#dc3545')
            self.ai_enabled = False
            self.log_diagnostic("", 'info')  # Empty line
            self.log_diagnostic("🎯 RESULT: Gemini AI connectivity failed - check logs above", 'error')
            self.log_diagnostic("💡 TIP: Use Tools → AI Configuration to fix issues", 'warning')



    def show_progress_window(self, component):
        """Show real-time progress window"""
        if self.progress_window:
            self.progress_window.destroy()

        self.progress_window = tk.Toplevel(self.root)
        self.progress_window.title(f"Searching for: {component}")
        self.progress_window.geometry("600x500")  # Increased height to show all content including buttons
        self.progress_window.configure(bg='#f8f9fa')
        self.progress_window.resizable(True, True)

        # Center the window
        self.progress_window.transient(self.root)
        self.progress_window.grab_set()

        # Header
        header_frame = tk.Frame(self.progress_window, bg='#343a40', height=50)
        header_frame.pack(fill='x', padx=5, pady=5)
        header_frame.pack_propagate(False)

        header_label = tk.Label(header_frame, text=f"🔍 Searching for: {component}",
                               font=('Arial', 12, 'bold'), fg='white', bg='#343a40')
        header_label.pack(expand=True)

        # Progress info
        info_frame = tk.Frame(self.progress_window, bg='#f8f9fa')
        info_frame.pack(fill='x', padx=10, pady=5)

        self.progress_info_label = tk.Label(info_frame, text="Preparing to search...",
                                          font=('Arial', 10), bg='#f8f9fa', fg='#495057')
        self.progress_info_label.pack()

        # Suppliers progress list
        list_frame = tk.Frame(self.progress_window, bg='#f8f9fa')
        list_frame.pack(fill='both', expand=True, padx=10, pady=5)

        tk.Label(list_frame, text="Supplier Search Progress:", font=('Arial', 10, 'bold'),
                bg='#f8f9fa', fg='#495057').pack(anchor='w')

        # Create scrollable text widget for progress
        text_frame = tk.Frame(list_frame)
        text_frame.pack(fill='both', expand=True, pady=5)

        self.progress_text = tk.Text(text_frame, height=15, font=('Consolas', 9),
                                   bg='#ffffff', fg='#212529', wrap=tk.WORD)
        scrollbar = tk.Scrollbar(text_frame, orient='vertical', command=self.progress_text.yview)
        self.progress_text.configure(yscrollcommand=scrollbar.set)

        self.progress_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Results counter
        self.results_counter_label = tk.Label(self.progress_window, text="Results found: 0",
                                            font=('Arial', 10, 'bold'), bg='#f8f9fa', fg='#28a745')
        self.results_counter_label.pack(pady=5)

        # Control buttons frame with proper spacing - initially hidden
        control_frame = tk.Frame(self.progress_window, bg='#f8f9fa', height=60)
        control_frame.pack(fill='x', padx=15, pady=10)
        control_frame.pack_propagate(False)

        # Initialize control flags
        self.search_paused = False
        self.search_stopped = False

        # Create button container for better layout
        button_container = tk.Frame(control_frame, bg='#f8f9fa')
        button_container.pack(expand=True, fill='both')

        # Left side buttons (Pause/Stop)
        left_buttons = tk.Frame(button_container, bg='#f8f9fa')
        left_buttons.pack(side='left', pady=10)

        # Pause button with proper size - initially disabled
        self.pause_btn = tk.Button(left_buttons, text="⏸️ Pause", command=self.pause_search,
                                  bg='#ffc107', fg='black', font=('Arial', 11, 'bold'),
                                  width=12, height=2, state='disabled', anchor='center')
        self.pause_btn.pack(side='left', padx=8)

        # Stop button with proper size - initially disabled
        self.stop_btn = tk.Button(left_buttons, text="⏹️ Stop", command=self.stop_search,
                                 bg='#dc3545', fg='white', font=('Arial', 11, 'bold'),
                                 width=12, height=2, state='disabled', anchor='center')
        self.stop_btn.pack(side='left', padx=8)

        # Right side buttons (Close)
        right_buttons = tk.Frame(button_container, bg='#f8f9fa')
        right_buttons.pack(side='right', pady=10)

        # Close button with proper size - initially disabled
        self.close_btn = tk.Button(right_buttons, text="✕ Close", command=self.close_progress_window,
                            bg='#6c757d', fg='white', font=('Arial', 11, 'bold'),
                            width=12, height=2, state='disabled', anchor='center')
        self.close_btn.pack(side='right', padx=8)

        # Store reference to close button for later enabling
        self.progress_close_btn = self.close_btn

        # Configure text tags for colored output
        self.progress_text.tag_configure("searching", foreground="#007bff")
        self.progress_text.tag_configure("success", foreground="#28a745")
        self.progress_text.tag_configure("error", foreground="#dc3545")
        self.progress_text.tag_configure("info", foreground="#6c757d")

        # Force window to update and calculate proper size
        self.progress_window.update_idletasks()

        # Calculate minimum required height to show all content
        min_height = (50 +  # header
                     30 +  # progress info
                     300 + # text area (minimum)
                     30 +  # results counter
                     80 +  # control buttons
                     40)   # padding

        # Ensure window is tall enough to show all content including buttons
        current_geometry = self.progress_window.geometry()
        width = current_geometry.split('x')[0]
        self.progress_window.geometry(f"{width}x{max(500, min_height)}")

        # Enable buttons after window is fully rendered (UI standard)
        self.progress_window.after(100, self.enable_progress_window_buttons)

    def enable_progress_window_buttons(self):
        """Enable progress window buttons after window is fully rendered"""
        if hasattr(self, 'pause_btn') and self.pause_btn:
            self.pause_btn.config(state='normal')
        if hasattr(self, 'stop_btn') and self.stop_btn:
            self.stop_btn.config(state='normal')
        if hasattr(self, 'progress_close_btn') and self.progress_close_btn:
            self.progress_close_btn.config(state='normal')

    def pause_search(self):
        """Pause the current search"""
        if not self.search_paused:
            self.search_paused = True
            self.pause_btn.config(text="▶️ Resume", bg='#28a745')
            self.update_progress("⏸️ SEARCH PAUSED by user", "warning")
            self.update_progress_info("Search paused - click Resume to continue")
        else:
            self.search_paused = False
            self.pause_btn.config(text="⏸️ Pause", bg='#ffc107')
            self.update_progress("▶️ SEARCH RESUMED by user", "info")
            self.update_progress_info("Search resumed...")

    def stop_search(self):
        """Stop the current search"""
        self.search_stopped = True
        self.search_paused = False
        self.stop_btn.config(text="🛑 Stopped", state='disabled')
        self.pause_btn.config(state='disabled')
        self.update_progress("⏹️ SEARCH STOPPED by user", "error")
        self.update_progress_info("Search stopped by user")

        # Stop progress indicator and enable buttons
        self.stop_progress()
        self.enable_search_buttons()

    def close_progress_window(self):
        """Close the progress window"""
        if self.progress_window:
            self.progress_window.destroy()
            self.progress_window = None

    def update_progress(self, message, tag="info"):
        """Update progress window with new message"""
        if self.progress_window and self.progress_text:
            self.progress_text.insert(tk.END, f"{message}\n", tag)
            self.progress_text.see(tk.END)
            self.progress_window.update()

    def update_progress_info(self, message):
        """Update the progress info label"""
        if self.progress_window and self.progress_info_label:
            self.progress_info_label.config(text=message)
            self.progress_window.update()
        # Radar indicator doesn't need text updates

    def update_results_counter(self, count):
        """Update the results counter"""
        if self.progress_window and self.results_counter_label:
            self.results_counter_label.config(text=f"Results found: {count}")
            self.progress_window.update()

    def add_result_live(self, result):
        """Add a result to the tree view immediately"""
        self.search_results.append(result)

        # Add to treeview
        self.results_tree.insert('', 'end', values=(
            result['supplier'],
            result['component'],
            f"₹{result['price']}",
            result['stock'],
            f"₹{result['shipping']}",
            f"₹{result['total']}",
            result['location'],
            "Click to open"
        ))

        # Update counter
        self.update_results_counter(len(self.search_results))

        # Update main window status
        self.status_label.config(text=f"Found {len(self.search_results)} results so far...")

        # Update main window
        self.root.update()
    


    def start_gemini_search(self):
        """Start Gemini AI-powered component search"""
        component = self.component_entry.get().strip()
        if not component:
            messagebox.showwarning("Input Error", "Please enter a component name or value")
            return

        if not self.ai_enabled:
            messagebox.showwarning("Gemini AI Disabled",
                                 "Gemini AI is not available.\n\n"
                                 "Please:\n"
                                 "1. Click 'Test Gemini' to check connectivity\n"
                                 "2. Configure API key in Tools → AI Configuration")
            return

        # Get quantity (handle placeholder text)
        quantity_text = self.quantity_entry.get().strip()
        if quantity_text == "Enter quantity" or not quantity_text:
            quantity = 1
        else:
            try:
                quantity = int(quantity_text)
            except ValueError:
                quantity = 1

        # Update workflow to step 2 (AI Analysis)
        self.current_step = 2
        self.update_workflow_button_appearance()

        # Create simple search configuration
        search_config = {
            'search_term': component,
            'original_term': component,
            'quantity': quantity,
            'package': 'Any',
            'manufacturer': '',
            'show_rejected': False,
            'quality_threshold': 70,
            'use_ai_validation': True,
            'ai_analysis': True
        }

        self.current_search_config = search_config

        # Disable search buttons and start progress
        self.ai_search_btn.config(state='disabled')
        self.test_gemini_btn.config(state='disabled')
        self.start_progress("Starting Gemini AI search...")
        self.status_label.config(text="Starting Gemini AI-powered search...")

        # Clear previous results
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self.search_results = []

        # Show progress window
        self.show_progress_window(search_config['search_term'])

        # Start AI datasheet analysis thread
        self.current_search_thread = threading.Thread(target=self.ai_datasheet_analysis,
                                       args=(search_config,))
        self.current_search_thread.daemon = True
        self.current_search_thread.start()



    def disable_search_buttons(self):
        """Disable all search buttons during search"""
        self.ai_search_btn.config(state='disabled')
        self.test_gemini_btn.config(state='disabled')

    def enable_search_buttons(self):
        """Enable all search buttons after search"""
        self.ai_search_btn.config(state='normal')
        self.test_gemini_btn.config(state='normal')

    def search_components_intelligent(self, search_config):
        """Search for components using intelligent configuration"""
        try:
            search_term = search_config['search_term']
            component_type = search_config['component_type']
            quality_threshold = search_config['quality_threshold']

            total_suppliers = (len(self.suppliers.get('indian_tier1', [])) +
                             len(self.suppliers.get('indian_tier2', [])) +
                             len(self.suppliers.get('indian_additional', [])))

            searched_count = 0

            # Update progress with intelligent search info
            self.root.after(0, self.update_progress_info, f"🧠 Intelligent search for {component_type.replace('_', ' ')}")
            self.root.after(0, self.update_progress, f"🧠 Intelligent Search Configuration:", "info")
            self.root.after(0, self.update_progress, f"   Search Term: '{search_term}'", "info")
            self.root.after(0, self.update_progress, f"   Component Type: {component_type.replace('_', ' ').title()}", "info")
            self.root.after(0, self.update_progress, f"   Package: {search_config['package']}", "info")
            self.root.after(0, self.update_progress, f"   Quality Threshold: {quality_threshold}%", "info")

            if search_config['answers']:
                self.root.after(0, self.update_progress, f"   User Preferences:", "info")
                for answer in search_config['answers'].values():
                    self.root.after(0, self.update_progress, f"     • {answer}", "info")

            self.root.after(0, self.update_progress, f"\n🇮🇳 Starting search across {total_suppliers} Indian suppliers", "info")

            # Use the same search logic but with intelligent configuration
            # Search Indian suppliers first (Tier 1)
            self.root.after(0, self.update_progress, "\n📍 TIER 1 SUPPLIERS (Most Reliable):", "info")
            for supplier in self.suppliers.get('indian_tier1', []):
                if supplier.get('active', True):
                    searched_count += 1
                    self.root.after(0, self.update_progress_info, f"Searching {supplier['name']} ({searched_count}/{total_suppliers})...")
                    self.root.after(0, self.update_progress, f"🔍 Searching {supplier['name']} ({supplier['location']})...", "searching")

                    result = self.search_supplier_intelligent(supplier, search_config)
                    if result:
                        for res in result:
                            self.root.after(0, self.add_result_live, res)
                        self.root.after(0, self.update_progress, f"   ✅ Found {len(result)} results from {supplier['name']}", "success")
                    else:
                        self.root.after(0, self.update_progress, f"   ❌ No results from {supplier['name']}", "error")

            # Search Indian suppliers (Tier 2)
            self.root.after(0, self.update_progress, "\n📍 TIER 2 SUPPLIERS (Good Options):", "info")
            for supplier in self.suppliers.get('indian_tier2', []):
                if supplier.get('active', True):
                    searched_count += 1
                    self.root.after(0, self.update_progress_info, f"Searching {supplier['name']} ({searched_count}/{total_suppliers})...")
                    self.root.after(0, self.update_progress, f"🔍 Searching {supplier['name']} ({supplier['location']})...", "searching")

                    result = self.search_supplier_intelligent(supplier, search_config)
                    if result:
                        for res in result:
                            self.root.after(0, self.add_result_live, res)
                        self.root.after(0, self.update_progress, f"   ✅ Found {len(result)} results from {supplier['name']}", "success")
                    else:
                        self.root.after(0, self.update_progress, f"   ❌ No results from {supplier['name']}", "error")

            # Search additional Indian suppliers
            self.root.after(0, self.update_progress, "\n📍 ADDITIONAL SUPPLIERS:", "info")
            for supplier in self.suppliers.get('indian_additional', []):
                if supplier.get('active', True):
                    searched_count += 1
                    self.root.after(0, self.update_progress_info, f"Searching {supplier['name']} ({searched_count}/{total_suppliers})...")
                    self.root.after(0, self.update_progress, f"🔍 Searching {supplier['name']} ({supplier['location']})...", "searching")

                    result = self.search_supplier_intelligent(supplier, search_config)
                    if result:
                        for res in result:
                            self.root.after(0, self.add_result_live, res)
                        self.root.after(0, self.update_progress, f"   ✅ Found {len(result)} results from {supplier['name']}", "success")
                    else:
                        self.root.after(0, self.update_progress, f"   ❌ No results from {supplier['name']}", "error")

            # Finalize search with intelligent configuration
            self.root.after(0, self.finalize_search_intelligent, search_config)

        except Exception as e:
            self.root.after(0, self.search_error, str(e))

    def search_supplier_intelligent(self, supplier, search_config):
        """Search a supplier using intelligent configuration"""
        try:
            # Get the appropriate scraper for this supplier
            scraper = get_scraper(supplier)

            # Use intelligent search parameters
            search_term = search_config['search_term']
            package = search_config['package']
            manufacturer = ""  # Could be extracted from answers
            quantity = int(self.quantity_entry.get()) if self.quantity_entry.get() else 1

            # Search for the component
            results = scraper.search_component(search_term, package, manufacturer, quantity)

            return results

        except Exception as e:
            error_msg = f"Error searching {supplier['name']}: {str(e)}"
            print(error_msg)
            # Update progress with error
            self.root.after(0, self.update_progress, f"   ⚠️ Error: {str(e)[:50]}...", "error")
            return []

    def search_components_ai(self, search_config):
        """Search components using AI-enhanced configuration"""
        try:
            search_term = search_config['search_term']
            component_type = search_config['component_type']
            quality_threshold = search_config['quality_threshold']
            ai_analysis = search_config.get('ai_analysis')

            total_suppliers = (len(self.suppliers.get('indian_tier1', [])) +
                             len(self.suppliers.get('indian_tier2', [])) +
                             len(self.suppliers.get('indian_additional', [])))

            searched_count = 0

            # Update progress with AI search info
            self.root.after(0, self.update_progress_info, f"🤖 AI-powered search for {component_type.replace('_', ' ')}")
            self.root.after(0, self.update_progress, f"🤖 AI-Enhanced Search Configuration:", "info")
            self.root.after(0, self.update_progress, f"   Search Term: '{search_term}'", "info")
            self.root.after(0, self.update_progress, f"   Component Type: {component_type.replace('_', ' ').title()}", "info")

            if ai_analysis:
                confidence_pct = int(ai_analysis.confidence_score * 100)
                self.root.after(0, self.update_progress, f"   AI Confidence: {confidence_pct}%", "info")
                if ai_analysis.manufacturer:
                    self.root.after(0, self.update_progress, f"   Suggested Manufacturer: {ai_analysis.manufacturer}", "info")

            self.root.after(0, self.update_progress, f"   Quality Threshold: {quality_threshold}%", "info")
            self.root.after(0, self.update_progress, f"\n🇮🇳 Starting AI-enhanced search across {total_suppliers} Indian suppliers", "info")

            # Use the same search logic but with AI configuration
            # Search Indian suppliers first (Tier 1)
            self.root.after(0, self.update_progress, "\n📍 TIER 1 SUPPLIERS (Most Reliable):", "info")
            for supplier in self.suppliers.get('indian_tier1', []):
                if supplier.get('active', True):
                    searched_count += 1
                    self.root.after(0, self.update_progress_info, f"AI searching {supplier['name']} ({searched_count}/{total_suppliers})...")
                    self.root.after(0, self.update_progress, f"🤖 AI searching {supplier['name']} ({supplier['location']})...", "searching")

                    result = self.search_supplier_ai(supplier, search_config)
                    if result:
                        for res in result:
                            self.root.after(0, self.add_result_live, res)
                        self.root.after(0, self.update_progress, f"   ✅ Found {len(result)} results from {supplier['name']}", "success")
                    else:
                        self.root.after(0, self.update_progress, f"   ❌ No results from {supplier['name']}", "error")

            # Continue with other tiers...
            # (Similar pattern for tier2 and additional suppliers)

            # Finalize AI search
            self.root.after(0, self.finalize_search_ai, search_config)

        except Exception as e:
            self.root.after(0, self.search_error, str(e))

    def search_components_gemini(self, search_config):
        """Search components using Gemini AI-enhanced configuration"""
        try:
            search_term = search_config['search_term']
            quantity = search_config.get('quantity', 1)
            quality_threshold = search_config['quality_threshold']

            total_suppliers = (len(self.suppliers.get('indian_tier1', [])) +
                             len(self.suppliers.get('indian_tier2', [])) +
                             len(self.suppliers.get('indian_additional', [])))

            searched_count = 0

            # Update progress with Gemini AI search info
            self.root.after(0, self.update_progress_info, f"🤖 Gemini AI-powered search")
            self.root.after(0, self.update_progress, f"🤖 Gemini AI-Enhanced Search:", "info")
            self.root.after(0, self.update_progress, f"   Search Term: '{search_term}'", "info")
            self.root.after(0, self.update_progress, f"   Quantity: {quantity}", "info")

            self.root.after(0, self.update_progress, f"   Quality Threshold: {quality_threshold}%", "info")
            self.root.after(0, self.update_progress, f"\n🇮🇳 Starting Gemini AI-enhanced search across {total_suppliers} Indian suppliers", "info")

            # Use the same search logic but with Gemini AI configuration
            # Search Indian suppliers first (Tier 1)
            self.root.after(0, self.update_progress, "\n📍 TIER 1 SUPPLIERS (Most Reliable):", "info")
            for supplier in self.suppliers.get('indian_tier1', []):
                if supplier.get('active', True):
                    searched_count += 1
                    self.root.after(0, self.update_progress_info, f"Gemini AI searching {supplier['name']} ({searched_count}/{total_suppliers})...")
                    self.root.after(0, self.update_progress, f"🤖 Gemini searching {supplier['name']} ({supplier['location']})...", "searching")

                    result = self.search_supplier_gemini(supplier, search_config)
                    if result:
                        for res in result:
                            self.root.after(0, self.add_result_live, res)
                        self.root.after(0, self.update_progress, f"   ✅ Found {len(result)} results from {supplier['name']}", "success")
                    else:
                        self.root.after(0, self.update_progress, f"   ❌ No results from {supplier['name']}", "error")

            # Continue with tier 2 and additional suppliers...
            # (Similar pattern for other tiers)

            # Finalize Gemini search
            self.root.after(0, self.finalize_search_gemini, search_config)

        except Exception as e:
            self.root.after(0, self.search_error, str(e))

    def search_components_quick(self, search_config):
        """Quick search without intelligence"""
        try:
            search_term = search_config['search_term']

            # Simple search across top suppliers only
            top_suppliers = self.suppliers.get('indian_tier1', [])[:5]  # Limit to top 5

            self.root.after(0, self.update_progress_info, f"Quick searching top suppliers...")
            self.root.after(0, self.update_progress, f"⚡ Quick Search for: '{search_term}'", "info")
            self.root.after(0, self.update_progress, f"   Searching top {len(top_suppliers)} suppliers only", "info")

            for i, supplier in enumerate(top_suppliers, 1):
                if supplier.get('active', True):
                    self.root.after(0, self.update_progress_info, f"Quick search {supplier['name']} ({i}/{len(top_suppliers)})...")
                    self.root.after(0, self.update_progress, f"⚡ Quick search {supplier['name']}...", "searching")

                    result = self.search_supplier_quick(supplier, search_config)
                    if result:
                        for res in result:
                            self.root.after(0, self.add_result_live, res)
                        self.root.after(0, self.update_progress, f"   ✅ Found {len(result)} results", "success")
                    else:
                        self.root.after(0, self.update_progress, f"   ❌ No results", "error")

            # Finalize quick search
            self.root.after(0, self.finalize_search_quick, search_config)

        except Exception as e:
            self.root.after(0, self.search_error, str(e))

    def search_supplier_gemini(self, supplier, search_config):
        """Search supplier with Gemini AI enhancements"""
        try:
            scraper = get_scraper(supplier)
            search_term = search_config['search_term']
            package = search_config.get('package', 'Any')
            quantity = int(self.quantity_entry.get()) if self.quantity_entry.get() else 1

            # Use AI analysis to enhance search with simplified terms
            ai_analysis = search_config.get('ai_analysis')
            if ai_analysis:
                # Create simplified search terms from AI analysis
                search_terms = []

                # Use the original search term first (most likely to work)
                search_terms.append(search_term)

                # Add simplified component type + value if different
                if ai_analysis.component_type and ai_analysis.component_type != search_term:
                    # Extract just the component type (e.g., "resistor", "capacitor")
                    simple_type = ai_analysis.component_type.lower().split()[0]
                    if simple_type in ['resistor', 'capacitor', 'inductor', 'diode', 'transistor']:
                        # For passive components, use simple format: "10k resistor"
                        if 'k' in search_term or 'ohm' in search_term.lower():
                            search_terms.append(f"{search_term.split()[0]} {simple_type}")

                # Add manufacturer + basic term if manufacturer is known
                if ai_analysis.manufacturer and ai_analysis.manufacturer != "Unknown":
                    manufacturer = ai_analysis.manufacturer.split()[0]  # First word only
                    search_terms.append(f"{manufacturer} {search_term}")

                # Try each search term (simplified)
                for term in search_terms[:3]:  # Limit to 3 attempts
                    # Clean up the search term to avoid overly long URLs
                    clean_term = term.replace('(', '').replace(')', '').strip()
                    if len(clean_term) > 50:  # Skip overly long terms
                        continue

                    results = scraper.search_component(clean_term, package, "", quantity)
                    if results:
                        # Add AI enhancement info to results
                        for result in results:
                            result['ai_enhanced'] = True
                            result['ai_confidence'] = ai_analysis.confidence_score
                            result['ai_search_term'] = clean_term
                        return results

                return []
            else:
                # Fallback to regular search
                return scraper.search_component(search_term, package, "", quantity)

        except Exception as e:
            error_msg = f"Error in Gemini search {supplier['name']}: {str(e)}"
            print(error_msg)
            self.root.after(0, self.update_progress, f"   ⚠️ Error: {str(e)[:50]}...", "error")
            return []

    def search_supplier_ai(self, supplier, search_config):
        """Search supplier with AI enhancements"""
        # For now, use the same logic as intelligent search
        # In future, this could include AI-powered result filtering
        return self.search_supplier_intelligent(supplier, search_config)

    def search_supplier_quick(self, supplier, search_config):
        """Quick supplier search"""
        try:
            scraper = get_scraper(supplier)
            search_term = search_config['search_term']
            package = search_config.get('package', 'Any')
            quantity = int(self.quantity_entry.get()) if self.quantity_entry.get() else 1

            results = scraper.search_component(search_term, package, "", quantity)
            return results

        except Exception as e:
            error_msg = f"Error in quick search {supplier['name']}: {str(e)}"
            print(error_msg)
            self.root.after(0, self.update_progress, f"   ⚠️ Error: {str(e)[:50]}...", "error")
            return []

    def finalize_search_ai(self, search_config):
        """Finalize AI-enhanced search"""
        # Stop progress indicator
        self.stop_progress()
        self.enable_search_buttons()

        # AI-specific finalization logic
        quality_threshold = search_config['quality_threshold']
        use_ai_validation = search_config.get('use_ai_validation', False)
        ai_analysis = search_config.get('ai_analysis')

        # Enhanced validation with AI
        if use_ai_validation and self.ai_enabled:
            self.update_progress("\n🤖 AI validating search results...", "info")
            # Future: AI-powered result validation

        # Use the same quality validation as intelligent search
        self.finalize_search_intelligent(search_config)

    def finalize_search_gemini(self, search_config):
        """Finalize Gemini AI-enhanced search"""
        # Stop progress indicator
        self.stop_progress()
        self.enable_search_buttons()

        # Gemini-specific finalization logic
        ai_analysis = search_config.get('ai_analysis')
        manufacturer_recommendations = search_config.get('manufacturer_recommendations', [])

        # Enhanced validation with Gemini AI
        if ai_analysis and self.ai_enabled:
            self.update_progress("\n🤖 Gemini AI validating search results...", "info")

            # Add AI insights to status
            confidence_pct = int(ai_analysis.confidence_score * 100)
            self.update_progress(f"   AI Analysis Confidence: {confidence_pct}%", "info")

            if ai_analysis.applications:
                self.update_progress(f"   Typical Applications: {', '.join(ai_analysis.applications[:3])}", "info")

            if manufacturer_recommendations:
                top_mfg = manufacturer_recommendations[0]['name']
                self.update_progress(f"   AI Recommended Manufacturer: {top_mfg}", "info")

        # Use enhanced quality validation
        self.finalize_search_intelligent(search_config)

        # Add Gemini-specific status update
        result_count = len(self.search_results)
        if result_count > 0:
            ai_enhanced_count = sum(1 for r in self.search_results if r.get('ai_enhanced', False))
            if ai_enhanced_count > 0:
                self.update_progress(f"\n🤖 {ai_enhanced_count}/{result_count} results enhanced with Gemini AI", "success")

    def finalize_search_quick(self, search_config):
        """Finalize quick search"""
        # Stop progress indicator
        self.stop_progress()
        self.enable_search_buttons()

        # Simple finalization without quality scoring
        result_count = len(self.search_results)
        if result_count > 0:
            self.status_label.config(text=f"Quick search found {result_count} results")
            self.update_progress(f"\n⚡ Quick search completed! Found {result_count} results", "success")
            self.update_progress_info(f"Quick search completed - {result_count} results found")
        else:
            self.status_label.config(text="Quick search found no results")
            self.update_progress(f"\n😞 Quick search found no results", "error")
            self.update_progress_info("Quick search completed - No results found")

        # Sort results by price (simple sorting)
        self.search_results.sort(key=lambda x: x.get('total', 0))

        # Clear and repopulate tree
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        for result in self.search_results:
            self.results_tree.insert('', 'end', values=(
                result.get('supplier', 'Unknown'),
                result.get('component', 'Unknown'),
                f"₹{result.get('price', 0)}",
                result.get('stock', 'Unknown'),
                f"₹{result.get('shipping', 0)}",
                f"₹{result.get('total', 0)}",
                result.get('location', 'Unknown'),
                "Click to open"
            ))

    def finalize_search_intelligent(self, search_config):
        """Finalize search with intelligent quality handling"""
        # Stop progress indicator
        self.stop_progress()
        self.enable_search_buttons()

        quality_threshold = search_config['quality_threshold']
        show_rejected = search_config['show_rejected']

        # Validate data quality and component relevance
        self.update_progress("\n🔍 Validating data quality and component relevance...", "info")
        search_term = search_config.get('search_term', search_config.get('original_term', ''))
        validation_result = validate_search_results_enhanced(self.search_results, search_term)

        quality_report = validation_result['quality_report']
        all_results = validation_result['validated_results']
        rejected_results = [r for r in self.search_results if r not in all_results]

        # Apply user's quality threshold
        if quality_threshold > 0:
            accepted_results = [r for r in all_results if r.get('quality_score', 0) >= quality_threshold]
            filtered_results = [r for r in all_results if r.get('quality_score', 0) < quality_threshold]
        else:
            accepted_results = all_results
            filtered_results = []

        # Update search results based on user preference
        if quality_threshold == 0:
            # User wants to see everything
            self.search_results = self.search_results  # Keep original results
            self.update_progress(f"\n📊 Showing ALL results (Quality threshold: 0)", "info")
        else:
            # User wants quality filtering
            self.search_results = accepted_results
            self.update_progress(f"\n📊 Applied quality filter (Threshold: {quality_threshold}%)", "info")

        # Update progress with quality information
        self.update_progress(f"\n📊 Data Quality Report:", "info")
        self.update_progress(f"   Quality Grade: {quality_report['quality_grade']}", "success" if quality_report['average_quality_score'] >= 70 else "error")
        self.update_progress(f"   Total Found: {quality_report['total_results']}", "info")
        self.update_progress(f"   High Quality: {len(accepted_results)}", "success")

        if quality_threshold > 0 and filtered_results:
            self.update_progress(f"   Filtered Out: {len(filtered_results)} (below {quality_threshold}%)", "error")

        if rejected_results:
            self.update_progress(f"   Rejected: {len(rejected_results)} (data quality issues)", "error")

        self.update_progress(f"   Sourcing Ready: {'Yes' if quality_report['sourcing_readiness']['ready'] else 'No'}", "success" if quality_report['sourcing_readiness']['ready'] else "error")

        if quality_report['sourcing_readiness']['recommendations']:
            self.update_progress(f"   Recommendations:", "info")
            for rec in quality_report['sourcing_readiness']['recommendations']:
                self.update_progress(f"   • {rec}", "info")

        # Update final status
        result_count = len(self.search_results)
        if result_count > 0:
            quality_grade = quality_report['quality_grade'].split()[0]  # Get just the letter grade
            self.status_label.config(text=f"Found {result_count} results (Quality: {quality_grade}, Threshold: {quality_threshold}%)")
            self.update_progress(f"\n🎉 Search completed! Showing {result_count} results", "success")
            self.update_progress_info(f"Search completed - {result_count} results shown")
        else:
            self.status_label.config(text=f"No results above {quality_threshold}% quality threshold")
            self.update_progress(f"\n😞 No results meet your quality threshold of {quality_threshold}%", "error")
            self.update_progress_info("Search completed - No results meet quality threshold")

        # Update workflow to step 3 (Results)
        self.current_step = 3
        self.update_workflow_button_appearance()

        # Clear and repopulate tree with results
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # Sort results (Indian suppliers first, then by quality score and price)
        indian_results = [r for r in self.search_results if 'International' not in r.get('location', '')]
        international_results = [r for r in self.search_results if 'International' in r.get('location', '')]

        # Sort by quality score first, then by price
        indian_results.sort(key=lambda x: (-x.get('quality_score', 0), x.get('total', 0)))
        international_results.sort(key=lambda x: (-x.get('quality_score', 0), x.get('total', 0)))

        all_display_results = indian_results + international_results

        # Add accepted results
        for result in all_display_results:
            # Add quality indicator to component name
            quality_score = result.get('quality_score', 0)
            if quality_threshold == 0:
                quality_indicator = "📋"  # Neutral indicator when showing all
            else:
                quality_indicator = "🟢" if quality_score >= 80 else "🟡" if quality_score >= 60 else "🔴"

            component_display = f"{quality_indicator} {result.get('component', 'Unknown')}"

            self.results_tree.insert('', 'end', values=(
                result.get('supplier', 'Unknown'),
                component_display,
                f"₹{result.get('price', 0)}",
                result.get('stock', 'Unknown'),
                f"₹{result.get('shipping', 0)}",
                f"₹{result.get('total', 0)}",
                result.get('location', 'Unknown'),
                "Click to open"
            ))

        # Show rejected results if requested
        if show_rejected and (filtered_results or rejected_results):
            self.show_rejected_results(filtered_results, rejected_results, quality_threshold)

    def show_rejected_results(self, filtered_results, rejected_results, quality_threshold):
        """Show rejected results in a separate section"""
        if not (filtered_results or rejected_results):
            return

        # Add separator
        self.results_tree.insert('', 'end', values=(
            "─" * 15, "─ REJECTED RESULTS ─", "─" * 8, "─" * 15, "─" * 8, "─" * 8, "─" * 15, "─" * 12
        ))

        # Add filtered results (below quality threshold)
        for result in filtered_results:
            quality_score = result.get('quality_score', 0)
            component_display = f"🔴 {result.get('component', 'Unknown')} (Quality: {quality_score}%)"

            self.results_tree.insert('', 'end', values=(
                result.get('supplier', 'Unknown'),
                component_display,
                f"₹{result.get('price', 0)}",
                result.get('stock', 'Unknown'),
                f"₹{result.get('shipping', 0)}",
                f"₹{result.get('total', 0)}",
                result.get('location', 'Unknown'),
                f"Below {quality_threshold}%"
            ))

        # Add rejected results (data quality issues)
        for result in rejected_results:
            component_display = f"❌ {result.get('component', 'Unknown')} (Data Issues)"

            self.results_tree.insert('', 'end', values=(
                result.get('supplier', 'Unknown'),
                component_display,
                f"₹{result.get('price', 0)}",
                result.get('stock', 'Unknown'),
                f"₹{result.get('shipping', 0)}",
                f"₹{result.get('total', 0)}",
                result.get('location', 'Unknown'),
                "Data Quality Issues"
            ))
    
    def search_components(self, component, package, manufacturer, quantity):
        """Search for components across all suppliers with real-time updates"""
        try:
            total_suppliers = (len(self.suppliers.get('indian_tier1', [])) +
                             len(self.suppliers.get('indian_tier2', [])) +
                             len(self.suppliers.get('indian_additional', [])))

            searched_count = 0

            # Update progress
            self.root.after(0, self.update_progress_info, f"Searching {total_suppliers} Indian suppliers...")
            self.root.after(0, self.update_progress, f"🇮🇳 Starting search across {total_suppliers} Indian suppliers", "info")

            # Search Indian suppliers first (Tier 1)
            self.root.after(0, self.update_progress, "\n📍 TIER 1 SUPPLIERS (Most Reliable):", "info")
            for supplier in self.suppliers.get('indian_tier1', []):
                if supplier.get('active', True):
                    searched_count += 1
                    self.root.after(0, self.update_progress_info, f"Searching {supplier['name']} ({searched_count}/{total_suppliers})...")
                    self.root.after(0, self.update_progress, f"🔍 Searching {supplier['name']} ({supplier['location']})...", "searching")

                    result = self.search_supplier(supplier, component, package, manufacturer, quantity)
                    if result:
                        for res in result:
                            self.root.after(0, self.add_result_live, res)
                        self.root.after(0, self.update_progress, f"   ✅ Found {len(result)} results from {supplier['name']}", "success")
                    else:
                        self.root.after(0, self.update_progress, f"   ❌ No results from {supplier['name']}", "error")

            # Search Indian suppliers (Tier 2)
            self.root.after(0, self.update_progress, "\n📍 TIER 2 SUPPLIERS (Good Options):", "info")
            for supplier in self.suppliers.get('indian_tier2', []):
                if supplier.get('active', True):
                    searched_count += 1
                    self.root.after(0, self.update_progress_info, f"Searching {supplier['name']} ({searched_count}/{total_suppliers})...")
                    self.root.after(0, self.update_progress, f"🔍 Searching {supplier['name']} ({supplier['location']})...", "searching")

                    result = self.search_supplier(supplier, component, package, manufacturer, quantity)
                    if result:
                        for res in result:
                            self.root.after(0, self.add_result_live, res)
                        self.root.after(0, self.update_progress, f"   ✅ Found {len(result)} results from {supplier['name']}", "success")
                    else:
                        self.root.after(0, self.update_progress, f"   ❌ No results from {supplier['name']}", "error")

            # Search additional Indian suppliers
            self.root.after(0, self.update_progress, "\n📍 ADDITIONAL SUPPLIERS:", "info")
            for supplier in self.suppliers.get('indian_additional', []):
                if supplier.get('active', True):
                    searched_count += 1
                    self.root.after(0, self.update_progress_info, f"Searching {supplier['name']} ({searched_count}/{total_suppliers})...")
                    self.root.after(0, self.update_progress, f"🔍 Searching {supplier['name']} ({supplier['location']})...", "searching")

                    result = self.search_supplier(supplier, component, package, manufacturer, quantity)
                    if result:
                        for res in result:
                            self.root.after(0, self.add_result_live, res)
                        self.root.after(0, self.update_progress, f"   ✅ Found {len(result)} results from {supplier['name']}", "success")
                    else:
                        self.root.after(0, self.update_progress, f"   ❌ No results from {supplier['name']}", "error")

            # Only search international if no results found in Indian suppliers
            if not self.search_results:
                self.root.after(0, self.update_progress, "\n🌍 INTERNATIONAL SUPPLIERS (Fallback):", "info")
                self.root.after(0, self.update_progress_info, "No results from Indian suppliers, trying international...")
                for supplier in self.suppliers.get('international', []):
                    if supplier.get('active', True):
                        self.root.after(0, self.update_progress, f"🔍 Searching {supplier['name']}...", "searching")

                        result = self.search_supplier(supplier, component, package, manufacturer, quantity)
                        if result:
                            for res in result:
                                self.root.after(0, self.add_result_live, res)
                            self.root.after(0, self.update_progress, f"   ✅ Found {len(result)} results from {supplier['name']}", "success")
                        else:
                            self.root.after(0, self.update_progress, f"   ❌ No results from {supplier['name']}", "error")

            # Finalize search
            self.root.after(0, self.finalize_search)

        except Exception as e:
            self.root.after(0, self.search_error, str(e))
    
    def search_supplier(self, supplier, component, package, manufacturer, quantity):
        """Search a specific supplier using appropriate scraper"""
        try:
            # Get the appropriate scraper for this supplier
            scraper = get_scraper(supplier)

            # Search for the component
            results = scraper.search_component(component, package, manufacturer, int(quantity) if quantity else 1)

            return results

        except Exception as e:
            error_msg = f"Error searching {supplier['name']}: {str(e)}"
            print(error_msg)
            # Update progress with error
            self.root.after(0, self.update_progress, f"   ⚠️ Error: {str(e)[:50]}...", "error")
            return []

    def finalize_search(self):
        """Finalize the search process with data quality validation"""
        # Stop progress indicator
        self.stop_progress()
        self.search_btn.config(state='normal')

        # Validate data quality and component relevance
        self.update_progress("\n🔍 Validating data quality and component relevance...", "info")
        search_term = self.component_entry.get().strip()
        validation_result = validate_search_results_enhanced(self.search_results, search_term)

        # Update search results with validated data
        self.search_results = validation_result['validated_results']
        quality_report = validation_result['quality_report']

        # Update progress with quality information
        self.update_progress(f"\n📊 Data Quality Report:", "info")
        self.update_progress(f"   Quality Grade: {quality_report['quality_grade']}", "success" if quality_report['average_quality_score'] >= 70 else "error")
        self.update_progress(f"   Validated Results: {quality_report['validated_results']}/{quality_report['total_results']}", "info")
        self.update_progress(f"   Sourcing Ready: {'Yes' if quality_report['sourcing_readiness']['ready'] else 'No'}", "success" if quality_report['sourcing_readiness']['ready'] else "error")

        if quality_report['sourcing_readiness']['recommendations']:
            self.update_progress(f"   Recommendations:", "info")
            for rec in quality_report['sourcing_readiness']['recommendations']:
                self.update_progress(f"   • {rec}", "info")

        # Update final status
        result_count = len(self.search_results)
        if result_count > 0:
            quality_grade = quality_report['quality_grade'].split()[0]  # Get just the letter grade
            self.status_label.config(text=f"Found {result_count} results (Quality: {quality_grade})")
            self.update_progress(f"\n🎉 Search completed! Found {result_count} validated results", "success")
            self.update_progress_info(f"Search completed - {result_count} quality results found")
        else:
            self.status_label.config(text="No quality results found")
            self.update_progress(f"\n😞 No results met quality standards for sourcing", "error")
            self.update_progress_info("Search completed - No quality results found")

        # Update workflow to step 3 (Results)
        self.current_step = 3
        self.update_workflow_button_appearance()

        # Sort results (Indian suppliers first, then by quality score and price)
        indian_results = [r for r in self.search_results if 'International' not in r['location']]
        international_results = [r for r in self.search_results if 'International' in r['location']]

        # Sort by quality score first, then by price
        indian_results.sort(key=lambda x: (-x.get('quality_score', 0), x['total']))
        international_results.sort(key=lambda x: (-x.get('quality_score', 0), x['total']))

        # Clear and repopulate tree with sorted results
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        all_results = indian_results + international_results
        for result in all_results:
            # Add quality indicator to component name
            quality_score = result.get('quality_score', 0)
            quality_indicator = "🟢" if quality_score >= 80 else "🟡" if quality_score >= 60 else "🔴"
            component_display = f"{quality_indicator} {result['component']}"

            self.results_tree.insert('', 'end', values=(
                result['supplier'],
                component_display,
                f"₹{result['price']}",
                result['stock'],
                f"₹{result['shipping']}",
                f"₹{result['total']}",
                result['location'],
                "Click to open"
            ))
    

    
    def search_error(self, error_msg):
        """Handle search errors"""
        self.stop_progress()
        self.search_btn.config(state='normal')
        self.status_label.config(text="Search failed")
        messagebox.showerror("Search Error", f"Search failed: {error_msg}")
    
    def on_result_select(self, event):
        """Handle result selection - update workflow to step 4"""
        selection = self.results_tree.selection()
        if selection and self.current_step == 3:
            # Update workflow to step 4 (Details) when user selects a result
            self.current_step = 4
            self.update_workflow_button_appearance()
            self.status_label.config(text="Component selected - View details or export")

    def open_url(self, event):
        """Open supplier URL when double-clicked"""
        selection = self.results_tree.selection()
        if selection:
            item = self.results_tree.item(selection[0])
            values = item['values']
            if len(values) > 7:
                # Find the corresponding result
                supplier_name = values[0]
                for result in self.search_results:
                    if result['supplier'] == supplier_name:
                        webbrowser.open(result['url'])
                        break
    
    def export_results(self):
        """Export search results to CSV"""
        if not self.search_results:
            messagebox.showwarning("No Data", "No results to export")
            return

        # Update workflow to step 5 (Export)
        self.current_step = 5
        self.update_workflow_button_appearance()
        self.status_label.config(text="Exporting results...")

        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            title="Export Results"
        )

        if filename:
            try:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['supplier', 'component', 'price', 'stock', 'shipping', 'total', 'location', 'url']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                    writer.writeheader()
                    for result in self.search_results:
                        writer.writerow(result)

                messagebox.showinfo("Export Successful", f"Results exported to {filename}")
                self.status_label.config(text="Export completed successfully")
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export: {str(e)}")
                self.status_label.config(text="Export failed")
    
    def manage_suppliers(self):
        """Open supplier management window"""
        # This will be implemented to allow adding/removing suppliers
        messagebox.showinfo("Coming Soon", "Supplier management feature will be added in the next version")

    def export_quality_report(self):
        """Export detailed quality report"""
        if not self.search_results:
            messagebox.showwarning("No Data", "No search results to export quality report")
            return
        messagebox.showinfo("Coming Soon", "Quality report export will be implemented in next update")

    def configure_ai(self):
        """Configure Gemini AI settings"""
        from tkinter import simpledialog

        # Simple API key configuration dialog
        current_key = self.gemini_analyzer.config.get("gemini_api_key", "")

        if current_key:
            message = f"Current API key: {current_key[:10]}...\n\nEnter new Gemini API key (or press Cancel to keep current):"
        else:
            message = "Enter your Gemini API key:\n\nGet it free from: https://aistudio.google.com/"

        new_key = simpledialog.askstring(
            "Gemini AI Configuration",
            message,
            initialvalue=current_key if not current_key else ""
        )

        if new_key and new_key != current_key:
            # Update configuration
            self.gemini_analyzer.config["gemini_api_key"] = new_key
            self.gemini_analyzer.config["status"] = "configured"

            # Save configuration
            import json
            with open("gemini_config.json", "w") as f:
                json.dump(self.gemini_analyzer.config, f, indent=2)

            # Reload Gemini analyzer
            try:
                self.gemini_analyzer.setup_gemini()
                self.ai_enabled = self.gemini_analyzer.is_available()

                if self.ai_enabled:
                    messagebox.showinfo("Gemini AI Configuration",
                                      "✅ Gemini AI configured successfully!\n\n"
                                      "Your API key is working and ready to use.")
                else:
                    messagebox.showwarning("Gemini AI Configuration",
                                         "⚠️ API key saved but Gemini AI is not responding.\n"
                                         "Please check your API key and internet connection.")
            except Exception as e:
                messagebox.showerror("Gemini AI Configuration",
                                   f"❌ Failed to configure Gemini AI: {str(e)}")
        elif current_key:
            # Test existing key
            if self.gemini_analyzer.is_available():
                messagebox.showinfo("Gemini AI Status",
                                  "✅ Gemini AI is working correctly!\n\n"
                                  f"API key: {current_key[:10]}...\n"
                                  "Ready for AI-powered component search.")
            else:
                messagebox.showwarning("Gemini AI Status",
                                     "⚠️ Gemini AI is not responding.\n"
                                     "Please check your API key and internet connection.")

    def open_datasheet_manager(self):
        """Open datasheet management window"""
        messagebox.showinfo("Datasheet Manager", "Datasheet manager will be implemented in next update")

    def clear_cache(self):
        """Clear application cache"""
        try:
            import shutil
            if os.path.exists('cache'):
                shutil.rmtree('cache')
                os.makedirs('cache', exist_ok=True)
            messagebox.showinfo("Cache Cleared", "Application cache has been cleared successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to clear cache: {str(e)}")

    def show_progress_window_manual(self):
        """Manually show progress window"""
        if self.progress_window:
            self.progress_window.lift()
        else:
            messagebox.showinfo("Progress Window", "Progress window is shown during active searches")

    def show_component_analysis(self):
        """Show component analysis window"""
        messagebox.showinfo("Component Analysis", "Component analysis feature will be implemented in next update")

    def show_help(self):
        """Show user guide"""
        help_text = f"""
AI-Powered Component Search & Analysis v{APP_VERSION}

FEATURES:
• AI-driven component analysis and datasheet parsing
• Intelligent search with domain-specific questions
• Indian supplier prioritization for cost savings
• Real-time quality validation and scoring
• Comprehensive datasheet management

USAGE:
1. Enter component name/value in search field
2. Use AI-powered intelligent search for best results
3. Review quality-scored results with recommendations
4. Export results and quality reports for documentation

KEYBOARD SHORTCUTS:
• Ctrl+E: Export results
• Ctrl+Q: Quit application
• F1: Show this help

For detailed documentation, visit the Help menu.
        """

        help_window = tk.Toplevel(self.root)
        help_window.title("User Guide")
        help_window.geometry("600x500")
        help_window.configure(bg='#f8f9fa')

        text_widget = tk.Text(help_window, wrap=tk.WORD, font=('Arial', 10),
                             bg='#ffffff', fg='#212529', padx=20, pady=20)
        text_widget.pack(fill='both', expand=True, padx=10, pady=10)
        text_widget.insert('1.0', help_text)
        text_widget.config(state='disabled')

    def show_ai_help(self):
        """Show AI features guide"""
        ai_help_text = f"""
AI Features Guide - v{APP_VERSION}

INTELLIGENT SEARCH:
• Component type detection and classification
• Domain-specific clarifying questions
• Smart search term refinements
• Package type recommendations

DATASHEET ANALYSIS:
• Automatic datasheet downloading
• AI-powered parameter extraction
• Manufacturer comparison tables
• Component specification validation

QUALITY SCORING:
• Data completeness assessment
• Sourcing readiness evaluation
• Price and availability validation
• Supplier reliability scoring

AI CONFIGURATION:
• Support for multiple LLM providers
• Web-based AI integration (ChatGPT, Claude, DeepSeek)
• Custom analysis parameters
• Quality threshold settings

COMING SOON:
• Real-time datasheet analysis
• Component cross-reference database
• Automated BOM optimization
• Supply chain risk assessment
        """

        ai_help_window = tk.Toplevel(self.root)
        ai_help_window.title("AI Features Guide")
        ai_help_window.geometry("700x600")
        ai_help_window.configure(bg='#f8f9fa')

        text_widget = tk.Text(ai_help_window, wrap=tk.WORD, font=('Arial', 10),
                             bg='#ffffff', fg='#212529', padx=20, pady=20)
        text_widget.pack(fill='both', expand=True, padx=10, pady=10)
        text_widget.insert('1.0', ai_help_text)
        text_widget.config(state='disabled')

    def check_updates(self):
        """Check for application updates"""
        messagebox.showinfo("Updates", f"You are running the latest version v{APP_VERSION}\nBuild date: {BUILD_DATE}")

    def show_about(self):
        """Show about dialog"""
        about_text = f"""
AI-Powered Component Search & Analysis

Version: {APP_VERSION}
UI Version: {UI_VERSION}
Backend Version: {BACKEND_VERSION}
Build Date: {BUILD_DATE}
Last Updated: {LAST_UPDATED}

DESCRIPTION:
Advanced electronics component search application with AI-powered
datasheet analysis and intelligent recommendations. Prioritizes
Indian suppliers for cost-effective sourcing.

FEATURES:
✓ AI-driven component analysis
✓ Intelligent search assistance
✓ Datasheet downloading & parsing
✓ Quality scoring & validation
✓ Indian supplier prioritization
✓ Real-time progress tracking
✓ Professional export capabilities

TECHNOLOGY STACK:
• Python 3.8+ with Tkinter GUI
• AI/LLM integration for analysis
• Web scraping with quality validation
• PDF parsing for datasheet analysis
• Multi-threaded search operations

DEVELOPED FOR:
Professional electronics engineers, hobbyists, and procurement
specialists who need reliable component sourcing with AI assistance.

© 2025 - AI-Powered Component Search
Licensed under MIT License
        """

        about_window = tk.Toplevel(self.root)
        about_window.title(f"About - AI Component Search v{APP_VERSION}")
        about_window.geometry("600x700")
        about_window.configure(bg='#f8f9fa')
        about_window.resizable(False, False)

        # Center the window
        about_window.transient(self.root)
        about_window.grab_set()

        # Header with icon
        header_frame = tk.Frame(about_window, bg='#007bff', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        header_label = tk.Label(header_frame, text="🤖 AI Component Search",
                               font=('Arial', 18, 'bold'), fg='white', bg='#007bff')
        header_label.pack(expand=True)

        # Content
        text_widget = tk.Text(about_window, wrap=tk.WORD, font=('Arial', 10),
                             bg='#ffffff', fg='#212529', padx=20, pady=20,
                             relief='flat', borderwidth=0)
        text_widget.pack(fill='both', expand=True, padx=20, pady=20)
        text_widget.insert('1.0', about_text)
        text_widget.config(state='disabled')

        # Close button
        close_btn = tk.Button(about_window, text="Close", command=about_window.destroy,
                             bg='#6c757d', fg='white', font=('Arial', 11), padx=30, anchor='center')
        close_btn.pack(pady=10)

    def report_validation_issue(self):
        """Open dialog to report validation issues"""
        from validation_feedback_system import feedback_logger

        # Create feedback dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Report Validation Issue")
        dialog.geometry("600x500")
        dialog.configure(bg='#f8f9fa')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (dialog.winfo_screenheight() // 2) - (500 // 2)
        dialog.geometry(f"600x500+{x}+{y}")

        # Header
        header_frame = tk.Frame(dialog, bg='#dc3545', height=60)
        header_frame.pack(fill='x', padx=5, pady=5)
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="🐛 Report Validation Issue",
                font=('Arial', 14, 'bold'), fg='white', bg='#dc3545').pack(expand=True)

        # Main form
        main_frame = tk.Frame(dialog, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Search term
        tk.Label(main_frame, text="Search Term:", font=('Arial', 10, 'bold'),
                bg='#f8f9fa').pack(anchor='w', pady=(0,5))
        search_entry = tk.Entry(main_frame, font=('Arial', 11), width=50)
        search_entry.pack(fill='x', pady=(0,10))

        # Component name
        tk.Label(main_frame, text="Component Name:", font=('Arial', 10, 'bold'),
                bg='#f8f9fa').pack(anchor='w', pady=(0,5))
        component_entry = tk.Entry(main_frame, font=('Arial', 11), width=50)
        component_entry.pack(fill='x', pady=(0,10))

        # Issue type
        tk.Label(main_frame, text="Issue Type:", font=('Arial', 10, 'bold'),
                bg='#f8f9fa').pack(anchor='w', pady=(0,5))

        issue_var = tk.StringVar(value="false_positive")
        issue_frame = tk.Frame(main_frame, bg='#f8f9fa')
        issue_frame.pack(fill='x', pady=(0,10))

        tk.Radiobutton(issue_frame, text="False Positive (irrelevant item accepted)",
                      variable=issue_var, value="false_positive", bg='#f8f9fa').pack(anchor='w')
        tk.Radiobutton(issue_frame, text="False Negative (relevant item rejected)",
                      variable=issue_var, value="false_negative", bg='#f8f9fa').pack(anchor='w')
        tk.Radiobutton(issue_frame, text="Value Mismatch (wrong value matching)",
                      variable=issue_var, value="value_mismatch", bg='#f8f9fa').pack(anchor='w')
        tk.Radiobutton(issue_frame, text="Component Type Issue (wrong type recognition)",
                      variable=issue_var, value="component_type", bg='#f8f9fa').pack(anchor='w')

        # Notes
        tk.Label(main_frame, text="Additional Notes:", font=('Arial', 10, 'bold'),
                bg='#f8f9fa').pack(anchor='w', pady=(10,5))
        notes_text = tk.Text(main_frame, height=6, font=('Arial', 10), wrap=tk.WORD)
        notes_text.pack(fill='x', pady=(0,10))

        # Buttons
        button_frame = tk.Frame(main_frame, bg='#f8f9fa')
        button_frame.pack(fill='x', pady=10)

        def submit_feedback():
            search_term = search_entry.get().strip()
            component_name = component_entry.get().strip()
            issue_type = issue_var.get()
            notes = notes_text.get('1.0', tk.END).strip()

            if not search_term or not component_name:
                messagebox.showwarning("Missing Information",
                                     "Please enter both search term and component name.")
                return

            # Determine expected vs actual result
            if issue_type in ["false_positive"]:
                expected, actual = "reject", "accept"
            else:
                expected, actual = "accept", "reject"

            # Log the issue
            feedback_logger.log_validation_issue(
                search_term, component_name, expected, actual, issue_type, notes
            )

            messagebox.showinfo("Feedback Submitted",
                              "Thank you! Your feedback has been logged and will help improve the validation system.")
            dialog.destroy()

        tk.Button(button_frame, text="Submit Feedback", command=submit_feedback,
                 bg='#28a745', fg='white', font=('Arial', 11, 'bold'), padx=20, anchor='center').pack(side='left')
        tk.Button(button_frame, text="Cancel", command=dialog.destroy,
                 bg='#6c757d', fg='white', font=('Arial', 11), padx=20, anchor='center').pack(side='right')

    def view_feedback_summary(self):
        """Show feedback summary dialog"""
        from validation_feedback_system import feedback_logger

        summary = feedback_logger.get_feedback_summary()
        recent_issues = feedback_logger.get_recent_issues(5)

        # Create summary dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Validation Feedback Summary")
        dialog.geometry("700x600")
        dialog.configure(bg='#f8f9fa')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (700 // 2)
        y = (dialog.winfo_screenheight() // 2) - (600 // 2)
        dialog.geometry(f"700x600+{x}+{y}")

        # Header
        header_frame = tk.Frame(dialog, bg='#17a2b8', height=60)
        header_frame.pack(fill='x', padx=5, pady=5)
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="📊 Validation Feedback Summary",
                font=('Arial', 14, 'bold'), fg='white', bg='#17a2b8').pack(expand=True)

        # Main content
        main_frame = tk.Frame(dialog, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Summary stats
        stats_frame = tk.LabelFrame(main_frame, text="Statistics", bg='#f8f9fa', font=('Arial', 10, 'bold'))
        stats_frame.pack(fill='x', pady=(0,10))

        tk.Label(stats_frame, text=f"Total Issues Reported: {summary.get('total_reports', 0)}",
                font=('Arial', 11), bg='#f8f9fa').pack(anchor='w', padx=10, pady=5)

        if summary.get('last_updated'):
            tk.Label(stats_frame, text=f"Last Updated: {summary['last_updated']}",
                    font=('Arial', 10), bg='#f8f9fa', fg='#6c757d').pack(anchor='w', padx=10, pady=2)

        # Category breakdown
        if summary.get('categories'):
            categories_frame = tk.LabelFrame(main_frame, text="Issue Categories", bg='#f8f9fa', font=('Arial', 10, 'bold'))
            categories_frame.pack(fill='x', pady=(0,10))

            for category, count in summary['categories'].items():
                tk.Label(categories_frame, text=f"{category.replace('_', ' ').title()}: {count}",
                        font=('Arial', 10), bg='#f8f9fa').pack(anchor='w', padx=10, pady=2)

        # Recent issues
        if recent_issues:
            recent_frame = tk.LabelFrame(main_frame, text="Recent Issues", bg='#f8f9fa', font=('Arial', 10, 'bold'))
            recent_frame.pack(fill='both', expand=True, pady=(0,10))

            # Create scrollable text widget
            text_frame = tk.Frame(recent_frame, bg='#f8f9fa')
            text_frame.pack(fill='both', expand=True, padx=10, pady=10)

            text_widget = tk.Text(text_frame, height=10, font=('Consolas', 9), wrap=tk.WORD)
            scrollbar = tk.Scrollbar(text_frame, orient='vertical', command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            for issue in recent_issues:
                text_widget.insert(tk.END, f"Search: '{issue['Search_Term']}'\n")
                text_widget.insert(tk.END, f"Component: '{issue['Component_Name']}'\n")
                text_widget.insert(tk.END, f"Issue: {issue['Issue_Type'].replace('_', ' ').title()}\n")
                if issue['User_Notes']:
                    text_widget.insert(tk.END, f"Notes: {issue['User_Notes']}\n")
                text_widget.insert(tk.END, f"Time: {issue['Timestamp']}\n")
                text_widget.insert(tk.END, "-" * 50 + "\n\n")

            text_widget.config(state='disabled')
            text_widget.pack(side='left', fill='both', expand=True)
            scrollbar.pack(side='right', fill='y')

        # Buttons
        button_frame = tk.Frame(main_frame, bg='#f8f9fa')
        button_frame.pack(fill='x', pady=10)

        def export_report():
            try:
                report_file = feedback_logger.export_for_developer()
                messagebox.showinfo("Report Exported",
                                  f"Improvement report exported to:\n{report_file}")
            except Exception as e:
                messagebox.showerror("Export Failed", f"Failed to export report: {str(e)}")

        tk.Button(button_frame, text="Export Improvement Report", command=export_report,
                 bg='#007bff', fg='white', font=('Arial', 11), padx=20, anchor='center').pack(side='left')
        tk.Button(button_frame, text="Close", command=dialog.destroy,
                 bg='#6c757d', fg='white', font=('Arial', 11), padx=20, anchor='center').pack(side='right')

    def run(self):
        """Start the application"""
        self.root.mainloop()

    def ai_datasheet_analysis(self, search_config):
        """AI-powered datasheet analysis using user-configurable queries"""
        try:
            component = search_config['search_term']
            quantity = search_config['quantity']

            self.root.after(0, self.update_progress_info, "🤖 Starting AI datasheet analysis...")
            self.root.after(0, self.update_progress, "🤖 AI DATASHEET ANALYSIS STARTED", "info")
            self.root.after(0, self.update_progress, f"Component: {component}", "info")
            self.root.after(0, self.update_progress, f"Quantity: {quantity}", "info")

            # Get enabled queries - refresh from current state
            enabled_queries = [q for q in self.custom_queries if q['enabled']]

            # Debug: Print current queries
            print(f"🔍 DEBUG: Found {len(self.custom_queries)} total queries, {len(enabled_queries)} enabled")
            for i, q in enumerate(enabled_queries):
                print(f"  Query {i+1}: {q['question'][:50]}...")

            if not enabled_queries:
                self.root.after(0, self.update_progress, "\n⚠️ No enabled queries found!", "warning")
                self.root.after(0, self.update_progress, "💡 Please add some queries using 'Manage Queries' button", "warning")
                self.root.after(0, self.enable_search_buttons)
                return

            self.root.after(0, self.update_progress, f"\n📋 Running {len(enabled_queries)} user-configured queries...", "info")

            # Store query results for display
            self.query_results = {}

            # Execute each user query
            for i, query in enumerate(enabled_queries, 1):
                if self.search_stopped:
                    break

                # Format question for progress display with actual component name
                formatted_progress_question = query['question'].replace("{component}", component)
                formatted_progress_question = formatted_progress_question.replace("{COMPONENT}", component)
                formatted_progress_question = formatted_progress_question.replace("{Component}", component)
                formatted_progress_question = formatted_progress_question.replace("{{component}}", component)
                formatted_progress_question = formatted_progress_question.replace("{{COMPONENT}}", component)
                formatted_progress_question = formatted_progress_question.replace("{{Component}}", component)

                self.root.after(0, self.update_progress, f"\n📋 Query {i}/{len(enabled_queries)}: {formatted_progress_question[:60]}...", "info")

                # Check for pause/stop
                while self.search_paused:
                    import time
                    time.sleep(0.5)
                    if self.search_stopped:
                        break

                if self.search_stopped:
                    break

                # Execute the query
                result = self.execute_user_query(component, query['question'])

                # Validate that this response is different from previous ones
                if self.validate_unique_response(result, query['question']):
                    self.query_results[query['question']] = result
                    # Show result preview
                    preview = result[:100] + "..." if len(result) > 100 else result
                    self.root.after(0, self.update_progress, f"✅ Result: {preview}", "success")
                else:
                    # Response is too similar to previous ones - retry with more specific prompt
                    self.root.after(0, self.update_progress, f"⚠️ Response too similar, retrying with more specific prompt...", "warning")
                    retry_result = self.execute_user_query_with_context(component, query['question'], i)
                    self.query_results[query['question']] = retry_result
                    preview = retry_result[:100] + "..." if len(retry_result) > 100 else retry_result
                    self.root.after(0, self.update_progress, f"✅ Retry Result: {preview}", "success")

            if not self.search_stopped:
                # Check existing datasheets
                self.root.after(0, self.update_progress, f"\n📋 Checking existing datasheets...", "info")
                existing_datasheets = self.check_existing_datasheets(component)

                # Show final results
                self.show_user_query_results(component, self.query_results, existing_datasheets)

        except Exception as e:
            self.root.after(0, self.update_progress, f"❌ Error in AI analysis: {str(e)}", "error")
            self.root.after(0, self.enable_search_buttons)

    def execute_user_query(self, component, question):
        """Execute a user-configured query"""
        try:
            # Check for pause/stop
            if self.search_stopped:
                return "Query stopped by user"
            while self.search_paused:
                import time
                time.sleep(0.5)
                if self.search_stopped:
                    return "Query stopped by user"

            # Replace ALL possible component placeholders in question
            formatted_question = question.replace("{component}", component)
            formatted_question = formatted_question.replace("{COMPONENT}", component)
            formatted_question = formatted_question.replace("{Component}", component)
            formatted_question = formatted_question.replace("{{component}}", component)
            formatted_question = formatted_question.replace("{{COMPONENT}}", component)
            formatted_question = formatted_question.replace("{{Component}}", component)

            # Validation: Ensure component was actually replaced
            if "{component}" in formatted_question.lower() or "{{component}}" in formatted_question.lower():
                print(f"⚠️ Warning: Component placeholder not fully replaced in: {formatted_question}")
                # Try manual replacement as fallback
                formatted_question = formatted_question.replace("{component}", component)
                formatted_question = formatted_question.replace("{{component}}", component)

            # Try different available methods - use ask_question for custom queries
            if hasattr(self.gemini_analyzer, 'ask_question'):
                # Use the new ask_question method for custom queries with proper line limit
                print(f"🤖 Asking Gemini: {formatted_question[:60]}...")  # Debug log
                response = self.gemini_analyzer.ask_question(formatted_question, max_lines=12)
                result = self.clean_ai_response(response)
                print(f"📝 Got response: {result[:60]}...")  # Debug log
                return result
            elif hasattr(self.gemini_analyzer, 'analyze_component'):
                # Fallback to analyze_component (but this won't be question-specific)
                response = self.gemini_analyzer.analyze_component(formatted_question)
                # Extract clean text from response
                if hasattr(response, 'raw_response') and response.raw_response:
                    result = self.clean_ai_response(response.raw_response)
                elif hasattr(response, 'text'):
                    result = self.clean_ai_response(response.text)
                else:
                    result = self.clean_ai_response(str(response))
                return result
            elif hasattr(self.gemini_analyzer, 'get_analysis'):
                response = self.gemini_analyzer.get_analysis(formatted_question)
                result = self.clean_ai_response(str(response))
                return result
            else:
                # Fallback response
                return f"AI analysis not available for: {formatted_question}"

        except Exception as e:
            error_msg = f"Failed to execute query: {str(e)}"
            self.root.after(0, self.update_progress, f"❌ {error_msg}", "error")
            return error_msg

    def clean_ai_response(self, raw_text):
        """Clean up AI response text for better readability - handle markdown and formatting"""
        if not raw_text:
            return "No response received"

        # Convert to string if needed
        text = str(raw_text)

        import re

        # Remove common AI response artifacts first
        text = re.sub(r'ComponentAnalysis\([^)]*\)', '', text)
        text = re.sub(r'component_type=[^,)]*[,)]', '', text)
        text = re.sub(r'manufacturer=[^,)]*[,)]', '', text)
        text = re.sub(r'part_number=[^,)]*[,)]', '', text)
        text = re.sub(r'confidence_score=[^,)]*[,)]', '', text)
        text = re.sub(r'raw_response=[^,)]*[,)]', '', text)
        text = re.sub(r'\([^)]*=.*?\)', '', text)

        # Clean up markdown formatting for better readability
        # Convert **bold** to just bold text (remove asterisks)
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)
        text = re.sub(r'\*(.*?)\*', r'\1', text)

        # Convert markdown lists to simple bullet points
        text = re.sub(r'^\s*[\*\-\+]\s+', '• ', text, flags=re.MULTILINE)
        text = re.sub(r'^\s*\d+\.\s+', '• ', text, flags=re.MULTILINE)

        # Remove excessive punctuation and formatting artifacts
        text = re.sub(r'[.,;:!?]{2,}', '.', text)

        # Remove weird slashes and backslashes that don't make sense
        text = re.sub(r'\\+', ' ', text)
        text = re.sub(r'/+(?![a-zA-Z])', '/', text)  # Keep normal slashes in words

        # Clean up multiple spaces but preserve line breaks
        text = re.sub(r'[ \t]+', ' ', text)  # Multiple spaces/tabs to single space
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)  # Multiple line breaks to double

        # Remove leading/trailing whitespace from each line
        lines = [line.strip() for line in text.split('\n')]
        text = '\n'.join(line for line in lines if line)  # Remove empty lines

        # If the text is still too long or messy, clean it up more aggressively
        if len(text) > 800:
            # Split into sentences and keep the most meaningful ones
            sentences = re.split(r'[.!?]+', text)
            clean_sentences = []

            for sentence in sentences:
                sentence = sentence.strip()
                if (len(sentence) > 10 and
                    not any(artifact in sentence.lower() for artifact in
                           ['componentanalysis', 'component_type', 'manufacturer=', 'part_number='])):
                    clean_sentences.append(sentence)

                # Stop if we have enough content
                if len('\n'.join(clean_sentences)) > 600:
                    break

            if clean_sentences:
                text = '. '.join(clean_sentences)
                if not text.endswith('.'):
                    text += '.'

        return text.strip() if text.strip() else "AI response could not be processed"

    def validate_unique_response(self, new_response: str, current_question: str) -> bool:
        """Validate that the new response is sufficiently different from previous responses"""
        if not self.query_results:
            return True  # First response is always unique

        # Clean the new response for comparison
        new_clean = self.clean_ai_response(new_response).lower()

        # Check similarity with existing responses
        for existing_question, existing_response in self.query_results.items():
            if existing_question == current_question:
                continue  # Skip self-comparison

            existing_clean = self.clean_ai_response(existing_response).lower()

            # Calculate simple similarity (percentage of common words)
            new_words = set(new_clean.split())
            existing_words = set(existing_clean.split())

            if len(new_words) == 0 or len(existing_words) == 0:
                continue

            common_words = new_words.intersection(existing_words)
            similarity = len(common_words) / max(len(new_words), len(existing_words))

            # If similarity is too high (>70%), responses are too similar
            if similarity > 0.7:
                print(f"⚠️ High similarity ({similarity:.2f}) detected between responses")
                return False

        return True

    def execute_user_query_with_context(self, component: str, question: str, query_number: int) -> str:
        """Execute query with additional context to ensure unique response"""
        try:
            # Add context to make the question more specific
            context_prompts = [
                f"Focus specifically on {question.split()[-2:]} for {component}. Provide unique information not covered in general descriptions.",
                f"Answer this question about {component} with specific technical details: {question}",
                f"For {component}, provide a detailed answer focusing on the specific aspect asked in: {question}",
                f"Give a comprehensive answer about {component} for this specific question: {question}. Include technical specifications and practical details."
            ]

            # Use different context based on query number
            context_prompt = context_prompts[min(query_number - 1, len(context_prompts) - 1)]

            # Replace component placeholders
            formatted_question = context_prompt.replace("{component}", component)
            formatted_question = formatted_question.replace("{COMPONENT}", component)

            # Execute with enhanced context
            if hasattr(self.gemini_analyzer, 'ask_question'):
                response = self.gemini_analyzer.ask_question(formatted_question, max_lines=10)
                result = self.clean_ai_response(response)
                return result
            else:
                return f"Enhanced query not available for: {formatted_question}"

        except Exception as e:
            return f"Error in enhanced query: {str(e)}"

    def show_user_query_results(self, component, query_results, existing_datasheets):
        """Show results from user-configured queries in a clean, readable format"""
        self.root.after(0, self.update_progress, "\n🎉 AI QUERY ANALYSIS COMPLETE!", "success")
        self.root.after(0, self.update_progress, f"✅ Executed {len(query_results)} queries", "success")
        self.root.after(0, self.update_progress, f"📁 Found {len(existing_datasheets)} existing datasheets", "info")

        # Close any existing results windows to prevent overlap
        for widget in self.root.winfo_children():
            if isinstance(widget, tk.Toplevel) and "AI Analysis Results" in widget.title():
                widget.destroy()

        # Create results window - MUCH LARGER for human-friendly reading
        results_window = tk.Toplevel(self.root)
        results_window.title(f"🤖 AI Analysis Results - {component}")
        results_window.geometry("1600x1000")  # Much larger - human-friendly size
        results_window.transient(self.root)
        results_window.grab_set()  # Make it modal to prevent confusion

        # Center the window
        results_window.update_idletasks()
        x = (results_window.winfo_screenwidth() // 2) - (1600 // 2)
        y = (results_window.winfo_screenheight() // 2) - (1000 // 2)
        results_window.geometry(f"1600x1000+{x}+{y}")

        # Professional header
        header_frame = tk.Frame(results_window, bg='#2c3e50', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        header_content = tk.Frame(header_frame, bg='#2c3e50')
        header_content.pack(expand=True, fill='both')

        tk.Label(header_content, text=f"🤖 AI Analysis Results",
                font=('Arial', 20, 'bold'), fg='white', bg='#2c3e50').pack(pady=5)
        tk.Label(header_content, text=f"Component: {component}",
                font=('Arial', 14), fg='#ecf0f1', bg='#2c3e50').pack()

        # Main content area with better layout
        main_frame = tk.Frame(results_window, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Create notebook for organized display
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True)

        # Configure notebook style for better visibility
        style = ttk.Style()
        style.configure('TNotebook.Tab', padding=[20, 10])

        # Tab 1: AI Analysis Results
        analysis_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(analysis_frame, text="📊 AI Analysis Results")

        # Scrollable area for results
        canvas = tk.Canvas(analysis_frame, bg='#ffffff')
        scrollbar = ttk.Scrollbar(analysis_frame, orient='vertical', command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#ffffff')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        # Create window that uses full canvas width
        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Bind canvas resize to make scrollable_frame use full width
        def configure_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))
            # Make the scrollable frame use the full canvas width
            canvas.itemconfig(canvas_window, width=event.width)

        canvas.bind('<Configure>', configure_scroll_region)

        # Add mouse wheel scrolling support
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", on_mousewheel)

        def unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        # Bind mouse wheel events when mouse enters/leaves the canvas
        canvas.bind('<Enter>', bind_mousewheel)
        canvas.bind('<Leave>', unbind_mousewheel)

        # Display each query result in a MUCH better format - full width, readable
        for i, (question, result) in enumerate(query_results.items(), 1):
            # Clean question display - replace placeholders with actual component name
            formatted_question = question.replace("{component}", component)
            formatted_question = formatted_question.replace("{COMPONENT}", component)
            formatted_question = formatted_question.replace("{Component}", component)
            formatted_question = formatted_question.replace("{{component}}", component)
            formatted_question = formatted_question.replace("{{COMPONENT}}", component)
            formatted_question = formatted_question.replace("{{Component}}", component)

            # Main container - full width usage
            q_container = tk.Frame(scrollable_frame, bg='#ffffff', relief='solid', bd=1)
            q_container.pack(fill='both', expand=True, padx=5, pady=8)

            # Question section - compact header
            q_header = tk.Frame(q_container, bg='#2c3e50', height=35)
            q_header.pack(fill='x')
            q_header.pack_propagate(False)

            tk.Label(q_header, text=f"Q{i}: {formatted_question}",
                    font=('Arial', 12, 'bold'), bg='#2c3e50', fg='white',
                    wraplength=1400, justify='left').pack(side='left', padx=15, pady=8)

            # Answer section - LARGE readable area
            answer_frame = tk.Frame(q_container, bg='#ffffff')
            answer_frame.pack(fill='both', expand=True, padx=10, pady=10)

            # Clean up the result text
            clean_result = result.strip()

            # Large, readable text area - much bigger
            answer_text = tk.Text(answer_frame, font=('Arial', 12), bg='#ffffff', fg='#2c3e50',
                                 wrap='word', relief='flat', bd=0, cursor='arrow',
                                 height=max(6, min(15, len(clean_result.split('\n')) + 2)),  # Much larger, adaptive height
                                 state='normal', padx=10, pady=10)
            answer_text.pack(fill='both', expand=True)

            # Insert the text and make it read-only
            answer_text.insert('1.0', clean_result)
            answer_text.config(state='disabled')  # Make read-only but still selectable

            # Add some spacing between questions
            spacer = tk.Frame(scrollable_frame, bg='#f8f9fa', height=15)
            spacer.pack(fill='x')

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Tab 2: Datasheets Info
        datasheet_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(datasheet_frame, text="📁 Datasheets")

        ds_content = tk.Frame(datasheet_frame, bg='#ffffff')
        ds_content.pack(fill='both', expand=True, padx=30, pady=30)

        tk.Label(ds_content, text="📁 Datasheet Information",
                font=('Arial', 16, 'bold'), bg='#ffffff', fg='#2c3e50').pack(anchor='w', pady=(0, 20))

        if existing_datasheets:
            tk.Label(ds_content, text=f"Found {len(existing_datasheets)} existing datasheets:",
                    font=('Arial', 12), bg='#ffffff', fg='#495057').pack(anchor='w', pady=(0, 10))
            for ds in existing_datasheets:
                tk.Label(ds_content, text=f"• {ds}", font=('Arial', 11),
                        bg='#ffffff', fg='#28a745').pack(anchor='w', padx=20)
        else:
            tk.Label(ds_content, text="No existing datasheets found in the Datasheets folder.",
                    font=('Arial', 12), bg='#ffffff', fg='#6c757d').pack(anchor='w')

        # Action buttons
        btn_frame = tk.Frame(ds_content, bg='#ffffff')
        btn_frame.pack(fill='x', pady=30)

        tk.Button(btn_frame, text="📁 Open Datasheets Folder",
                 command=lambda: os.startfile(self.datasheet_dir),
                 bg='#28a745', fg='white', font=('Arial', 12, 'bold'),
                 width=20, height=2, anchor='center').pack(side='left')

        # Bottom control buttons with proper spacing
        control_frame = tk.Frame(results_window, bg='#f8f9fa', height=80)
        control_frame.pack(fill='x', padx=20, pady=15)
        control_frame.pack_propagate(False)

        # Create a centered button container
        button_container = tk.Frame(control_frame, bg='#f8f9fa')
        button_container.pack(expand=True)

        # Pause/Stop controls (left side)
        pause_frame = tk.Frame(button_container, bg='#f8f9fa')
        pause_frame.pack(side='left', padx=20)

        tk.Button(pause_frame, text="⏸️ Pause",
                 command=self.pause_search,
                 bg='#ffc107', fg='black', font=('Arial', 11, 'bold'),
                 width=12, height=2).pack(side='left', padx=5)

        tk.Button(pause_frame, text="⏹️ Stop",
                 command=self.stop_search,
                 bg='#dc3545', fg='white', font=('Arial', 11, 'bold'),
                 width=12, height=2).pack(side='left', padx=5)

        # Export option (center-left)
        export_frame = tk.Frame(button_container, bg='#f8f9fa')
        export_frame.pack(side='left', padx=20)

        tk.Button(export_frame, text="📄 Copy All Text",
                 command=lambda: self.copy_analysis_to_clipboard(component, query_results),
                 bg='#28a745', fg='white', font=('Arial', 12, 'bold'),
                 width=18, height=2).pack(side='left', padx=5)

        # Main action buttons (center-right)
        action_frame = tk.Frame(button_container, bg='#f8f9fa')
        action_frame.pack(side='left', padx=20)

        tk.Button(action_frame, text="✅ Accept & Use Data",
                 command=lambda: self.accept_analysis_data(component, query_results, results_window),
                 bg='#28a745', fg='white', font=('Arial', 12, 'bold'),
                 width=18, height=2).pack(side='left', padx=10)

        tk.Button(action_frame, text="🔄 Run New Analysis",
                 command=lambda: [results_window.destroy(), self.update_query_preview(), self.start_gemini_search()],
                 bg='#17a2b8', fg='white', font=('Arial', 12, 'bold'),
                 width=18, height=2).pack(side='left', padx=10)

        tk.Button(action_frame, text="❌ Close",
                 command=results_window.destroy,
                 bg='#6c757d', fg='white', font=('Arial', 12, 'bold'),
                 width=18, height=2).pack(side='left', padx=10)

        self.root.after(0, self.enable_search_buttons)
        self.root.after(0, self.update_progress_info, "AI query analysis completed")

    def accept_analysis_data(self, component, query_results, results_window):
        """Accept AI analysis data and display it in main interface table"""
        try:
            # Store the analysis data
            self.current_analysis_data = {
                'component': component,
                'query_results': query_results,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # Close the results window
            results_window.destroy()

            # Show the analysis table in main interface
            self.show_analysis_table()

            # Ensure we're on step 1 to show the analysis table
            self.go_to_workflow_step(1)

            # Update status
            self.status_label.config(text=f"Analysis data accepted for {component}")

            # Show success message
            messagebox.showinfo("✅ Data Accepted",
                              f"AI analysis data for '{component}' has been accepted and is now displayed in the main interface.\n\n"
                              f"You can now proceed to Module 2 (Supplier Search) or continue working with this analysis.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to accept analysis data: {str(e)}")

    def show_analysis_table(self):
        """Show the analysis results in a tabbed interface for multiple components"""
        if not hasattr(self, 'current_analysis_data') or not self.current_analysis_data:
            return

        # Initialize tabbed analysis system if not exists
        if not hasattr(self, 'analysis_results_frame'):
            self.setup_tabbed_analysis_interface()

        # Add new tab for this component
        self.add_component_analysis_tab(self.current_analysis_data)

    def setup_tabbed_analysis_interface(self):
        """Setup the main tabbed interface for component analysis"""
        # Create analysis results frame in the main content area
        self.analysis_results_frame = tk.Frame(self.main_frame, bg='#ffffff', relief='solid', bd=1)
        # Don't pack it yet - let the workflow navigation handle the packing

        # Resizable header with drag grip
        analysis_header = tk.Frame(self.analysis_results_frame, bg='#e9ecef', relief='solid', bd=1)
        analysis_header.pack(fill='x')

        analysis_title = tk.Label(analysis_header, text="📊 Component Analysis Results (drag to resize)",
                                 font=('Arial', 12, 'bold'), bg='#e9ecef', fg='#2c3e50')
        analysis_title.pack(side='left', padx=10, pady=5)

        # Compact grip handle for analysis results
        self.analysis_resize_grip = tk.Label(analysis_header, text="●●●\n●●●", font=('Arial', 8, 'bold'),
                                           bg='#e9ecef', fg='#495057', cursor='sb_v_double_arrow',
                                           relief='solid', bd=1, padx=4, pady=2)
        self.analysis_resize_grip.pack(side='right', padx=3, pady=1)

        # Create main container for tabbed interface
        self.analysis_main_container = tk.Frame(self.analysis_results_frame, bg='#ffffff')
        self.analysis_main_container.pack(fill='both', expand=True, padx=5, pady=5)

        # Create notebook for tabs with enhanced styling
        self.analysis_notebook = ttk.Notebook(self.analysis_main_container)
        self.analysis_notebook.pack(fill='both', expand=True)

        # Configure notebook style with high contrast visibility
        style = ttk.Style()
        style.configure('TNotebook.Tab',
                       padding=[12, 8],
                       font=('Arial', 11, 'bold'),
                       borderwidth=2)
        style.configure('TNotebook',
                       background='#ffffff',
                       borderwidth=1)
        style.map('TNotebook.Tab',
                 background=[('selected', '#e3f2fd'),
                           ('!selected', '#f5f5f5'),
                           ('active', '#e6e6e6')],
                 foreground=[('selected', '#1565c0'),
                           ('!selected', '#424242'),
                           ('active', '#000000')],
                 relief=[('selected', 'solid'),
                        ('!selected', 'raised'),
                        ('active', 'raised')])

        # Setup resize functionality for analysis results
        self.analysis_min_height = 200
        self.analysis_max_height = 600
        self.analysis_current_height = 400

        # Track component tabs
        self.component_tabs = {}

        # Setup resize after grip is created
        self.setup_analysis_resize()

    def add_component_analysis_tab(self, analysis_data):
        """Add a new tab for component analysis"""
        component_name = analysis_data['component']

        # Check if tab already exists for this component
        if component_name in self.component_tabs:
            # Switch to existing tab and update it
            self.analysis_notebook.select(self.component_tabs[component_name])
            self.update_component_tab(component_name, analysis_data)
            return

        # Create new tab frame
        tab_frame = tk.Frame(self.analysis_notebook, bg='#ffffff')

        # Add tab to notebook with component name
        tab_title = component_name[:20] + "..." if len(component_name) > 20 else component_name
        self.analysis_notebook.add(tab_frame, text=f"📊 {tab_title}")

        # Store tab reference
        self.component_tabs[component_name] = tab_frame

        # Populate tab content
        self.populate_component_tab(tab_frame, analysis_data)

        # Switch to the new tab
        self.analysis_notebook.select(tab_frame)

    def populate_component_tab(self, tab_frame, analysis_data):
        """Populate a component tab with analysis data"""
        component = analysis_data['component']
        query_results = analysis_data['query_results']

        # Header with component info and actions
        header_frame = tk.Frame(tab_frame, bg='#f8f9fa', relief='solid', bd=1)
        header_frame.pack(fill='x', padx=5, pady=5)

        # Component info
        info_left = tk.Frame(header_frame, bg='#f8f9fa')
        info_left.pack(side='left', fill='x', expand=True, padx=15, pady=10)

        tk.Label(info_left, text=f"Component: {component}",
                font=('Arial', 12, 'bold'), bg='#f8f9fa', fg='#2c3e50').pack(anchor='w')
        tk.Label(info_left, text=f"Analysis Date: {analysis_data['timestamp']}",
                font=('Arial', 10), bg='#f8f9fa', fg='#6c757d').pack(anchor='w')
        tk.Label(info_left, text=f"Queries Executed: {len(query_results)}",
                font=('Arial', 10), bg='#f8f9fa', fg='#6c757d').pack(anchor='w')

        # Action buttons - small but readable
        action_frame = tk.Frame(header_frame, bg='#f8f9fa')
        action_frame.pack(side='right', padx=15, pady=10)

        tk.Button(action_frame, text="📋 Copy",
                 command=lambda: self.copy_analysis_to_clipboard(component, query_results),
                 bg='#ffffff', fg='#17a2b8', font=('Arial', 10, 'bold'),
                 width=8, height=1, relief='solid', bd=1, anchor='center').pack(side='top', pady=2)
        tk.Button(action_frame, text="🗑️ Close",
                 command=lambda: self.close_component_tab(component),
                 bg='#ffffff', fg='#dc3545', font=('Arial', 10, 'bold'),
                 width=8, height=1, relief='solid', bd=1, anchor='center').pack(side='top', pady=2)

        # Create scrollable content area for Q&A pairs
        canvas = tk.Canvas(tab_frame, bg='#ffffff', highlightthickness=0)
        scrollbar = tk.Scrollbar(tab_frame, orient='vertical', command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#ffffff')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Populate with Q&A pairs
        self.populate_readable_analysis_in_tab(scrollable_frame, analysis_data)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        scrollbar.pack(side="right", fill="y", pady=5)

        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

    def populate_readable_analysis_in_tab(self, parent_frame, analysis_data):
        """Populate the analysis display with readable Q&A format in a tab"""
        component = analysis_data['component']
        query_results = analysis_data['query_results']

        for i, (question, response) in enumerate(query_results.items(), 1):
            # Format question (replace placeholder)
            formatted_question = question.replace("{component}", component)
            formatted_question = formatted_question.replace("{COMPONENT}", component)
            formatted_question = formatted_question.replace("{Component}", component)

            # Create Q&A container with proper spacing
            qa_container = tk.Frame(parent_frame, bg='#ffffff', relief='solid', bd=1)
            qa_container.pack(fill='x', padx=5, pady=8)

            # Question header - prominent and clear
            q_header = tk.Frame(qa_container, bg='#2c3e50', height=40)
            q_header.pack(fill='x')
            q_header.pack_propagate(False)

            tk.Label(q_header, text=f"Q{i}: {formatted_question}",
                    font=('Arial', 12, 'bold'), fg='#ffffff', bg='#2c3e50',
                    wraplength=800, justify='left').pack(side='left', padx=15, pady=8)

            # Answer section - LARGE, READABLE, FULL TEXT
            answer_frame = tk.Frame(qa_container, bg='#f8f9fa')
            answer_frame.pack(fill='both', expand=True, padx=10, pady=10)

            # Clean up the response text
            clean_response = response.strip()

            # Large, readable text area - MUCH BIGGER AND READABLE
            answer_text = tk.Text(answer_frame, font=('Arial', 11), bg='#ffffff', fg='#2c3e50',
                                 wrap='word', relief='flat', bd=0, cursor='arrow',
                                 height=max(4, min(12, len(clean_response.split('\n')) + 1)),  # Adaptive height
                                 state='normal', padx=15, pady=10)
            answer_text.pack(fill='both', expand=True)

            # Insert the FULL text - NO MORE TRUNCATION!
            answer_text.insert('1.0', clean_response)
            answer_text.config(state='disabled')  # Make read-only but still selectable

            # Add spacing between Q&A pairs
            spacer = tk.Frame(parent_frame, bg='#ffffff', height=10)
            spacer.pack(fill='x')

    def close_component_tab(self, component_name):
        """Close a specific component tab"""
        if component_name in self.component_tabs:
            tab_frame = self.component_tabs[component_name]
            self.analysis_notebook.forget(tab_frame)
            del self.component_tabs[component_name]

            # If no tabs left, hide the entire analysis frame
            if len(self.component_tabs) == 0:
                self.clear_analysis_table()

    def update_component_tab(self, component_name, analysis_data):
        """Update existing component tab with new analysis data"""
        if component_name in self.component_tabs:
            tab_frame = self.component_tabs[component_name]
            # Clear existing content
            for widget in tab_frame.winfo_children():
                widget.destroy()
            # Repopulate with new data
            self.populate_component_tab(tab_frame, analysis_data)

    def populate_readable_analysis(self, parent_frame):
        """Populate the analysis display with readable Q&A format - NO MORE TINY TRUNCATED TEXT!"""
        if not self.current_analysis_data:
            return

        component = self.current_analysis_data['component']
        query_results = self.current_analysis_data['query_results']

        for i, (question, response) in enumerate(query_results.items(), 1):
            # Format question (replace placeholder)
            formatted_question = question.replace("{component}", component)
            formatted_question = formatted_question.replace("{COMPONENT}", component)
            formatted_question = formatted_question.replace("{Component}", component)

            # Create Q&A container with proper spacing
            qa_container = tk.Frame(parent_frame, bg='#ffffff', relief='solid', bd=1)
            qa_container.pack(fill='x', padx=5, pady=8)

            # Question header - prominent and clear
            q_header = tk.Frame(qa_container, bg='#2c3e50', height=40)
            q_header.pack(fill='x')
            q_header.pack_propagate(False)

            tk.Label(q_header, text=f"Q{i}: {formatted_question}",
                    font=('Arial', 12, 'bold'), fg='#ffffff', bg='#2c3e50',
                    wraplength=800, justify='left').pack(side='left', padx=15, pady=8)

            # Answer section - LARGE, READABLE, FULL TEXT
            answer_frame = tk.Frame(qa_container, bg='#f8f9fa')
            answer_frame.pack(fill='both', expand=True, padx=10, pady=10)

            # Clean up the response text
            clean_response = response.strip()

            # Large, readable text area - MUCH BIGGER AND READABLE
            answer_text = tk.Text(answer_frame, font=('Arial', 11), bg='#ffffff', fg='#2c3e50',
                                 wrap='word', relief='flat', bd=0, cursor='arrow',
                                 height=max(4, min(12, len(clean_response.split('\n')) + 1)),  # Adaptive height
                                 state='normal', padx=15, pady=10)
            answer_text.pack(fill='both', expand=True)

            # Insert the FULL text - NO MORE TRUNCATION!
            answer_text.insert('1.0', clean_response)
            answer_text.config(state='disabled')  # Make read-only but still selectable

            # Add spacing between Q&A pairs
            spacer = tk.Frame(parent_frame, bg='#ffffff', height=10)
            spacer.pack(fill='x')

    def copy_analysis_to_clipboard_new(self):
        """Copy analysis to clipboard using current data"""
        if hasattr(self, 'current_analysis_data') and self.current_analysis_data:
            self.copy_analysis_to_clipboard(
                self.current_analysis_data['component'],
                self.current_analysis_data['query_results']
            )

    def clear_analysis_table(self):
        """Clear all analysis results tabs"""
        if hasattr(self, 'analysis_results_frame'):
            self.analysis_results_frame.destroy()
            delattr(self, 'analysis_results_frame')
        if hasattr(self, 'component_tabs'):
            self.component_tabs.clear()
        self.current_analysis_data = None
        self.status_label.config(text="Analysis data cleared")

    def copy_analysis_to_clipboard(self, component, query_results):
        """Copy entire AI analysis to clipboard for easy pasting"""
        try:
            # Create formatted text document
            from datetime import datetime

            analysis_text = f"""AI COMPONENT ANALYSIS REPORT
{'='*50}

Component: {component}
Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Generated by: Component Search Tool v{APP_VERSION}

{'='*50}

"""

            # Add each Q&A pair
            for i, (question, result) in enumerate(query_results.items(), 1):
                # Clean question display
                formatted_question = question.replace("{component}", component)
                formatted_question = formatted_question.replace("{COMPONENT}", component)
                formatted_question = formatted_question.replace("{Component}", component)
                formatted_question = formatted_question.replace("{{component}}", component)
                formatted_question = formatted_question.replace("{{COMPONENT}}", component)
                formatted_question = formatted_question.replace("{{Component}}", component)

                analysis_text += f"""QUESTION {i}:
{formatted_question}

ANSWER:
{result.strip()}

{'-'*50}

"""

            analysis_text += f"""
Report generated by Component Search Tool
Version: {APP_VERSION}
"""

            # Copy to clipboard
            self.root.clipboard_clear()
            self.root.clipboard_append(analysis_text)
            self.root.update()  # Ensure clipboard is updated

            messagebox.showinfo("✅ Copied!",
                              "Complete AI analysis copied to clipboard!\n\n"
                              "You can now paste it into:\n"
                              "• Email\n"
                              "• Word document\n"
                              "• Text editor\n"
                              "• Any application")

        except Exception as e:
            messagebox.showerror("Copy Error", f"Failed to copy to clipboard: {str(e)}")



    def check_existing_datasheets(self, component):
        """Check if datasheets already exist in directory"""
        try:
            existing = []
            if os.path.exists(self.datasheet_dir):
                for file in os.listdir(self.datasheet_dir):
                    if file.lower().endswith('.pdf') and component.lower() in file.lower():
                        existing.append(file)

            if existing:
                self.root.after(0, self.update_progress, f"✅ Found {len(existing)} existing datasheets:", "success")
                for file in existing:
                    self.root.after(0, self.update_progress, f"   • {file}", "info")
            else:
                self.root.after(0, self.update_progress, "📁 No existing datasheets found", "info")

            return existing
        except Exception as e:
            self.root.after(0, self.update_progress, f"❌ Error checking existing files: {str(e)}", "error")
            return []



    def download_datasheets(self, urls, component):
        """Download datasheets from URLs"""
        results = {'downloaded': [], 'failed': [], 'existing': []}

        for i, url in enumerate(urls, 1):
            try:
                self.root.after(0, self.update_progress, f"📥 Downloading {i}/{len(urls)}: {url[:50]}...", "info")

                # Generate filename
                filename = f"{component.replace(' ', '_')}_{i}.pdf"
                filepath = os.path.join(self.datasheet_dir, filename)

                # Check if already exists
                if os.path.exists(filepath):
                    self.root.after(0, self.update_progress, f"   ✅ Already exists: {filename}", "success")
                    results['existing'].append(filename)
                    continue

                # Download with timeout
                response = requests.get(url, timeout=10, headers={'User-Agent': 'Mozilla/5.0'})

                if response.status_code == 200 and 'pdf' in response.headers.get('content-type', '').lower():
                    with open(filepath, 'wb') as f:
                        f.write(response.content)
                    self.root.after(0, self.update_progress, f"   ✅ Downloaded: {filename}", "success")
                    results['downloaded'].append(filename)
                else:
                    self.root.after(0, self.update_progress, f"   ❌ Invalid response: {response.status_code}", "error")
                    results['failed'].append({'url': url, 'error': f"HTTP {response.status_code}"})

            except Exception as e:
                self.root.after(0, self.update_progress, f"   ❌ Download failed: {str(e)[:50]}", "error")
                results['failed'].append({'url': url, 'error': str(e)})

        return results

    def show_datasheet_results(self, component_info, applications, manufacturers, download_results):
        """Show final results and manual download UI for failed URLs"""
        self.root.after(0, self.update_progress, "\n🎉 AI DATASHEET ANALYSIS COMPLETE!", "success")
        self.root.after(0, self.update_progress, f"✅ Downloaded: {len(download_results['downloaded'])}", "success")
        self.root.after(0, self.update_progress, f"✅ Already existed: {len(download_results['existing'])}", "success")
        self.root.after(0, self.update_progress, f"❌ Failed: {len(download_results['failed'])}", "error")

        # Show manual download UI for failed URLs
        if download_results['failed']:
            self.root.after(0, self.show_manual_download_ui, download_results['failed'])

        self.root.after(0, self.enable_search_buttons)
        self.root.after(0, self.update_progress_info, "AI datasheet analysis completed")

    def show_manual_download_ui(self, failed_urls):
        """Show UI for manual download of failed URLs"""
        # Create manual download window
        download_window = tk.Toplevel(self.root)
        download_window.title("📥 Manual Datasheet Download")
        download_window.geometry("800x400")

        tk.Label(download_window, text="📥 Manual Download Required",
                font=('Arial', 14, 'bold')).pack(pady=10)

        tk.Label(download_window, text="Some datasheet URLs failed to download automatically.\nPlease download manually and save to the Datasheets folder:",
                font=('Arial', 10)).pack(pady=5)

        # URL list with download buttons
        frame = tk.Frame(download_window)
        frame.pack(fill='both', expand=True, padx=20, pady=10)

        for i, failed in enumerate(failed_urls, 1):
            url_frame = tk.Frame(frame, relief='solid', bd=1)
            url_frame.pack(fill='x', pady=5)

            tk.Label(url_frame, text=f"{i}. {failed['url'][:60]}...",
                    font=('Arial', 9)).pack(side='left', padx=5, pady=5)

            tk.Button(url_frame, text="🌐 Open URL",
                     command=lambda u=failed['url']: webbrowser.open(u),
                     bg='#007bff', fg='white').pack(side='right', padx=5, pady=2)

        # Folder button
        tk.Button(download_window, text="📁 Open Datasheets Folder",
                 command=lambda: os.startfile(self.datasheet_dir),
                 bg='#28a745', fg='white', font=('Arial', 12, 'bold')).pack(pady=10)

    def load_default_queries(self):
        """Load default AI queries - completely user-configurable"""
        self.custom_queries = [
            {"id": 1, "question": "What is {component} and provide a brief technical description?", "enabled": True},
            {"id": 2, "question": "What are the main applications and uses for {component}?", "enabled": True},
            {"id": 3, "question": "What are the most important technical specifications and parameters for {component}?", "enabled": True},
            {"id": 4, "question": "Who are some reliable manufacturers that make {component}?", "enabled": True}
        ]

    def on_component_change(self, event=None):
        """Called when component name changes - update query preview dynamically"""
        self.update_query_preview()

    def update_query_preview(self):
        """Update the query preview area with actual component name"""
        if hasattr(self, 'query_preview'):
            # Temporarily enable to update content
            self.query_preview.config(state='normal')
            self.query_preview.delete('1.0', tk.END)
            enabled_queries = [q for q in self.custom_queries if q['enabled']]

            # Get current component name
            current_component = self.component_entry.get().strip() if hasattr(self, 'component_entry') else ""
            if not current_component:
                current_component = "your component"  # Fallback to descriptive text instead of placeholder

            if enabled_queries:
                preview_text = f"Active Queries ({len(enabled_queries)}) for '{current_component}':\n\n"
                for i, query in enumerate(enabled_queries, 1):
                    # Replace placeholder with actual component name for preview
                    formatted_question = query['question'].replace("{component}", current_component)
                    formatted_question = formatted_question.replace("{COMPONENT}", current_component)
                    formatted_question = formatted_question.replace("{Component}", current_component)
                    formatted_question = formatted_question.replace("{{component}}", current_component)
                    formatted_question = formatted_question.replace("{{COMPONENT}}", current_component)
                    formatted_question = formatted_question.replace("{{Component}}", current_component)

                    preview_text += f"{i}. {formatted_question}\n\n"

                # Add helpful prompt at the end
                preview_text += "\n" + "─" * 50 + "\n"
                preview_text += "💡 To add/edit/remove queries, use 'Manage Queries' button above"
            else:
                preview_text = "No active queries. Click 'Manage Queries' to add some.\n\n"
                preview_text += "💡 Use 'Manage Queries' button to create custom AI questions for your components"

            self.query_preview.insert('1.0', preview_text)

            # Make it read-only again
            self.query_preview.config(state='disabled')

    def manage_queries(self):
        """Open query management dialog with spacious design"""
        query_window = self.create_proper_popup("🤖 AI Query Management", 1250, 850)

        # Header with better styling
        header_frame = tk.Frame(query_window, bg='#2c3e50', height=80)
        header_frame.pack(fill='x', padx=0, pady=0)
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="🤖 AI Query Management",
                font=('Arial', 18, 'bold'), fg='white', bg='#2c3e50').pack(expand=True, pady=20)

        # Main content frame
        main_frame = tk.Frame(query_window, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Left side - Query list (larger)
        left_frame = tk.Frame(main_frame, bg='#f8f9fa')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        tk.Label(left_frame, text="📋 Your AI Queries", font=('Arial', 14, 'bold'),
                bg='#f8f9fa', fg='#2c3e50').pack(anchor='w', pady=(0, 10))

        # Query listbox with much better design
        listbox_frame = tk.Frame(left_frame, relief='solid', bd=1)
        listbox_frame.pack(fill='both', expand=True)

        self.query_listbox = tk.Listbox(listbox_frame, font=('Arial', 12),
                                       bg='white', fg='#495057',
                                       selectbackground='#007bff',
                                       selectforeground='white',
                                       activestyle='none')
        scrollbar = tk.Scrollbar(listbox_frame, orient='vertical', command=self.query_listbox.yview)
        self.query_listbox.configure(yscrollcommand=scrollbar.set)

        self.query_listbox.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)

        # Right side - Controls (better organized)
        right_frame = tk.Frame(main_frame, bg='#ffffff', relief='solid', bd=1, width=300)
        right_frame.pack(side='right', fill='y', padx=(10, 0))
        right_frame.pack_propagate(False)

        tk.Label(right_frame, text="🛠️ Query Actions", font=('Arial', 14, 'bold'),
                bg='#ffffff', fg='#2c3e50').pack(pady=20)

        # Action buttons - much larger and better spaced
        btn_configs = [
            ("➕ Add New Query", self.add_query, '#28a745'),
            ("✏️ Edit Selected", self.edit_query, '#17a2b8'),
            ("🗑️ Delete Selected", self.delete_query, '#dc3545'),
            ("🔄 Toggle Enable/Disable", self.toggle_query, '#ffc107'),
        ]

        for text, command, color in btn_configs:
            btn = tk.Button(right_frame, text=text, command=command,
                           bg=color, fg='white' if color != '#ffc107' else 'black',
                           font=('Arial', 12, 'bold'), width=20, height=2)
            btn.pack(pady=10, padx=20)

        # Bottom frame - Done button
        bottom_frame = tk.Frame(query_window, bg='#f8f9fa')
        bottom_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(bottom_frame, text="✅ Done & Apply Changes",
                 command=lambda: [self.update_query_preview(), query_window.destroy()],
                 bg='#6c757d', fg='white', font=('Arial', 14, 'bold'),
                 width=25, height=2).pack(side='right')

        # Populate listbox
        self.refresh_query_listbox()

    def refresh_query_listbox(self):
        """Refresh the query listbox"""
        if hasattr(self, 'query_listbox'):
            self.query_listbox.delete(0, tk.END)
            for query in self.custom_queries:
                status = "✅" if query['enabled'] else "❌"
                self.query_listbox.insert(tk.END, f"{status} {query['question']}")

    def add_query(self):
        """Add a new query with spacious design"""
        dialog = self.create_proper_popup("➕ Add New AI Query", 950, 750)

        # Header
        header_frame = tk.Frame(dialog, bg='#28a745', height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="➕ Add New AI Query",
                font=('Arial', 16, 'bold'), fg='white', bg='#28a745').pack(expand=True, pady=15)

        # Main content
        content_frame = tk.Frame(dialog, bg='#f8f9fa')
        content_frame.pack(fill='both', expand=True, padx=30, pady=20)

        tk.Label(content_frame, text="Enter your AI query question:",
                font=('Arial', 14, 'bold'), bg='#f8f9fa', fg='#2c3e50').pack(anchor='w', pady=(0, 10))

        # Smart guidance frame with examples and tips
        guidance_frame = tk.LabelFrame(content_frame, text="💡 Smart Query Assistant",
                                     font=('Arial', 11, 'bold'), bg='#e8f4fd', fg='#0066cc',
                                     relief='solid', bd=1)
        guidance_frame.pack(fill='x', pady=(0, 15))

        guidance_text = """🎯 Use {component} placeholder to make your query dynamic:

✅ Good Examples:
• "What is {component} and provide technical specifications?"
• "What are the main applications for {component}?"
• "Who are reliable manufacturers of {component}?"
• "What is the typical price range for {component}?"

❌ Avoid Static Queries:
• "What is a resistor?" (too specific)
• "Tell me about components" (too generic)

💡 The {component} will be replaced with the actual component name during search."""

        guidance_label = tk.Label(guidance_frame, text=guidance_text, font=('Arial', 10),
                                bg='#e8f4fd', fg='#333333', justify='left', wraplength=600)
        guidance_label.pack(padx=15, pady=10)

        # Quick insert buttons for common patterns
        quick_frame = tk.Frame(content_frame, bg='#f8f9fa')
        quick_frame.pack(fill='x', pady=(0, 10))

        tk.Label(quick_frame, text="🚀 Quick Insert Templates:",
                font=('Arial', 11, 'bold'), bg='#f8f9fa', fg='#495057').pack(anchor='w')

        btn_frame = tk.Frame(quick_frame, bg='#f8f9fa')
        btn_frame.pack(fill='x', pady=5)

        # Large text area
        query_entry = tk.Text(content_frame, height=6, font=('Arial', 12), wrap='word',
                             relief='solid', bd=1, padx=15, pady=10)
        query_entry.pack(fill='both', expand=True, pady=(0, 15))

        # Quick insert templates
        templates = [
            ("📋 Basic Info", "What is {component} and provide a brief technical description?"),
            ("🔧 Applications", "What are the main applications and uses for {component}?"),
            ("📊 Specifications", "What are the key technical specifications for {component}?"),
            ("🏭 Manufacturers", "Who are some reliable manufacturers that make {component}?"),
            ("💰 Pricing", "What is the typical price range for {component}?"),
            ("🔄 Alternatives", "What are good alternatives or substitutes for {component}?")
        ]

        def insert_template(template_text):
            query_entry.delete('1.0', tk.END)
            query_entry.insert('1.0', template_text)
            query_entry.focus_set()

        # Create template buttons in two rows
        for i, (btn_text, template) in enumerate(templates):
            if i < 3:  # First row
                row_frame = btn_frame
            else:  # Second row
                if i == 3:  # Create second row frame
                    btn_frame2 = tk.Frame(quick_frame, bg='#f8f9fa')
                    btn_frame2.pack(fill='x', pady=2)
                row_frame = btn_frame2

            tk.Button(row_frame, text=btn_text,
                     command=lambda t=template: insert_template(t),
                     bg='#17a2b8', fg='white', font=('Arial', 9, 'bold'),
                     width=18, height=1).pack(side='left', padx=2)

        # Real-time validation and assistance
        validation_frame = tk.Frame(content_frame, bg='#f8f9fa')
        validation_frame.pack(fill='x', pady=(0, 15))

        validation_label = tk.Label(validation_frame, text="", font=('Arial', 10, 'bold'),
                                  bg='#f8f9fa', wraplength=600, justify='left')
        validation_label.pack(anchor='w')

        def validate_query(*args):
            """Real-time validation of query text"""
            query_text = query_entry.get('1.0', tk.END).strip()

            if not query_text:
                validation_label.config(text="💭 Start typing your query...", fg='#6c757d')
                return

            # Check for {component} placeholder
            has_placeholder = '{component}' in query_text.lower()

            if has_placeholder:
                validation_label.config(text="✅ Great! Your query includes {component} placeholder and will be dynamic.",
                                      fg='#28a745')
            else:
                validation_label.config(text="⚠️ Consider adding {component} placeholder to make your query dynamic.\n"
                                           "Example: Change 'What is a resistor?' to 'What is {component}?'",
                                      fg='#ffc107')

        # Bind validation to text changes
        def on_text_change(event=None):
            # Small delay to avoid too frequent updates
            content_frame.after(300, validate_query)

        query_entry.bind('<KeyRelease>', on_text_change)
        query_entry.bind('<Button-1>', on_text_change)

        # Button frame
        btn_frame = tk.Frame(content_frame, bg='#f8f9fa')
        btn_frame.pack(fill='x')

        def save_query():
            question = query_entry.get('1.0', tk.END).strip()
            if question:
                new_id = max([q['id'] for q in self.custom_queries], default=0) + 1
                self.custom_queries.append({"id": new_id, "question": question, "enabled": True})
                self.refresh_query_listbox()
                dialog.destroy()
            else:
                messagebox.showwarning("Invalid Input", "Please enter a question.")

        tk.Button(btn_frame, text="💾 Save Query", command=save_query,
                 bg='#28a745', fg='white', font=('Arial', 12, 'bold'),
                 width=15, height=2).pack(side='left', padx=10)

        tk.Button(btn_frame, text="❌ Cancel", command=dialog.destroy,
                 bg='#6c757d', fg='white', font=('Arial', 12, 'bold'),
                 width=15, height=2).pack(side='right', padx=10)

    def edit_query(self):
        """Edit selected query with spacious design"""
        selection = self.query_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a query to edit.")
            return

        query_index = selection[0]
        query = self.custom_queries[query_index]

        dialog = self.create_proper_popup("✏️ Edit AI Query", 950, 750)

        # Header
        header_frame = tk.Frame(dialog, bg='#17a2b8', height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="✏️ Edit AI Query",
                font=('Arial', 16, 'bold'), fg='white', bg='#17a2b8').pack(expand=True, pady=15)

        # Main content
        content_frame = tk.Frame(dialog, bg='#f8f9fa')
        content_frame.pack(fill='both', expand=True, padx=30, pady=20)

        tk.Label(content_frame, text="Edit your AI query question:",
                font=('Arial', 14, 'bold'), bg='#f8f9fa', fg='#2c3e50').pack(anchor='w', pady=(0, 10))

        # Large text area with existing content
        query_entry = tk.Text(content_frame, height=8, font=('Arial', 12), wrap='word',
                             relief='solid', bd=1, padx=15, pady=10)
        query_entry.pack(fill='both', expand=True, pady=(0, 20))
        query_entry.insert('1.0', query['question'])

        # Button frame
        btn_frame = tk.Frame(content_frame, bg='#f8f9fa')
        btn_frame.pack(fill='x')

        def save_changes():
            question = query_entry.get('1.0', tk.END).strip()
            if question:
                self.custom_queries[query_index]['question'] = question
                self.refresh_query_listbox()
                dialog.destroy()
            else:
                messagebox.showwarning("Invalid Input", "Please enter a question.")

        tk.Button(btn_frame, text="💾 Save Changes", command=save_changes,
                 bg='#28a745', fg='white', font=('Arial', 12, 'bold'),
                 width=15, height=2).pack(side='left', padx=10)

        tk.Button(btn_frame, text="❌ Cancel", command=dialog.destroy,
                 bg='#6c757d', fg='white', font=('Arial', 12, 'bold'),
                 width=15, height=2).pack(side='right', padx=10)

    def delete_query(self):
        """Delete selected query"""
        selection = self.query_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a query to delete.")
            return

        if messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this query?"):
            query_index = selection[0]
            del self.custom_queries[query_index]
            self.refresh_query_listbox()

    def toggle_query(self):
        """Toggle query enabled/disabled"""
        selection = self.query_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a query to toggle.")
            return

        query_index = selection[0]
        self.custom_queries[query_index]['enabled'] = not self.custom_queries[query_index]['enabled']
        self.refresh_query_listbox()

    def auto_generate_queries(self):
        """Auto-generate component-specific queries using AI"""
        component = self.component_entry.get().strip()
        if not component:
            messagebox.showwarning("No Component", "Please enter a component name first.")
            return

        if not self.ai_enabled:
            messagebox.showwarning("AI Disabled", "Gemini AI is not available for auto-generation.")
            return

        # Show progress
        progress_dialog = tk.Toplevel(self.root)
        progress_dialog.title("Generating Queries...")
        progress_dialog.geometry("400x150")
        progress_dialog.transient(self.root)
        progress_dialog.grab_set()

        tk.Label(progress_dialog, text="🤖 Generating component-specific queries...",
                font=('Arial', 12, 'bold')).pack(pady=20)

        progress_bar = ttk.Progressbar(progress_dialog, mode='indeterminate')
        progress_bar.pack(fill='x', padx=20, pady=10)
        progress_bar.start()

        def generate_queries():
            try:
                # Ask Gemini for simple, clean questions
                question = f"Generate 5 simple technical questions about {component} for component selection. Each question should be one line, start with 'What' or 'How', end with '?', and use {{component}} placeholder. Format as: 1. Question here? 2. Question here? etc."

                if hasattr(self.gemini_analyzer, 'analyze_component'):
                    response = self.gemini_analyzer.analyze_component(question)
                    response_text = str(response)

                    # Parse response into clean questions
                    new_queries = []
                    import re
                    # Find numbered questions ending with ?
                    pattern = r'\d+\.\s*([^?]+\?)'
                    matches = re.findall(pattern, response_text)

                    for match in matches:
                        clean_question = match.strip()
                        if len(clean_question) > 10 and len(clean_question) < 150:
                            new_queries.append(clean_question)

                    # Add to custom queries
                    if new_queries:
                        for question in new_queries[:5]:  # Limit to 5
                            new_id = max([q['id'] for q in self.custom_queries], default=0) + 1
                            self.custom_queries.append({"id": new_id, "question": question, "enabled": True})

                        self.root.after(0, lambda: [
                            progress_dialog.destroy(),
                            self.update_query_preview(),
                            messagebox.showinfo("Success", f"Generated {len(new_queries)} component-specific queries!")
                        ])
                    else:
                        self.root.after(0, lambda: [
                            progress_dialog.destroy(),
                            messagebox.showwarning("No Queries", "Could not generate specific queries for this component.")
                        ])
                else:
                    self.root.after(0, lambda: [
                        progress_dialog.destroy(),
                        messagebox.showwarning("AI Unavailable", "AI analysis method not available.")
                    ])

            except Exception as e:
                self.root.after(0, lambda: [
                    progress_dialog.destroy(),
                    messagebox.showerror("Error", f"Failed to generate queries: {str(e)}")
                ])

        # Run generation in background thread
        threading.Thread(target=generate_queries, daemon=True).start()

    def save_query_template(self):
        """Save current query configuration as a template"""
        try:
            from tkinter import simpledialog
            from datetime import datetime

            if not self.custom_queries:
                messagebox.showwarning("No Queries", "No queries to save.")
                return

            # Ask for template name
            template_name = simpledialog.askstring(
                "Save Query Template",
                "Enter a name for this template:",
                initialvalue=f"Template_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )

            if not template_name:
                return

            # Create templates directory if it doesn't exist
            templates_dir = "Query_Templates"
            if not os.path.exists(templates_dir):
                os.makedirs(templates_dir)

            # Prepare template data
            template_data = {
                "name": template_name,
                "created_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "app_version": APP_VERSION,
                "queries": self.custom_queries.copy(),
                "description": f"Query template with {len([q for q in self.custom_queries if q['enabled']])} active queries"
            }

            # Save to file
            filename = os.path.join(templates_dir, f"{template_name}.json")
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(template_data, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("✅ Template Saved!",
                              f"Query template saved successfully!\n\n"
                              f"Name: {template_name}\n"
                              f"File: {filename}\n"
                              f"Queries: {len(self.custom_queries)} total, "
                              f"{len([q for q in self.custom_queries if q['enabled']])} active\n\n"
                              f"Use 'Load Template' to restore this configuration later.")

        except Exception as e:
            messagebox.showerror("Save Error", f"Failed to save template: {str(e)}")

    def load_query_template(self):
        """Load a previously saved query template"""
        try:
            # Check if templates directory exists
            templates_dir = "Query_Templates"
            if not os.path.exists(templates_dir):
                messagebox.showwarning("No Templates",
                                     "No templates directory found.\n\n"
                                     "Save a template first using 'Save Template' button.")
                return

            # Check if any templates exist
            template_files = [f for f in os.listdir(templates_dir) if f.endswith('.json')]
            if not template_files:
                messagebox.showwarning("No Templates",
                                     "No saved templates found.\n\n"
                                     "Save a template first using 'Save Template' button.")
                return

            # Show template selection dialog
            self.show_template_selection_dialog(templates_dir, template_files)

        except Exception as e:
            messagebox.showerror("Load Error", f"Failed to load templates: {str(e)}")

    def show_template_selection_dialog(self, templates_dir, template_files):
        """Show dialog to select and load a template"""
        dialog = self.create_proper_popup("📂 Load Query Template", 1050, 750)

        # Header
        header_frame = tk.Frame(dialog, bg='#17a2b8', height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="📂 Load Query Template",
                font=('Arial', 16, 'bold'), fg='white', bg='#17a2b8').pack(expand=True, pady=15)

        # Main content
        content_frame = tk.Frame(dialog, bg='#f8f9fa')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Instructions
        tk.Label(content_frame, text="Select a template to load:",
                font=('Arial', 12), bg='#f8f9fa', fg='#495057').pack(anchor='w', pady=(0, 10))

        # Template list with details
        list_frame = tk.Frame(content_frame, bg='#ffffff', relief='solid', bd=1)
        list_frame.pack(fill='both', expand=True, pady=(0, 20))

        # Scrollable listbox
        listbox_frame = tk.Frame(list_frame)
        listbox_frame.pack(fill='both', expand=True, padx=10, pady=10)

        template_listbox = tk.Listbox(listbox_frame, font=('Arial', 11), height=15)
        scrollbar = tk.Scrollbar(listbox_frame, orient='vertical', command=template_listbox.yview)
        template_listbox.configure(yscrollcommand=scrollbar.set)

        template_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Load template details
        template_data_list = []
        for filename in template_files:
            try:
                filepath = os.path.join(templates_dir, filename)
                with open(filepath, 'r', encoding='utf-8') as f:
                    template_data = json.load(f)

                # Format display text
                name = template_data.get('name', filename.replace('.json', ''))
                created = template_data.get('created_date', 'Unknown')
                query_count = len(template_data.get('queries', []))
                active_count = len([q for q in template_data.get('queries', []) if q.get('enabled', True)])

                display_text = f"{name} | {created} | {active_count}/{query_count} queries"
                template_listbox.insert(tk.END, display_text)
                template_data_list.append((filepath, template_data))

            except Exception as e:
                template_listbox.insert(tk.END, f"❌ {filename} (Error: {str(e)[:30]}...)")
                template_data_list.append((None, None))

        # Preview area
        preview_frame = tk.LabelFrame(content_frame, text="Template Preview",
                                    font=('Arial', 11, 'bold'), bg='#f8f9fa')
        preview_frame.pack(fill='x', pady=(0, 20))

        preview_text = tk.Text(preview_frame, height=8, font=('Arial', 10),
                             bg='#ffffff', fg='#495057', wrap='word',
                             relief='solid', bd=1, state='disabled')
        preview_text.pack(fill='x', padx=10, pady=10)

        def on_template_select(event):
            selection = template_listbox.curselection()
            if selection:
                index = selection[0]
                filepath, template_data = template_data_list[index]

                if template_data:
                    preview_text.config(state='normal')
                    preview_text.delete('1.0', tk.END)

                    preview_content = f"Template: {template_data.get('name', 'Unknown')}\n"
                    preview_content += f"Created: {template_data.get('created_date', 'Unknown')}\n"
                    preview_content += f"App Version: {template_data.get('app_version', 'Unknown')}\n"
                    preview_content += f"Description: {template_data.get('description', 'No description')}\n\n"
                    preview_content += "Queries:\n"

                    for i, query in enumerate(template_data.get('queries', []), 1):
                        status = "✅" if query.get('enabled', True) else "❌"
                        preview_content += f"{i}. {status} {query.get('question', 'No question')}\n"

                    preview_text.insert('1.0', preview_content)
                    preview_text.config(state='disabled')

        template_listbox.bind('<<ListboxSelect>>', on_template_select)

        # Buttons
        btn_frame = tk.Frame(content_frame, bg='#f8f9fa')
        btn_frame.pack(fill='x')

        def load_selected_template():
            selection = template_listbox.curselection()
            if not selection:
                messagebox.showwarning("No Selection", "Please select a template to load.")
                return

            index = selection[0]
            filepath, template_data = template_data_list[index]

            if not template_data:
                messagebox.showerror("Invalid Template", "Selected template is corrupted or invalid.")
                return

            # Confirm loading
            result = messagebox.askyesno("Confirm Load",
                                       f"Load template '{template_data.get('name', 'Unknown')}'?\n\n"
                                       f"This will replace your current {len(self.custom_queries)} queries "
                                       f"with {len(template_data.get('queries', []))} template queries.\n\n"
                                       f"Current queries will be lost unless saved as a template first.")

            if result:
                # Load the template
                self.custom_queries = template_data.get('queries', []).copy()
                self.update_query_preview()
                dialog.destroy()

                messagebox.showinfo("✅ Template Loaded!",
                                  f"Template '{template_data.get('name', 'Unknown')}' loaded successfully!\n\n"
                                  f"Loaded {len(self.custom_queries)} queries, "
                                  f"{len([q for q in self.custom_queries if q.get('enabled', True)])} active.\n\n"
                                  f"Query preview has been updated.")

        def rename_selected_template():
            selection = template_listbox.curselection()
            if not selection:
                messagebox.showwarning("No Selection", "Please select a template to rename.")
                return

            index = selection[0]
            filepath, template_data = template_data_list[index]

            if not template_data:
                messagebox.showerror("Invalid Template", "Selected template is corrupted or invalid.")
                return

            # Ask for new name
            from tkinter import simpledialog
            current_name = template_data.get('name', 'Unknown')
            new_name = simpledialog.askstring(
                "Rename Template",
                f"Enter new name for template:\n\nCurrent name: {current_name}",
                initialvalue=current_name
            )

            if not new_name or new_name == current_name:
                return

            try:
                # Update template data
                template_data['name'] = new_name

                # Create new filename
                new_filename = os.path.join(templates_dir, f"{new_name}.json")

                # Check if new filename already exists
                if os.path.exists(new_filename) and new_filename != filepath:
                    result = messagebox.askyesno("File Exists",
                                               f"A template named '{new_name}' already exists.\n\n"
                                               f"Do you want to overwrite it?")
                    if not result:
                        return

                # Save with new name
                with open(new_filename, 'w', encoding='utf-8') as f:
                    json.dump(template_data, f, indent=2, ensure_ascii=False)

                # Delete old file if filename changed
                if new_filename != filepath:
                    os.remove(filepath)

                # Refresh the template list
                dialog.destroy()
                self.load_query_template()  # Reopen the dialog with updated list

                messagebox.showinfo("✅ Template Renamed!",
                                  f"Template renamed successfully!\n\n"
                                  f"Old name: {current_name}\n"
                                  f"New name: {new_name}")

            except Exception as e:
                messagebox.showerror("Rename Error", f"Failed to rename template: {str(e)}")

        def delete_selected_template():
            selection = template_listbox.curselection()
            if not selection:
                messagebox.showwarning("No Selection", "Please select a template to delete.")
                return

            index = selection[0]
            filepath, template_data = template_data_list[index]

            if not template_data:
                messagebox.showerror("Invalid Template", "Selected template is corrupted or invalid.")
                return

            template_name = template_data.get('name', 'Unknown')

            # Confirm deletion
            result = messagebox.askyesno("Confirm Delete",
                                       f"Are you sure you want to delete template:\n\n"
                                       f"'{template_name}'?\n\n"
                                       f"This action cannot be undone.")

            if result:
                try:
                    os.remove(filepath)

                    # Refresh the template list
                    dialog.destroy()
                    self.load_query_template()  # Reopen the dialog with updated list

                    messagebox.showinfo("✅ Template Deleted!",
                                      f"Template '{template_name}' deleted successfully!")

                except Exception as e:
                    messagebox.showerror("Delete Error", f"Failed to delete template: {str(e)}")

        tk.Button(btn_frame, text="📂 Load Selected", command=load_selected_template,
                 bg='#28a745', fg='white', font=('Arial', 11, 'bold'),
                 width=12, height=2).pack(side='left', padx=5)

        tk.Button(btn_frame, text="✏️ Rename", command=rename_selected_template,
                 bg='#ffc107', fg='black', font=('Arial', 11, 'bold'),
                 width=12, height=2).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🗑️ Delete", command=delete_selected_template,
                 bg='#dc3545', fg='white', font=('Arial', 11, 'bold'),
                 width=12, height=2).pack(side='left', padx=5)

        tk.Button(btn_frame, text="❌ Cancel", command=dialog.destroy,
                 bg='#6c757d', fg='white', font=('Arial', 11, 'bold'),
                 width=12, height=2).pack(side='right', padx=5)

if __name__ == "__main__":
    app = ComponentSearcher()
    app.run()
