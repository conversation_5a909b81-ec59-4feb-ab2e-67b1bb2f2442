import cv2
import numpy as np

class OcrEngine:
    def __init__(self):
        """
        Initializes the 7-segment display OCR engine.
        """
        # This dictionary maps the on/off state of the 7 segments to a digit.
        # The order of segments is: Top, Top-Left, Top-Right, Middle, Bottom-Left, Bottom-Right, Bottom
        self.DIGIT_MAP = {
            (1, 1, 1, 0, 1, 1, 1): "0",
            (0, 0, 1, 0, 0, 1, 0): "1",
            (1, 0, 1, 1, 1, 0, 1): "2",
            (1, 0, 1, 1, 0, 1, 1): "3",
            (0, 1, 1, 1, 0, 1, 0): "4",
            (1, 1, 0, 1, 0, 1, 1): "5",
            (1, 1, 0, 1, 1, 1, 1): "6",
            (1, 0, 1, 0, 0, 1, 0): "7",
            (1, 1, 1, 1, 1, 1, 1): "8",
            (1, 1, 1, 1, 0, 1, 1): "9",
            (0, 0, 0, 1, 0, 0, 0): "-",
        }
        # Placeholder for the display's location in the camera feed.
        # These points would be calibrated once. (Top-Left, Top-Right, Bottom-Right, Bottom-Left)
        self.display_roi_pts = None
        self.calibrated = False

    def calibrate(self, frame):
        """
        Finds the display in the frame automatically.
        For a real application, this might be a manual calibration step.
        Here, we'll try to find a large, bright rectangle.
        """
        # A simple auto-calibration based on finding the largest contour
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        thresh = cv2.threshold(blurred, 60, 255, cv2.THRESH_BINARY)[1]
        
        contours, _ = cv2.findContours(thresh.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            print("Calibration failed: No contours found.")
            return False

        display_contour = max(contours, key=cv2.contourArea)
        
        # Get bounding box and set as ROI
        rect = cv2.minAreaRect(display_contour)
        box = cv2.boxPoints(rect)
        self.display_roi_pts = np.intp(box)
        
        # Reorder points to be TL, TR, BR, BL for perspective transform
        rect = np.zeros((4, 2), dtype="float32")
        s = self.display_roi_pts.sum(axis=1)
        rect[0] = self.display_roi_pts[np.argmin(s)]
        rect[2] = self.display_roi_pts[np.argmax(s)]
        
        diff = np.diff(self.display_roi_pts, axis=1)
        rect[1] = self.display_roi_pts[np.argmin(diff)]
        rect[3] = self.display_roi_pts[np.argmax(diff)]
        
        self.display_roi_pts = rect
        self.calibrated = True
        print(f"Calibration successful. ROI points set.")
        return True

    def recognize(self, frame):
        """
        Takes a full camera frame and returns the recognized number as a string.
        """
        if not self.calibrated:
            if not self.calibrate(frame):
                return None, frame # Return original frame if calibration fails

        # Apply perspective transform to get a top-down view of the display
        (tl, tr, br, bl) = self.display_roi_pts
        widthA = np.sqrt(((br[0] - bl[0]) ** 2) + ((br[1] - bl[1]) ** 2))
        widthB = np.sqrt(((tr[0] - tl[0]) ** 2) + ((tr[1] - tl[1]) ** 2))
        maxWidth = max(int(widthA), int(widthB))

        heightA = np.sqrt(((tr[0] - br[0]) ** 2) + ((tr[1] - br[1]) ** 2))
        heightB = np.sqrt(((tl[0] - bl[0]) ** 2) + ((tl[1] - bl[1]) ** 2))
        maxHeight = max(int(heightA), int(heightB))

        dst = np.array([
            [0, 0],
            [maxWidth - 1, 0],
            [maxWidth - 1, maxHeight - 1],
            [0, maxHeight - 1]], dtype="float32")

        M = cv2.getPerspectiveTransform(self.display_roi_pts, dst)
        warped = cv2.warpPerspective(frame, M, (maxWidth, maxHeight))
        
        # Draw the ROI on the original frame for visualization
        cv2.drawContours(frame, [self.display_roi_pts.astype("int")], -1, (0, 255, 0), 2)

        # Preprocess the warped, top-down image
        output_gray = cv2.cvtColor(warped, cv2.COLOR_BGR2GRAY)
        output_thresh = cv2.threshold(output_gray, 0, 255, cv2.THRESH_BINARY_INV | cv2.THRESH_OTSU)[1]

        # TODO: Logic to segment the display into individual digits and recognize each one.
        # This is a placeholder for now.
        
        return "12.34", frame # Placeholder return