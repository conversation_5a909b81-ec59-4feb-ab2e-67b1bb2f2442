#!/usr/bin/env python3
"""
Test Gemini API with Correct Model Name
Fixed version that uses the correct Gemini model name
"""

import json

def test_gemini_key_fixed():
    """Test the Gemini API key with correct model name"""
    print("🔑 Testing Your Gemini API Key (Fixed)")
    print("=" * 40)
    
    # Your API key
    api_key = "AIzaSyAHl-ePSQ6RqY2wi0IUj17JRcoALefRmlg"
    
    try:
        # Import and configure Gemini
        import google.generativeai as genai
        genai.configure(api_key=api_key)
        
        print("✅ Gemini API configured with your key")
        
        # First, let's see what models are available
        print("\n🔍 Checking available models...")
        try:
            models = genai.list_models()
            available_models = []
            for model in models:
                if 'generateContent' in model.supported_generation_methods:
                    available_models.append(model.name)
                    print(f"📋 Available: {model.name}")
            
            if not available_models:
                print("❌ No models available for content generation")
                return False
            
            # Use the first available model
            model_name = available_models[0]
            print(f"✅ Using model: {model_name}")
            
        except Exception as e:
            print(f"⚠️ Could not list models: {e}")
            # Try common model names
            model_names_to_try = [
                "gemini-1.5-flash",
                "gemini-1.5-pro", 
                "gemini-pro",
                "models/gemini-1.5-flash",
                "models/gemini-1.5-pro",
                "models/gemini-pro"
            ]
            
            model_name = None
            for name in model_names_to_try:
                try:
                    test_model = genai.GenerativeModel(name)
                    test_response = test_model.generate_content("test")
                    model_name = name
                    print(f"✅ Found working model: {model_name}")
                    break
                except Exception:
                    continue
            
            if not model_name:
                print("❌ Could not find a working model")
                return False
        
        # Create model
        model = genai.GenerativeModel(model_name)
        
        # Test 1: Simple connection test
        print("\n🧪 Test 1: Simple connection test...")
        response = model.generate_content("Hello! Please respond with 'Gemini API test successful'")
        print(f"📝 Response: {response.text}")
        
        # Test 2: Component analysis
        print("\n🧪 Test 2: Component analysis test...")
        component = "arduino uno"
        
        prompt = f"""
Analyze this electronics component: "{component}"

Provide a structured analysis with:
1. Component Type
2. Manufacturer  
3. Key Specifications
4. Common Package/Form Factor
5. Typical Applications
6. Compatible Alternatives

Keep response concise and technical.
"""
        
        response = model.generate_content(prompt)
        print(f"📋 Component Analysis for '{component}':")
        print("-" * 50)
        print(response.text)
        print("-" * 50)
        
        # Test 3: Quick component analysis
        print("\n🧪 Test 3: Quick component analysis...")
        
        test_components = ["10k resistor", "LM358 op amp", "ESP32"]
        
        for comp in test_components:
            print(f"\n🔍 Quick analysis: {comp}")
            quick_prompt = f"Briefly describe this electronics component: {comp}. Include type, typical use, and package in 2-3 sentences."
            
            response = model.generate_content(quick_prompt)
            print(f"📝 {response.text}")
        
        # Save the working configuration
        config = {
            "gemini_api_key": api_key,
            "working_model": model_name,
            "test_date": "2025-01-27",
            "status": "working"
        }
        
        with open("gemini_config.json", "w") as f:
            json.dump(config, f, indent=2)
        
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ Your Gemini API key is working perfectly")
        print(f"🤖 Using model: {model_name}")
        print(f"💾 Configuration saved to gemini_config.json")
        print(f"🚀 Ready to integrate with component search application")
        
        return True
        
    except ImportError:
        print("❌ Google Generative AI library not installed")
        print("📦 Install with: pip install google-generativeai")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print(f"🔧 This might be a temporary API issue or model name change")
        return False

def show_next_steps():
    """Show what happens next"""
    print(f"\n🚀 NEXT STEPS")
    print(f"=" * 15)
    
    print(f"Now that your Gemini API key works, I will:")
    print(f"")
    print(f"1. 🔧 Create Gemini-powered AI analyzer")
    print(f"2. 🤖 Replace failed web automation with API calls")
    print(f"3. 📊 Add intelligent component classification")
    print(f"4. 🏭 Implement AI-driven manufacturer recommendations")
    print(f"5. 🔍 Enhance search with AI insights")
    print(f"6. 📈 Add AI-based quality scoring")
    print(f"")
    print(f"Your component search will have:")
    print(f"• ⚡ Fast AI analysis (2-3 seconds)")
    print(f"• 🆓 Free usage (1,500 requests/day)")
    print(f"• 🧠 Smart component understanding")
    print(f"• 📋 Structured data extraction")
    print(f"• 🔄 Reliable service (official Google API)")
    print(f"• 🎯 Professional-grade component analysis")

if __name__ == "__main__":
    print("🚀 Testing Your Gemini API Key (Fixed Version)")
    print("=" * 50)
    
    if test_gemini_key_fixed():
        show_next_steps()
        
        print(f"\n🎯 SUCCESS!")
        print(f"Your Gemini API key works perfectly.")
        print(f"Ready to build the AI-powered component search application!")
    else:
        print(f"\n❌ API key test failed")
        print(f"This might be a temporary issue with the Gemini API.")
        print(f"Let's try a different approach or check the API status.")
    
    input(f"\nPress Enter to continue...")
