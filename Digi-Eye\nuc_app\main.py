import customtkinter as ctk
import threading
from PIL import Image, ImageTk
import time
from camera_thread import CameraThread
from serial_thread import SerialThread

class DigiEyeApp(ctk.CTk):
    def __init__(self):
        super().__init__()

        # ---- Window Setup ----
        self.title("Project Digi-Eye: Control & Recognition")
        self.geometry("1200x700")
        self._set_appearance_mode("System") # Modes: "System" (default), "Dark", "Light"

        # ---- Layout Setup ----
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # ---- Frames ----
        # Left Frame for Camera Feed
        self.camera_frame = ctk.CTkFrame(self, width=800, corner_radius=0)
        self.camera_frame.grid(row=0, column=0, sticky="nsew")
        self.camera_frame.grid_propagate(False) # Prevent frame from shrinking to fit content

        # Right Frame for Controls
        self.control_frame = ctk.CTkFrame(self, width=400, corner_radius=0)
        self.control_frame.grid(row=0, column=1, sticky="nsew", padx=10, pady=10)
        self.control_frame.grid_propagate(False)

        # ---- Widgets ----
        self.create_control_widgets()
        self.create_camera_widgets()

        # ---- App State ----
        self.threads = []
        self.stop_event = threading.Event()
        self.serial_thread = None

        print("Digi-Eye Application Initialized.")
        self.start_serial_thread()

    def create_control_widgets(self):
        """Create all widgets for the right-hand control panel."""
        self.control_frame.grid_columnconfigure(0, weight=1)
        
        # Title
        title_label = ctk.CTkLabel(self.control_frame, text="Digi-Eye Controls", font=ctk.CTkFont(size=20, weight="bold"))
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10))

        # Status Section
        status_frame = ctk.CTkFrame(self.control_frame)
        status_frame.grid(row=1, column=0, padx=20, pady=10, sticky="ew")
        status_frame.grid_columnconfigure(1, weight=1)
        
        ctk.CTkLabel(status_frame, text="Display Status:").grid(row=0, column=0, padx=10, pady=5, sticky="w")
        self.display_status_label = ctk.CTkLabel(status_frame, text="Disconnected", text_color="red")
        self.display_status_label.grid(row=0, column=1, padx=10, pady=5, sticky="w")

        ctk.CTkLabel(status_frame, text="Camera Status:").grid(row=1, column=0, padx=10, pady=5, sticky="w")
        self.camera_status_label = ctk.CTkLabel(status_frame, text="Not Started", text_color="red")
        self.camera_status_label.grid(row=1, column=1, padx=10, pady=5, sticky="w")

        # Recognized Value Section
        rec_frame = ctk.CTkFrame(self.control_frame)
        rec_frame.grid(row=2, column=0, padx=20, pady=20, sticky="ew")
        rec_frame.grid_columnconfigure(0, weight=1)

        ctk.CTkLabel(rec_frame, text="Recognized Value:", font=ctk.CTkFont(size=16)).pack(pady=(10,0))
        self.recognized_value_label = ctk.CTkLabel(rec_frame, text="--.--", font=ctk.CTkFont(size=48, weight="bold"))
        self.recognized_value_label.pack(pady=10, padx=10)

        # Control Buttons
        self.start_camera_button = ctk.CTkButton(self.control_frame, text="Start Camera", command=self.start_camera)
        self.start_camera_button.grid(row=3, column=0, padx=20, pady=10, sticky="ew")

        # Sequence Controls
        seq_frame = ctk.CTkFrame(self.control_frame)
        seq_frame.grid(row=4, column=0, padx=20, pady=10, sticky="ew")
        seq_frame.grid_columnconfigure((0,1,2), weight=1)
        ctk.CTkLabel(seq_frame, text="Sequence Control").grid(row=0, column=0, columnspan=3, pady=(5,0))
        
        self.count_up_button = ctk.CTkButton(seq_frame, text="Count Up", command=lambda: self.send_serial_command("U100"))
        self.count_up_button.grid(row=1, column=0, padx=5, pady=10)
        
        self.count_down_button = ctk.CTkButton(seq_frame, text="Count Down", command=lambda: self.send_serial_command("D100"))
        self.count_down_button.grid(row=1, column=1, padx=5, pady=10)

        self.random_button = ctk.CTkButton(seq_frame, text="Random", command=lambda: self.send_serial_command("R"))
        self.random_button.grid(row=1, column=2, padx=5, pady=10)

        self.pause_button = ctk.CTkButton(seq_frame, text="Pause", command=lambda: self.send_serial_command("P"))
        self.pause_button.grid(row=2, column=0, columnspan=3, padx=5, pady=(0,10), sticky="ew")

        # Manual Input
        manual_frame = ctk.CTkFrame(self.control_frame)
        manual_frame.grid(row=5, column=0, padx=20, pady=10, sticky="ew")
        manual_frame.grid_columnconfigure(0, weight=1)
        ctk.CTkLabel(manual_frame, text="Manual Input").pack()

        self.manual_entry = ctk.CTkEntry(manual_frame, placeholder_text="Enter a number...")
        self.manual_entry.pack(fill="x", expand=True, padx=10, pady=5)
        
        self.send_manual_button = ctk.CTkButton(manual_frame, text="Send to Display", command=self.send_manual_value)
        self.send_manual_button.pack(fill="x", expand=True, padx=10, pady=(0,10))

    def create_camera_widgets(self):
        """Create widgets for the left-hand camera feed."""
        self.camera_label = ctk.CTkLabel(self.camera_frame, text="Camera Feed Will Appear Here")
        self.camera_label.pack(expand=True)

    def start_camera(self):
        """Starts the camera thread."""
        if any(isinstance(t, CameraThread) and t.is_alive() for t in self.threads):
            print("Camera thread is already running.")
            return

        print("Starting camera thread...")
        cam_thread = CameraThread(self, camera_index=0)
        self.threads.append(cam_thread)
        cam_thread.start()
        self.start_camera_button.configure(text="Stop Camera", command=self.stop_camera)

    def stop_camera(self):
        """Stops the camera thread."""
        print("Stopping camera thread...")
        self.stop_event.set() # Signal all threads to stop
        # Wait for threads to finish
        for t in self.threads:
            t.join()
        self.threads.clear()
        self.stop_event.clear() # Reset event for next time
        
        self.camera_label.configure(image=None, text="Camera Feed Stopped")
        self.camera_status_label.configure(text="Stopped", text_color="orange")
        self.start_camera_button.configure(text="Start Camera", command=self.start_camera)

    def start_serial_thread(self):
        """Starts the serial communication thread."""
        if self.serial_thread and self.serial_thread.is_alive():
            print("Serial thread is already running.")
            return
        print("Starting serial thread...")
        self.serial_thread = SerialThread(self)
        self.threads.append(self.serial_thread)
        self.serial_thread.start()

    def send_serial_command(self, command):
        """Safely sends a command to the serial thread."""
        if self.serial_thread and self.serial_thread.is_alive():
            self.serial_thread.send_command(command)
        else:
            print("Cannot send command, serial thread is not running.")
            self.display_status_label.configure(text="Not Connected", text_color="red")

    def send_manual_value(self):
        """Sends the value from the manual entry box."""
        value = self.manual_entry.get()
        if value:
            # Basic validation can be added here
            self.send_serial_command(f"S{value}")
        else:
            print("Manual entry is empty.")

    def on_closing(self):
        """Handle window closing event."""
        print("Closing application...")
        self.stop_event.set()
        for t in self.threads:
            t.join()
        self.destroy()

if __name__ == "__main__":
    app = DigiEyeApp()
    app.protocol("WM_DELETE_WINDOW", app.on_closing)
    app.mainloop()