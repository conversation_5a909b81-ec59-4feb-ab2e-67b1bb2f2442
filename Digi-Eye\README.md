# Project Digi-Eye

This project is a proof-of-concept system for controlling a 7-segment display with a Raspberry Pi Pico and using a Python application on a host PC to read the display's value via a camera in real-time.

## Project Structure

- `/nuc_app`: Contains the Python application that runs on the host PC (NUC).
  - `main.py`: The main entry point for the GUI application.
  - `camera_thread.py`: Manages the camera feed and OCR processing.
  - `serial_thread.py`: Manages serial communication with the RP2040.
  - `ocr_engine.py`: Contains the computer vision logic for recognizing digits.
  - `requirements.txt`: A list of required Python packages.
- `/rp2040_firmware`: Contains the C/C++ source code for the Raspberry Pi Pico.
  - `main.c`: The main firmware logic.

---

## 1. NUC Application Setup (Windows)

This application runs the user interface and the core recognition logic.

### Prerequisites
- Python 3.8+

### Installation
1.  **Open a terminal or command prompt.**
2.  **Navigate to the `nuc_app` directory:**
    ```bash
    cd path/to/Digi-Eye/nuc_app
    ```
3.  **Create a Python virtual environment:**
    ```bash
    python -m venv venv
    ```
4.  **Activate the virtual environment:**
    ```bash
    .\venv\Scripts\activate
    ```
5.  **Install the required packages:**
    ```bash
    pip install -r requirements.txt
    ```

### Running the Application
With the virtual environment still active, run the main script:
```bash
python main.py
```

---

## 2. RP2040 Firmware Setup

This firmware runs on the Raspberry Pi Pico to control the display.

### Prerequisites
You must have the Raspberry Pi Pico C/C++ SDK configured on your system. If you do not, please follow the official guide:
- [Getting started with Raspberry Pi Pico](https://datasheets.raspberrypi.com/pico/getting-started-with-pico.pdf) (Chapter 2 for command-line setup).

### Building the Firmware
1.  **Create a `build` directory** inside `/rp2040_firmware`.
2.  **Create a `CMakeLists.txt` file** in the `/rp2040_firmware` directory with the appropriate project setup (a minimal example is provided below).
3.  **Run CMake and Make** from the `build` directory to compile the code. This will generate a `main.uf2` file.
4.  **Flash the `.uf2` file** to your Pico by holding the `BOOTSEL` button while plugging it in, then dragging the file to the `RPI-RP2` drive that appears.

### Minimal `CMakeLists.txt` for RP2040
```cmake
cmake_minimum_required(VERSION 3.13)

# Initialize the Pico SDK from the path in your environment variables
include(pico_sdk_import.cmake)

project(digi_eye_firmware C CXX ASM)
set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 17)

pico_sdk_init()

add_executable(main
    main.c
)

# Enable USB support
pico_enable_stdio_usb(main 1)
pico_enable_stdio_uart(main 0)

# Add the pico_stdlib library
target_link_libraries(main pico_stdlib)

# Create a .uf2 file
pico_add_extra_outputs(main)
```

---

## 3. How to Run the Demo

1.  Ensure the firmware has been built and flashed to the RP2040.
2.  Connect the RP2040 to the NUC via USB.
3.  Connect the endoscope camera to the NUC via USB.
4.  Run the NUC application as described above.
5.  Click the "Start Camera" button. You should see the live camera feed.
6.  The application will attempt to automatically connect to the Pico. Check the "Display Status" label.
7.  Use the control buttons to send commands to the display and watch the OCR engine recognize the numbers in real-time.