{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        
        
        {
            "name": "Cortex-Debug",
            "cwd": "${workspaceRoot}",
            "request": "launch",
            "type": "cortex-debug",
            "servertype": "openocd",
            "device": "RP2040",
            "runToEntryPoint": "main",
            "configFiles": ["interface/picoprobe.cfg", "target/rp2040.cfg"],
            "searchDir": ["C:/Users/<USER>/pico/openocd/tcl"],
            "svdFile": "C:/Users/<USER>/pico/pico-sdk/src/rp2040/hardware_regs/rp2040.svd",
            "executable": "${workspaceRoot}\\build\\7-segment.elf",
            //"executable": "C:\\Myproject\\Blink.ino.elf",
            "postRestartCommands": [
                //"continue",
                "reset exit",            
            ]
        }
    ]
}