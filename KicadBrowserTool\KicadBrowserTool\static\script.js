// P:\KiCadProjectBrowserTool\static\script.js

const UI_VERSION = "0.6.1"; // Incremented for click listener debug
console.log(`KiCad Project Browser: JavaScript execution started. UI v${UI_VERSION}`);

const API_BASE_URL = 'http://127.0.0.1:5000';
let fullDataFromApi = { metadata: {}, projects: [] }; 
let eventSource = null; 
let foundProjectsDuringScanCount = 0;
let originalRefreshButtonHTML = ''; 

let currentSortColumn = 'last_modified'; 
let currentSortDirection = 'desc';      

const mainDisplayArea = document.getElementById('mainDisplayArea');
const projectsTable = document.getElementById('projectsTable');
const tableHeaderRow = projectsTable.querySelector('thead tr'); 
const tableBody = projectsTable.querySelector('tbody');
const searchInput = document.getElementById('searchInput');
const refreshButton = document.getElementById('refreshButton');
const statusMessagesContainer = document.getElementById('statusMessages');
const loadingMessage = document.getElementById('loadingMessage');
const errorMessage = document.getElementById('errorMessage');
const noProjectsMessage = document.getElementById('noProjectsMessage');
const noResultsMessage = document.getElementById('noResultsMessage');
const projectCountSpan = document.getElementById('projectCount');
const toolVersionInfoSpan = document.getElementById('toolVersionInfo');
const scanInfoSpan = document.getElementById('scanInfo');
const scanInProgressUIDiv = document.getElementById('scanInProgressUI');
const scanStatusOutputDiv = document.getElementById('scanStatusOutput');
const scanStatusListUl = document.getElementById('scanStatusList');
const scanFoundProjectsOutputDiv = document.getElementById('scanFoundProjectsOutput');
const scanFoundProjectsListUl = document.getElementById('scanFoundProjectsList');
const foundProjectsCountSpan = document.getElementById('foundProjectsCount');
const acceptProjectsButton = document.getElementById('acceptProjectsButton');

if (refreshButton) {
    originalRefreshButtonHTML = refreshButton.innerHTML;
}

function showMainDisplay(areaToShow) { 
    projectsTable.style.display = 'none';
    statusMessagesContainer.style.display = 'none';
    scanInProgressUIDiv.style.display = 'none';
    if (areaToShow === 'table') projectsTable.style.display = 'table';
    else if (areaToShow === 'status') statusMessagesContainer.style.display = 'block';
    else if (areaToShow === 'scan_ui') scanInProgressUIDiv.style.display = 'block';
}

function showStatusMessage(type, messageText = '') { 
    loadingMessage.style.display = 'none'; errorMessage.style.display = 'none';
    noProjectsMessage.style.display = 'none'; noResultsMessage.style.display = 'none';
    let activeMessageElement; let defaultText = '';
    switch (type) {
        case 'loading': activeMessageElement = loadingMessage; defaultText = 'Loading projects...';
            const loadingTextSpan = loadingMessage.querySelector('span');
            if (loadingTextSpan) loadingTextSpan.textContent = messageText || defaultText;
            break;
        case 'error': activeMessageElement = errorMessage; errorMessage.textContent = messageText || 'An error occurred.'; break;
        case 'no_projects': activeMessageElement = noProjectsMessage; noProjectsMessage.textContent = messageText || 'No projects found. Click "Refresh Projects" to perform initial scan.'; break;
        case 'no_results': activeMessageElement = noResultsMessage; noResultsMessage.textContent = messageText || 'No projects match your search criteria.'; break;
        default: console.warn("showStatusMessage called with invalid type:", type); return;
    }
    if (activeMessageElement) activeMessageElement.style.display = 'block';
    showMainDisplay('status');
}

function updateInfoDisplay() { 
    if (toolVersionInfoSpan) toolVersionInfoSpan.textContent = `UI v${UI_VERSION}`;
    if (scanInfoSpan && fullDataFromApi.metadata && fullDataFromApi.metadata.data_exists !== false) {
        const meta = fullDataFromApi.metadata;
        let scanText = `Data as of: ${meta.scan_timestamp_utc ? new Date(meta.scan_timestamp_utc).toLocaleString() : 'N/A'}`;
        if (meta.server_script_version) scanText += ` (Server v${meta.server_script_version})`;
        if (typeof meta.scan_duration_seconds === 'number') scanText += ` | Scan took: ${meta.scan_duration_seconds}s`;
        if (typeof meta.directories_scanned === 'number') scanText += ` | Dirs: ${meta.directories_scanned}`;
        scanInfoSpan.textContent = scanText;
    } else if (scanInfoSpan) {
        scanInfoSpan.textContent = 'Scan information not available or index is empty.';
    }
}

function processFinalDataAndRenderTable(data) { 
    fullDataFromApi = data;
    console.log("KiCad Project Browser: Full data processed. Projects count:",
                (fullDataFromApi.projects ? fullDataFromApi.projects.length : 'N/A'),
                "Metadata:", fullDataFromApi.metadata);
    updateInfoDisplay();
    if (!fullDataFromApi.projects || !Array.isArray(fullDataFromApi.projects) || fullDataFromApi.projects.length === 0) {
        showStatusMessage('no_projects', 'Scan complete, but no projects were found.');
        projectCountSpan.textContent = '0 projects';
    } else {
        sortProjects(currentSortColumn, currentSortDirection, false); 
        renderTable(fullDataFromApi.projects); 
    }
}

async function fetchInitialProjectData() { 
    console.log("KiCad Project Browser: fetchInitialProjectData() called.");
    showStatusMessage('loading', 'Fetching existing project data...');
    if (refreshButton) refreshButton.disabled = true; if (searchInput) searchInput.disabled = true;
    try {
        const response = await fetch(`${API_BASE_URL}/api/projects`);
        if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
        const data = await response.json();
        if (data.metadata && data.metadata.data_exists === false) {
            showStatusMessage('no_projects', data.metadata.error || "Project data not found. Click 'Refresh Projects' to perform initial scan.");
            updateInfoDisplay(); if(projectCountSpan) projectCountSpan.textContent = '';
        } else { processFinalDataAndRenderTable(data); }
    } catch (error) {
        showStatusMessage('error', `Failed to load initial project data: ${error.message}`);
        updateInfoDisplay();
    } finally {
        if (refreshButton) refreshButton.disabled = false; if (searchInput) searchInput.disabled = false;
    }
}

function addScanProgressMessage(listUl, message, typeClass = '') { 
    if (!listUl) return; const li = document.createElement('li'); li.textContent = message;
    if (typeClass) li.classList.add(typeClass); listUl.appendChild(li);
    if (listUl.parentElement && listUl.parentElement.classList.contains('scan-output-box')) {
        listUl.parentElement.scrollTop = listUl.parentElement.scrollHeight;
    }
}

function finalizeScanUIState() { 
    if (refreshButton) { refreshButton.innerHTML = originalRefreshButtonHTML; refreshButton.disabled = false; }
    if (searchInput) searchInput.disabled = false;
}

function startScanProcess() { 
    console.log("KiCad Project Browser: startScanProcess() initiated.");
    if (eventSource) eventSource.close();
    showMainDisplay('scan_ui');
    if(scanStatusListUl) scanStatusListUl.innerHTML = ''; if(scanFoundProjectsListUl) scanFoundProjectsListUl.innerHTML = '';
    foundProjectsDuringScanCount = 0; if(foundProjectsCountSpan) foundProjectsCountSpan.textContent = '0';
    if(acceptProjectsButton) { acceptProjectsButton.style.display = 'none'; acceptProjectsButton.disabled = true; }
    if(refreshButton) refreshButton.disabled = true; if(searchInput) searchInput.disabled = true;
    if (refreshButton) {
        refreshButton.innerHTML = `<svg viewBox="0 0 20 20" fill="currentColor" class="animate-spin-manual" style="width:16px; height:16px; margin-right:8px;"><path d="M10 3a7 7 0 100 14 7 7 0 000-14zM3 10a7 7 0 1114 0A7 7 0 013 10z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path><path d="M10 18a8 8 0 100-16 8 8 0 000 16zM9 7a1 1 0 012 0v3.586l1.707 1.707a1 1 0 01-1.414 1.414L10 11.414V7z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg>Scanning...`;
    }
    addScanProgressMessage(scanStatusListUl, "Attempting to connect to scan stream...", "status-message");
    eventSource = new EventSource(`${API_BASE_URL}/api/scan_progress_stream`);
    eventSource.onopen = function() { addScanProgressMessage(scanStatusListUl, "Connection opened. Scan starting...", "status-message"); };
    eventSource.onmessage = function(event) {
        const data = JSON.parse(event.data);
        if (data.type === 'status') { addScanProgressMessage(scanStatusListUl, data.message, "status-message"); }
        else if (data.type === 'found_project') {
            foundProjectsDuringScanCount++; if(foundProjectsCountSpan) foundProjectsCountSpan.textContent = foundProjectsDuringScanCount.toString();
            addScanProgressMessage(scanFoundProjectsListUl, `(${foundProjectsDuringScanCount}) ${data.sku} - ${data.description.substring(0,70)}... (${data.path_display.substring(0,30)})`, "found-project-item");
        } else if (data.type === 'error') { addScanProgressMessage(scanStatusListUl, `SERVER ERROR: ${data.message}`, "error-message"); }
        else if (data.type === 'final_data') {
            addScanProgressMessage(scanStatusListUl, "Scan complete. Final data received.", "status-message");
            fullDataFromApi = data.payload; 
            if(acceptProjectsButton) { acceptProjectsButton.style.display = 'inline-flex'; acceptProjectsButton.disabled = false; }
            finalizeScanUIState(); eventSource.close(); 
        }
    };
    eventSource.onerror = function(err) {
        console.error("KiCad Project Browser: EventSource failed:", err);
        addScanProgressMessage(scanStatusListUl, "Error connecting to scan stream or stream interrupted.", "error-message");
        eventSource.close(); finalizeScanUIState(); 
        if(acceptProjectsButton) { acceptProjectsButton.style.display = 'inline-flex'; acceptProjectsButton.disabled = !fullDataFromApi.projects || fullDataFromApi.projects.length === 0; }
        if (!fullDataFromApi.projects || fullDataFromApi.projects.length === 0) { showStatusMessage('error', 'Scan process failed. No project data available.');}
        updateInfoDisplay();
    };
}

if(acceptProjectsButton) {
    acceptProjectsButton.addEventListener('click', () => {
        console.log("KiCad Project Browser: Accept Projects button clicked.");
        if (fullDataFromApi && fullDataFromApi.projects) {
            processFinalDataAndRenderTable(fullDataFromApi);
        } else {
            showStatusMessage('no_projects', 'No project data was finalized from the scan.');
        }
        if (refreshButton) refreshButton.disabled = false;
        if (searchInput) searchInput.disabled = false;
    });
}

function renderTable(projectsToRender) {
    console.log("KiCad Project Browser: renderTable() called, projects length:", projectsToRender ? projectsToRender.length : 'N/A');
    tableBody.innerHTML = '';
    
    let totalProjectCount = 0;
    if (fullDataFromApi && fullDataFromApi.projects && Array.isArray(fullDataFromApi.projects)) {
        totalProjectCount = fullDataFromApi.projects.length;
    }
    const currentDisplayCount = (Array.isArray(projectsToRender) ? projectsToRender.length : 0);
    projectCountSpan.textContent = `${currentDisplayCount} of ${totalProjectCount} projects shown`;

    if (!Array.isArray(projectsToRender) || projectsToRender.length === 0) {
        if (searchInput && searchInput.value.trim() !== '') { showStatusMessage('no_results'); }
        else {
             if (totalProjectCount === 0) { showStatusMessage('no_projects', 'No projects found in the index.');}
             else { showStatusMessage('no_results', 'No projects match your current search.');}
        }
        return;
    }
    showMainDisplay('table'); 

    try {
        projectsToRender.forEach((project, index) => { 
            const row = tableBody.insertRow();
            // ============ START OF MODIFIED addEventListener in renderTable ============
            row.addEventListener('click', () => {
                console.log(`KiCad Project Browser: Row clicked! SKU: ${project.sku}, Path: ${project.path}`); // ADDED THIS LOG
                openProjectFolder(project.path, project.sku);
            });
            // ============  END OF MODIFIED addEventListener in renderTable  ============
            row.insertCell().textContent = index + 1; 
            row.insertCell().textContent = project.sku || 'N/A';
            row.insertCell().textContent = project.description || 'N/A';
            row.insertCell().textContent = project.last_modified || 'N/A';
            row.insertCell().textContent = project.file_size || 'N/A';
            row.insertCell().textContent = project.version || 'N/A';
            // Moved addEventListener to be attached to the row itself, before cells are added
            // for potentially better event capturing, though usually order of cell append vs listener attach doesn't matter for row.
        });
    } catch (e) {
        console.error("KiCad Project Browser: Error during projectsToRender.forEach loop in renderTable:", e);
        showStatusMessage('error', 'Error displaying project rows. Check console.');
    }
    updateSortIndicators();
}

function sortProjects(columnKey, direction, toggle = true) { /* ... unchanged ... */ 
    if (!fullDataFromApi.projects || !Array.isArray(fullDataFromApi.projects)) return;
    if (toggle && currentSortColumn === columnKey) {
        currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        currentSortColumn = columnKey;
        currentSortDirection = direction || (columnKey === 'last_modified' ? 'desc' : 'asc'); // Default desc for date
    }
    fullDataFromApi.projects.sort((a, b) => {
        let valA = a[columnKey]; let valB = b[columnKey];
        if (columnKey === 'last_modified') { valA = new Date(valA); valB = new Date(valB); }
        else if (typeof valA === 'string') { valA = valA.toLowerCase(); valB = valB.toLowerCase(); }
        if (valA < valB) return currentSortDirection === 'asc' ? -1 : 1;
        if (valA > valB) return currentSortDirection === 'asc' ? 1 : -1;
        if (a.sku && b.sku) { if (a.sku.toLowerCase() < b.sku.toLowerCase()) return -1; if (a.sku.toLowerCase() > b.sku.toLowerCase()) return 1;}
        return 0;
    });
    renderTable(fullDataFromApi.projects); 
}

function updateSortIndicators() { /* ... unchanged ... */
    if (!tableHeaderRow) return;
    tableHeaderRow.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
        const sortKey = th.dataset.sortKey;
        if (sortKey === currentSortColumn) {
            th.classList.add(currentSortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
        }
    });
}

function initializeTableSorting() { /* ... unchanged ... */
    if (!tableHeaderRow) return;
    const headers = tableHeaderRow.querySelectorAll('th');
    headers.forEach((th, colIndex) => {
        let key;
        switch (colIndex) { // Now 0-indexed due to '#' column
            case 1: key = 'sku'; break; case 2: key = 'description'; break;
            case 3: key = 'last_modified'; break; case 4: key = 'file_size'; break;
            case 5: key = 'version'; break; default: return; 
        }
        th.dataset.sortKey = key; th.style.cursor = 'pointer'; 
        th.addEventListener('click', () => {
            if (key !== 'file_size') {
                const initialDirection = (currentSortColumn !== key) ? (key === 'last_modified' ? 'desc' : 'asc') : currentSortDirection;
                sortProjects(key, initialDirection);
            } else {
                alert("Sorting by File Size is not fully implemented due to varying units (KB/MB/GB).");
            }
        });
    });
}

async function openProjectFolder(path, sku) { /* ... unchanged ... */
    console.log(`KiCad Project Browser: Requesting to open folder for ${sku}: Path: ${path}`);
    try {
        const response = await fetch(`${API_BASE_URL}/api/open_folder`, {
            method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ path: path }),
        });
        const result = await response.json();
        if (!result.success) alert(`Error opening folder: ${result.message}`);
    } catch (error) {
        alert(`Failed to communicate with server to open folder: ${error.message}`);
    }
}

if(searchInput) { searchInput.addEventListener('input', (e) => { /* ... unchanged ... */
    const searchTerm = e.target.value.toLowerCase().trim();
    const sourceProjects = (fullDataFromApi && fullDataFromApi.projects && Array.isArray(fullDataFromApi.projects)) ? fullDataFromApi.projects : [];
    if (searchTerm === '') { renderTable(sourceProjects); return; }
    const filteredProjects = sourceProjects.filter(project => 
        (project.sku && project.sku.toLowerCase().includes(searchTerm)) ||
        (project.description && project.description.toLowerCase().includes(searchTerm))
    );
    renderTable(filteredProjects);
});
}
if(refreshButton) { refreshButton.addEventListener('click', () => { /* ... unchanged ... */
    const performScan = confirm("Are you sure you want to perform a full re-scan of all projects?\nThis may take some time.");
    if (!performScan) return; 
    startScanProcess();
});
}

console.log("KiCad Project Browser: Calling fetchInitialProjectData() for initial load.");
fetchInitialProjectData();
document.addEventListener('DOMContentLoaded', () => { 
    initializeTableSorting();
    if (fullDataFromApi.projects && fullDataFromApi.projects.length > 0) {
        updateSortIndicators(); 
    }
});