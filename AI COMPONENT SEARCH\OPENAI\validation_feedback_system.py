#!/usr/bin/env python3
"""
Validation Feedback System
Easy logging and reporting of quality validation issues for continuous improvement
"""

import json
import csv
import os
from datetime import datetime
from typing import Dict, List, Any

class ValidationFeedbackLogger:
    """Logs validation issues for future quality engine improvements"""
    
    def __init__(self):
        self.feedback_file = "validation_feedback.log"
        self.summary_file = "validation_summary.json"
        self.csv_file = "validation_feedback.csv"
        self.ensure_files_exist()
    
    def ensure_files_exist(self):
        """Create feedback files if they don't exist"""
        # Create CSV with headers if it doesn't exist
        if not os.path.exists(self.csv_file):
            with open(self.csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'Timestamp', 'Search_Term', 'Component_Name', 'Expected_Result', 
                    'Actual_Result', 'Issue_Type', 'User_Notes'
                ])
        
        # Create summary file if it doesn't exist
        if not os.path.exists(self.summary_file):
            with open(self.summary_file, 'w', encoding='utf-8') as f:
                json.dump({"total_reports": 0, "categories": {}}, f, indent=2)
    
    def log_validation_issue(self, search_term: str, component_name: str, 
                           expected_result: str, actual_result: str, 
                           issue_type: str = "relevance", user_notes: str = ""):
        """
        Log a validation issue for future improvement
        
        Args:
            search_term: What the user searched for
            component_name: The component that was incorrectly validated
            expected_result: "accept" or "reject" - what should have happened
            actual_result: "accept" or "reject" - what actually happened
            issue_type: Type of issue (relevance, value_matching, component_type, etc.)
            user_notes: Additional notes from the user
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Log to CSV file
        with open(self.csv_file, 'a', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                timestamp, search_term, component_name, expected_result,
                actual_result, issue_type, user_notes
            ])
        
        # Log to text file (human readable)
        with open(self.feedback_file, 'a', encoding='utf-8') as f:
            f.write(f"\n[{timestamp}] VALIDATION ISSUE\n")
            f.write(f"Search: '{search_term}'\n")
            f.write(f"Component: '{component_name}'\n")
            f.write(f"Expected: {expected_result} | Actual: {actual_result}\n")
            f.write(f"Issue Type: {issue_type}\n")
            if user_notes:
                f.write(f"Notes: {user_notes}\n")
            f.write("-" * 50 + "\n")
        
        # Update summary
        self.update_summary(issue_type)
        
        print(f"✅ Validation issue logged: {search_term} -> {component_name}")
    
    def log_false_positive(self, search_term: str, component_name: str, user_notes: str = ""):
        """Log when irrelevant component was accepted (should have been rejected)"""
        self.log_validation_issue(
            search_term, component_name, "reject", "accept", 
            "false_positive", user_notes
        )
    
    def log_false_negative(self, search_term: str, component_name: str, user_notes: str = ""):
        """Log when relevant component was rejected (should have been accepted)"""
        self.log_validation_issue(
            search_term, component_name, "accept", "reject", 
            "false_negative", user_notes
        )
    
    def log_value_mismatch(self, search_term: str, component_name: str, user_notes: str = ""):
        """Log when component values weren't matched properly"""
        self.log_validation_issue(
            search_term, component_name, "accept", "reject", 
            "value_mismatch", user_notes
        )
    
    def log_component_type_issue(self, search_term: str, component_name: str, user_notes: str = ""):
        """Log when component type wasn't recognized properly"""
        self.log_validation_issue(
            search_term, component_name, "accept", "reject", 
            "component_type", user_notes
        )
    
    def update_summary(self, issue_type: str):
        """Update the summary statistics"""
        try:
            with open(self.summary_file, 'r', encoding='utf-8') as f:
                summary = json.load(f)
        except:
            summary = {"total_reports": 0, "categories": {}}
        
        summary["total_reports"] = summary.get("total_reports", 0) + 1
        summary["categories"][issue_type] = summary["categories"].get(issue_type, 0) + 1
        summary["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        with open(self.summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2)
    
    def get_feedback_summary(self) -> Dict:
        """Get summary of all feedback logged"""
        try:
            with open(self.summary_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {"total_reports": 0, "categories": {}}
    
    def get_recent_issues(self, limit: int = 10) -> List[Dict]:
        """Get recent validation issues"""
        issues = []
        try:
            with open(self.csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                issues = list(reader)
                return issues[-limit:] if len(issues) > limit else issues
        except:
            return []
    
    def generate_improvement_report(self) -> str:
        """Generate a report for developers to improve the validation"""
        summary = self.get_feedback_summary()
        recent_issues = self.get_recent_issues(20)
        
        report = []
        report.append("🔧 VALIDATION IMPROVEMENT REPORT")
        report.append("=" * 40)
        report.append(f"Total Issues Reported: {summary.get('total_reports', 0)}")
        report.append(f"Last Updated: {summary.get('last_updated', 'Never')}")
        
        # Category breakdown
        if summary.get('categories'):
            report.append("\n📊 Issue Categories:")
            for category, count in summary['categories'].items():
                report.append(f"  {category}: {count} issues")
        
        # Recent issues
        if recent_issues:
            report.append("\n🔍 Recent Issues (for pattern analysis):")
            for issue in recent_issues[-10:]:
                report.append(f"  '{issue['Search_Term']}' -> '{issue['Component_Name']}'")
                report.append(f"    Expected: {issue['Expected_Result']}, Got: {issue['Actual_Result']}")
                report.append(f"    Type: {issue['Issue_Type']}")
                if issue['User_Notes']:
                    report.append(f"    Notes: {issue['User_Notes']}")
                report.append("")
        
        # Suggestions for improvement
        report.append("💡 SUGGESTED IMPROVEMENTS:")
        if summary.get('categories', {}).get('false_negative', 0) > 0:
            report.append("  - Add more component name patterns")
            report.append("  - Improve keyword matching")
        if summary.get('categories', {}).get('value_mismatch', 0) > 0:
            report.append("  - Enhance value extraction patterns")
            report.append("  - Add more unit synonyms")
        if summary.get('categories', {}).get('component_type', 0) > 0:
            report.append("  - Expand component type recognition")
            report.append("  - Add more component categories")
        
        return "\n".join(report)
    
    def export_for_developer(self) -> str:
        """Export data in format useful for developer improvements"""
        report_file = f"validation_improvement_report_{datetime.now().strftime('%Y%m%d')}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(self.generate_improvement_report())
        
        print(f"📄 Improvement report exported to: {report_file}")
        return report_file

# Global instance for easy access
feedback_logger = ValidationFeedbackLogger()

# Convenience functions for easy use
def report_false_positive(search_term: str, component_name: str, notes: str = ""):
    """Quick function: component was accepted but should have been rejected"""
    feedback_logger.log_false_positive(search_term, component_name, notes)

def report_false_negative(search_term: str, component_name: str, notes: str = ""):
    """Quick function: component was rejected but should have been accepted"""
    feedback_logger.log_false_negative(search_term, component_name, notes)

def report_value_issue(search_term: str, component_name: str, notes: str = ""):
    """Quick function: component value wasn't matched properly"""
    feedback_logger.log_value_mismatch(search_term, component_name, notes)

def report_type_issue(search_term: str, component_name: str, notes: str = ""):
    """Quick function: component type wasn't recognized properly"""
    feedback_logger.log_component_type_issue(search_term, component_name, notes)

def show_feedback_summary():
    """Show summary of all feedback"""
    summary = feedback_logger.get_feedback_summary()
    print("\n📊 Validation Feedback Summary")
    print("=" * 35)
    print(f"Total issues reported: {summary.get('total_reports', 0)}")
    
    if summary.get('categories'):
        print("\nIssue breakdown:")
        for category, count in summary['categories'].items():
            print(f"  {category}: {count}")
    
    if summary.get('last_updated'):
        print(f"\nLast updated: {summary['last_updated']}")

def generate_improvement_report():
    """Generate and save improvement report for developers"""
    return feedback_logger.export_for_developer()

if __name__ == "__main__":
    print("🔧 Validation Feedback System")
    print("=" * 35)
    
    # Example usage
    print("\n📝 Example Usage:")
    print("# Report false positive (irrelevant item accepted)")
    print("report_false_positive('10k resistor', 'Arduino Uno Board', 'Completely wrong type')")
    
    print("\n# Report false negative (relevant item rejected)")
    print("report_false_negative('temperature sensor', 'LM35 Temperature IC', 'Should match sensor search')")
    
    print("\n# Report value matching issue")
    print("report_value_issue('100uF capacitor', '100 microfarad cap', 'Should recognize microfarad = uF')")
    
    print("\n# Show current summary")
    print("show_feedback_summary()")
    
    print("\n# Generate improvement report")
    print("generate_improvement_report()")
    
    # Show current summary
    show_feedback_summary()
    
    print(f"\n✅ Feedback system ready!")
    print(f"📁 Files created:")
    print(f"  - validation_feedback.log (human readable)")
    print(f"  - validation_feedback.csv (structured data)")
    print(f"  - validation_summary.json (statistics)")
