#!/usr/bin/env python3
"""
Datasheet Manager <PERSON><PERSON><PERSON>les downloading, storing, and analyzing component datasheets
Integrates with AI analyzer for intelligent parameter extraction
"""

import os
import json
import requests
import hashlib
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import threading
from urllib.parse import urlparse, urljoin
import re
from pathlib import Path

@dataclass
class DatasheetInfo:
    """Information about a downloaded datasheet"""
    component_name: str
    manufacturer: str
    part_number: str
    datasheet_url: str
    local_path: str
    download_date: str
    file_size: int
    file_hash: str
    analysis_status: str  # "pending", "analyzed", "failed"
    analysis_data: Optional[Dict] = None

class DatasheetManager:
    """Manages component datasheets and their analysis"""
    
    def __init__(self):
        self.base_dir = Path("datasheets")
        self.downloaded_dir = self.base_dir / "downloaded"
        self.analyzed_dir = self.base_dir / "analyzed"
        self.cache_dir = Path("cache")
        
        # Create directories
        for directory in [self.base_dir, self.downloaded_dir, self.analyzed_dir, self.cache_dir]:
            directory.mkdir(exist_ok=True)
        
        # Load datasheet database
        self.db_file = self.base_dir / "datasheet_db.json"
        self.datasheet_db = self.load_database()
        
        # Session for downloads
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Download statistics
        self.download_stats = {
            "total_downloads": 0,
            "successful_downloads": 0,
            "failed_downloads": 0,
            "total_size": 0
        }
    
    def load_database(self) -> Dict[str, DatasheetInfo]:
        """Load datasheet database from JSON file"""
        if self.db_file.exists():
            try:
                with open(self.db_file, 'r') as f:
                    data = json.load(f)
                    # Convert dict back to DatasheetInfo objects
                    db = {}
                    for key, value in data.items():
                        db[key] = DatasheetInfo(**value)
                    return db
            except Exception as e:
                print(f"Error loading datasheet database: {e}")
                return {}
        return {}
    
    def save_database(self):
        """Save datasheet database to JSON file"""
        try:
            # Convert DatasheetInfo objects to dicts
            data = {}
            for key, value in self.datasheet_db.items():
                data[key] = asdict(value)
            
            with open(self.db_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Error saving datasheet database: {e}")
    
    def search_datasheet_urls(self, component_name: str, manufacturer: str = "") -> List[str]:
        """Search for datasheet URLs for a component"""
        urls = []
        
        # Common datasheet search patterns
        search_terms = [
            f"{component_name} datasheet",
            f"{manufacturer} {component_name} datasheet",
            f"{component_name} pdf datasheet"
        ]
        
        # Common datasheet hosting sites
        datasheet_sites = [
            "datasheetspdf.com",
            "alldatasheet.com",
            "datasheet4u.com",
            "datasheetcatalog.org",
            "datasheet.live"
        ]
        
        # Manufacturer websites (common patterns)
        manufacturer_patterns = {
            "texas instruments": "ti.com",
            "analog devices": "analog.com",
            "microchip": "microchip.com",
            "stmicroelectronics": "st.com",
            "infineon": "infineon.com",
            "nxp": "nxp.com",
            "maxim": "maximintegrated.com",
            "linear technology": "linear.com"
        }
        
        # Try to find manufacturer-specific URLs
        if manufacturer.lower() in manufacturer_patterns:
            manufacturer_site = manufacturer_patterns[manufacturer.lower()]
            urls.append(f"https://www.{manufacturer_site}/product/{component_name}")
        
        # Add generic datasheet search URLs
        for site in datasheet_sites:
            search_url = f"https://www.{site}/search?q={component_name.replace(' ', '+')}"
            urls.append(search_url)
        
        return urls[:10]  # Limit to top 10 URLs
    
    def download_datasheet(self, component_name: str, datasheet_url: str, 
                          manufacturer: str = "", part_number: str = "") -> Optional[DatasheetInfo]:
        """Download a datasheet from URL"""
        try:
            # Generate filename
            safe_name = re.sub(r'[^\w\-_.]', '_', component_name)
            filename = f"{safe_name}_{int(datetime.now().timestamp())}.pdf"
            local_path = self.downloaded_dir / filename
            
            # Download the file
            response = self.session.get(datasheet_url, timeout=30, stream=True)
            response.raise_for_status()
            
            # Check if it's actually a PDF
            content_type = response.headers.get('content-type', '').lower()
            if 'pdf' not in content_type and not datasheet_url.lower().endswith('.pdf'):
                # Try to find PDF links in the page
                pdf_links = self._extract_pdf_links(response.text, datasheet_url)
                if pdf_links:
                    # Try the first PDF link
                    return self.download_datasheet(component_name, pdf_links[0], manufacturer, part_number)
                else:
                    raise Exception("No PDF content found")
            
            # Save the file
            total_size = 0
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        total_size += len(chunk)
            
            # Calculate file hash
            file_hash = self._calculate_file_hash(local_path)
            
            # Create datasheet info
            datasheet_info = DatasheetInfo(
                component_name=component_name,
                manufacturer=manufacturer,
                part_number=part_number or component_name,
                datasheet_url=datasheet_url,
                local_path=str(local_path),
                download_date=datetime.now().isoformat(),
                file_size=total_size,
                file_hash=file_hash,
                analysis_status="pending"
            )
            
            # Add to database
            db_key = f"{component_name}_{file_hash[:8]}"
            self.datasheet_db[db_key] = datasheet_info
            self.save_database()
            
            # Update statistics
            self.download_stats["total_downloads"] += 1
            self.download_stats["successful_downloads"] += 1
            self.download_stats["total_size"] += total_size
            
            return datasheet_info
        
        except Exception as e:
            self.download_stats["total_downloads"] += 1
            self.download_stats["failed_downloads"] += 1
            print(f"Failed to download datasheet from {datasheet_url}: {e}")
            return None
    
    def _extract_pdf_links(self, html_content: str, base_url: str) -> List[str]:
        """Extract PDF links from HTML content"""
        pdf_links = []
        
        # Simple regex to find PDF links
        pdf_pattern = r'href=["\']([^"\']*\.pdf[^"\']*)["\']'
        matches = re.findall(pdf_pattern, html_content, re.IGNORECASE)
        
        for match in matches:
            # Convert relative URLs to absolute
            if match.startswith('http'):
                pdf_links.append(match)
            else:
                pdf_links.append(urljoin(base_url, match))
        
        return pdf_links[:5]  # Limit to first 5 PDF links
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of a file"""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def find_datasheet_for_component(self, component_name: str, manufacturer: str = "") -> Optional[DatasheetInfo]:
        """Find existing datasheet for a component"""
        for datasheet_info in self.datasheet_db.values():
            if (datasheet_info.component_name.lower() == component_name.lower() or
                datasheet_info.part_number.lower() == component_name.lower()):
                if not manufacturer or datasheet_info.manufacturer.lower() == manufacturer.lower():
                    return datasheet_info
        return None
    
    def get_top_manufacturers(self, component_type: str, search_term: str) -> List[Dict[str, str]]:
        """Get top 5 manufacturers for a component type"""
        # This would integrate with AI analysis and web search
        # For now, return common manufacturers based on component type
        
        manufacturer_db = {
            "resistor": [
                {"name": "Vishay", "reputation": "Excellent", "availability": "High"},
                {"name": "Yageo", "reputation": "Good", "availability": "High"},
                {"name": "Panasonic", "reputation": "Excellent", "availability": "Medium"},
                {"name": "Bourns", "reputation": "Good", "availability": "Medium"},
                {"name": "KOA Speer", "reputation": "Good", "availability": "Medium"}
            ],
            "capacitor": [
                {"name": "Murata", "reputation": "Excellent", "availability": "High"},
                {"name": "TDK", "reputation": "Excellent", "availability": "High"},
                {"name": "KEMET", "reputation": "Good", "availability": "High"},
                {"name": "Nichicon", "reputation": "Good", "availability": "Medium"},
                {"name": "Panasonic", "reputation": "Excellent", "availability": "Medium"}
            ],
            "microcontroller": [
                {"name": "Microchip", "reputation": "Excellent", "availability": "High"},
                {"name": "STMicroelectronics", "reputation": "Excellent", "availability": "High"},
                {"name": "Texas Instruments", "reputation": "Excellent", "availability": "Medium"},
                {"name": "NXP", "reputation": "Good", "availability": "Medium"},
                {"name": "Infineon", "reputation": "Good", "availability": "Medium"}
            ],
            "arduino": [
                {"name": "Arduino", "reputation": "Excellent", "availability": "High"},
                {"name": "SparkFun", "reputation": "Good", "availability": "High"},
                {"name": "Adafruit", "reputation": "Good", "availability": "High"},
                {"name": "Seeed Studio", "reputation": "Good", "availability": "Medium"},
                {"name": "DFRobot", "reputation": "Good", "availability": "Medium"}
            ]
        }
        
        return manufacturer_db.get(component_type, [
            {"name": "Generic", "reputation": "Variable", "availability": "High"},
            {"name": "OEM", "reputation": "Variable", "availability": "Medium"}
        ])
    
    def analyze_datasheet_with_ai(self, datasheet_info: DatasheetInfo) -> Dict:
        """Analyze datasheet using AI (placeholder for now)"""
        # This would integrate with the AI analyzer
        # For now, return a placeholder analysis
        
        analysis = {
            "component_type": "unknown",
            "key_parameters": {},
            "package_options": [],
            "electrical_characteristics": {},
            "applications": [],
            "confidence_score": 0.0,
            "analysis_date": datetime.now().isoformat(),
            "notes": "AI analysis not yet implemented"
        }
        
        # Update datasheet info
        datasheet_info.analysis_status = "analyzed"
        datasheet_info.analysis_data = analysis
        self.save_database()
        
        return analysis
    
    def get_download_statistics(self) -> Dict:
        """Get download statistics"""
        stats = self.download_stats.copy()
        stats["database_entries"] = len(self.datasheet_db)
        stats["total_size_mb"] = round(stats["total_size"] / (1024 * 1024), 2)
        return stats
    
    def cleanup_old_datasheets(self, days_old: int = 30):
        """Clean up datasheets older than specified days"""
        cutoff_date = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
        
        to_remove = []
        for key, datasheet_info in self.datasheet_db.items():
            download_date = datetime.fromisoformat(datasheet_info.download_date).timestamp()
            if download_date < cutoff_date:
                # Remove file
                try:
                    if os.path.exists(datasheet_info.local_path):
                        os.remove(datasheet_info.local_path)
                    to_remove.append(key)
                except Exception as e:
                    print(f"Error removing {datasheet_info.local_path}: {e}")
        
        # Remove from database
        for key in to_remove:
            del self.datasheet_db[key]
        
        self.save_database()
        return len(to_remove)

# Global instance
datasheet_manager = DatasheetManager()

def get_datasheet_manager() -> DatasheetManager:
    """Get the global datasheet manager instance"""
    return datasheet_manager

if __name__ == "__main__":
    # Test the datasheet manager
    manager = get_datasheet_manager()
    
    print("Datasheet Manager Test")
    print(f"Database entries: {len(manager.datasheet_db)}")
    print(f"Download stats: {manager.get_download_statistics()}")
    
    # Test manufacturer lookup
    manufacturers = manager.get_top_manufacturers("resistor", "10k")
    print(f"Top resistor manufacturers: {manufacturers}")
