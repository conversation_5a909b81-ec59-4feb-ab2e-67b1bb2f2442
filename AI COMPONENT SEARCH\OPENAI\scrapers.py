#!/usr/bin/env python3
"""
Web scrapers for different electronics component suppliers.
Each supplier has its own scraper class that implements the BaseScraper interface.
"""

import requests
from bs4 import BeautifulSoup
import time
import random
from urllib.parse import urlencode, quote_plus
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class BaseScraper:
    """Base class for all supplier scrapers"""
    
    def __init__(self, supplier_config):
        self.config = supplier_config
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def search_component(self, component_value, package_type=None, manufacturer=None, quantity=1):
        """
        Search for a component on this supplier's website
        Returns list of dictionaries with component information
        """
        try:
            # Add random delay to avoid being blocked
            time.sleep(random.uniform(0.5, 2.0))
            
            # Build search query
            search_query = self.build_search_query(component_value, package_type, manufacturer)
            
            # Get search results
            results = self.perform_search(search_query)
            
            # Parse and return results
            return self.parse_results(results, quantity)
            
        except Exception as e:
            print(f"Error searching {self.config['name']}: {str(e)}")
            return []
    
    def build_search_query(self, component_value, package_type=None, manufacturer=None):
        """Build search query string with proper normalization"""
        # Normalize component value for better search compatibility
        normalized_component = self.normalize_component_value(component_value)
        query_parts = [normalized_component]

        if manufacturer:
            query_parts.append(manufacturer)

        if package_type and package_type != "Any":
            query_parts.append(package_type)

        return " ".join(query_parts)

    def normalize_component_value(self, component_value):
        """Normalize component value for better search compatibility"""
        if not component_value:
            return ""

        # Create a normalized version for search
        normalized = component_value

        # Handle resistor values with omega symbol
        normalized = normalized.replace('Ω', ' ohm')
        normalized = normalized.replace('Ω', ' ohm')  # Different omega characters
        normalized = normalized.replace('Ω', ' ohm')

        # Handle common electronics abbreviations
        replacements = {
            'µF': 'uF',
            'μF': 'uF',
            'pF': 'pf',
            'nF': 'nf',
            'mF': 'mf',
            'kΩ': 'k ohm',
            'MΩ': 'M ohm',
            'GΩ': 'G ohm',
            'kHz': 'khz',
            'MHz': 'mhz',
            'GHz': 'ghz',
            'mA': 'ma',
            'µA': 'ua',
            'μA': 'ua'
        }

        for old, new in replacements.items():
            normalized = normalized.replace(old, new)

        # Clean up extra spaces
        normalized = re.sub(r'\s+', ' ', normalized).strip()

        return normalized
    
    def perform_search(self, search_query):
        """Perform the actual search - to be implemented by subclasses"""
        raise NotImplementedError("Subclasses must implement perform_search")
    
    def parse_results(self, html_content, quantity):
        """Parse search results - to be implemented by subclasses"""
        raise NotImplementedError("Subclasses must implement parse_results")
    
    def extract_price(self, price_text):
        """Extract numeric price from text"""
        if not price_text:
            return 0
        
        # Remove currency symbols and extract numbers
        price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', ''))
        if price_match:
            return float(price_match.group())
        return 0
    
    def clean_text(self, text):
        """Clean and normalize text"""
        if not text:
            return ""
        return re.sub(r'\s+', ' ', text.strip())

class RobuScraper(BaseScraper):
    """Scraper for Robu.in"""

    def perform_search(self, search_query):
        """Search Robu.in"""
        # Use the actual search URL format for Robu
        search_url = f"https://robu.in/?s={quote_plus(search_query)}&post_type=product"

        try:
            response = self.session.get(search_url, timeout=10)
            response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            print(f"Error fetching from Robu: {e}")
            return ""

    def parse_results(self, html_content, quantity):
        """Parse Robu search results"""
        if not html_content:
            return []

        soup = BeautifulSoup(html_content, 'html.parser')
        results = []

        # Look for WooCommerce product containers (common in Indian e-commerce sites)
        selectors_to_try = [
            'li.product',
            'div.product',
            'article.product',
            '.woocommerce-LoopProduct-link',
            '.product-item',
            '.product-card'
        ]

        products = []
        for selector in selectors_to_try:
            products = soup.select(selector)
            if products:
                print(f"Found {len(products)} products using selector: {selector}")
                break

        if not products:
            # Fallback: look for any links that might be products
            products = soup.find_all('a', href=True)
            products = [p for p in products if 'product' in p.get('href', '').lower()][:5]
            print(f"Fallback: Found {len(products)} product links")

        for product in products[:5]:  # Limit to first 5 results
            try:
                # Extract product name
                name_elem = product.find(['h2', 'h3', 'h4']) or product.find('a', title=True)
                if name_elem:
                    name = self.clean_text(name_elem.get_text() or name_elem.get('title', ''))
                else:
                    name = "Product found on Robu"

                # Extract price - look for common price patterns
                price_elem = product.find(['span', 'div'], class_=lambda x: x and ('price' in x.lower() or 'cost' in x.lower() or 'amount' in x.lower()))
                if not price_elem:
                    price_elem = product.find(text=re.compile(r'₹|Rs\.?\s*\d+'))

                if price_elem:
                    price_text = price_elem if isinstance(price_elem, str) else price_elem.get_text()
                    price = self.extract_price(price_text)
                else:
                    price = 0

                # Extract stock status
                stock_elem = product.find(['span', 'div'], class_=lambda x: x and ('stock' in x.lower() or 'availability' in x.lower()))
                stock = self.clean_text(stock_elem.get_text()) if stock_elem else "Check Website"

                # Extract product URL
                link_elem = product if product.name == 'a' else product.find('a')
                product_url = link_elem.get('href') if link_elem else self.config['url']
                if product_url and product_url.startswith('/'):
                    product_url = self.config['url'] + product_url

                if name and name != "Product found on Robu":  # Only add if we found a real name
                    results.append({
                        'supplier': self.config['name'],
                        'component': name,
                        'price': price,
                        'stock': stock,
                        'shipping': 49,  # Known shipping cost for Robu
                        'total': price + 49,
                        'location': self.config['location'],
                        'url': product_url or self.config['url']
                    })

            except Exception as e:
                print(f"Error parsing Robu product: {e}")
                continue

        return results

class EveltaScraper(BaseScraper):
    """Enhanced Evelta.com scraper with professional sourcing data quality"""

    def perform_search(self, search_query):
        """Search Evelta using category browsing for better results"""
        try:
            # Enhanced category mapping for better targeting
            category_mapping = {
                'arduino': '/development-boards-and-kits/arduino-and-compatible-boards/',
                'resistor': '/passive-components/resistors/',
                'capacitor': '/categories/passive-components/capacitor/',
                'led': '/optoelectronics/leds-through-hole/',
                'sensor': '/categories/sensors/',
                'ic': '/integrated-circuits-ics/',
                'transistor': '/categories/discrete-semiconductors/transistors/',
                'microcontroller': '/development-boards-and-kits/microcontroller-boards/',
                'raspberry': '/development-boards-and-kits/raspberry-pi/',
                'esp32': '/development-boards-and-kits/esp32-boards/',
                'esp8266': '/development-boards-and-kits/esp8266-boards/',
                'motor': '/motors-and-actuators/',
                'display': '/displays/',
                'module': '/modules/',
                'board': '/development-boards-and-kits/',
                'kit': '/development-boards-and-kits/'
            }

            # Find best matching category
            search_lower = search_query.lower()
            category_url = None

            for keyword, url in category_mapping.items():
                if keyword in search_lower:
                    category_url = url
                    break

            if category_url:
                search_url = f"https://www.evelta.com{category_url}"
            else:
                # Fallback to search
                search_url = f"https://www.evelta.com/?s={quote_plus(search_query)}&post_type=product"

            response = self.session.get(search_url, timeout=10)
            response.raise_for_status()
            return response.text

        except requests.RequestException as e:
            print(f"Error fetching from Evelta: {e}")
            return ""

    def parse_results(self, html_content, quantity):
        """Parse Evelta results with professional sourcing focus"""
        if not html_content:
            return []

        soup = BeautifulSoup(html_content, 'html.parser')
        results = []
        seen_products = set()

        # Find product cards using the working selector
        products = soup.select('article.card')

        for product in products[:8]:  # Get more results for better selection
            try:
                # Extract clean product name
                name = self.extract_evelta_product_name(product)
                if not name or name in seen_products:
                    continue

                seen_products.add(name)

                # Extract accurate price
                price = self.extract_evelta_price(product)
                if price <= 0:
                    continue

                # Extract detailed stock information
                stock_info = self.extract_evelta_stock(product)

                # Extract product URL
                product_url = self.extract_evelta_url(product)

                # Extract specifications and part number
                specifications = self.extract_evelta_specifications(product)
                part_number = self.extract_evelta_part_number(product)

                # Calculate total with accurate shipping
                shipping_cost = self.calculate_evelta_shipping(price, quantity)

                results.append({
                    'supplier': 'Evelta',
                    'component': name,
                    'price': price,
                    'stock': stock_info,
                    'shipping': shipping_cost,
                    'total': price + shipping_cost,
                    'location': 'Mumbai, Maharashtra',
                    'url': product_url,
                    'specifications': specifications,
                    'part_number': part_number,
                    'quantity_breaks': self.get_quantity_pricing(price, quantity),
                    'lead_time': '1-2 days (Mumbai stock)'
                })

            except Exception as e:
                print(f"Error parsing Evelta product: {e}")
                continue

        return results

    def extract_evelta_product_name(self, product):
        """Extract clean, professional product name"""
        # Try multiple selectors for product name
        selectors = [
            'h3.card-title',
            'h4.card-title',
            '.card-title',
            'h3 a',
            'h4 a',
            'a[title]'
        ]

        for selector in selectors:
            elem = product.select_one(selector)
            if elem:
                name = self.clean_text(elem.get_text() or elem.get('title', ''))
                if name and len(name) > 5:
                    # Clean up common issues
                    name = re.sub(r'\s+', ' ', name)
                    name = name.replace('Selling Fast!', '').strip()
                    name = name.replace('Sale •', '').strip()
                    name = re.sub(r'Save \d+%', '', name).strip()

                    if name and not any(x in name for x in ['{{', 'template', 'error']):
                        return name[:80]  # Reasonable length

        return None

    def extract_evelta_price(self, product):
        """Extract accurate price from Evelta product"""
        # Look for price in various formats
        price_patterns = [
            r'₹([\d,]+\.?\d*)',
            r'Rs\.?\s*([\d,]+\.?\d*)',
            r'INR\s*([\d,]+\.?\d*)'
        ]

        product_text = product.get_text()

        for pattern in price_patterns:
            matches = re.findall(pattern, product_text)
            if matches:
                # Get the last price (usually the current price)
                price_str = matches[-1].replace(',', '')
                try:
                    price = float(price_str)
                    if 1 <= price <= 50000:  # Reasonable range
                        return price
                except ValueError:
                    continue

        return 0

    def extract_evelta_stock(self, product):
        """Extract detailed stock information"""
        stock_text = product.get_text().lower()

        # Look for specific stock indicators
        if 'only' in stock_text and 'stock' in stock_text:
            # Extract "Only X in stock"
            match = re.search(r'only\s+(\d+)\s+in\s+stock', stock_text)
            if match:
                count = int(match.group(1))
                if count <= 5:
                    return f"Limited Stock ({count} units)"
                else:
                    return f"In Stock ({count} units)"

        if any(x in stock_text for x in ['in stock', 'available']):
            return "In Stock"
        elif any(x in stock_text for x in ['out of stock', 'sold out']):
            return "Out of Stock"
        elif 'pre-order' in stock_text:
            return "Pre-order (2-3 weeks)"

        return "Contact for Availability"

    def extract_evelta_url(self, product):
        """Extract product URL"""
        link_elem = product.find('a')
        if link_elem:
            href = link_elem.get('href', '')
            if href.startswith('/'):
                return f"https://www.evelta.com{href}"
            elif href.startswith('http'):
                return href

        return "https://www.evelta.com"

    def extract_evelta_specifications(self, product):
        """Extract product specifications"""
        text = product.get_text()
        specs = []

        # Common specifications for electronics
        voltage_match = re.search(r'(\d+\.?\d*)\s*[Vv](?:olt)?', text)
        if voltage_match:
            specs.append(f"{voltage_match.group(1)}V")

        current_match = re.search(r'(\d+\.?\d*)\s*[Aa](?:mp)?', text)
        if current_match:
            specs.append(f"{current_match.group(1)}A")

        # Package types
        package_match = re.search(r'(DIP|SOP|QFP|BGA|0603|0805|1206|SOT-\d+|TO-\d+)', text, re.I)
        if package_match:
            specs.append(package_match.group(1).upper())

        # Frequency
        freq_match = re.search(r'(\d+\.?\d*)\s*(MHz|GHz|KHz)', text, re.I)
        if freq_match:
            specs.append(f"{freq_match.group(1)}{freq_match.group(2)}")

        return ", ".join(specs) if specs else ""

    def extract_evelta_part_number(self, product):
        """Extract manufacturer part number"""
        text = product.get_text()

        # Common part number patterns
        patterns = [
            r'(STM32[A-Z0-9]+)',
            r'(ATMEGA\d+[A-Z]*)',
            r'(ESP32[A-Z0-9-]*)',
            r'(ESP8266[A-Z0-9-]*)',
            r'(LM\d+[A-Z]*)',
            r'([A-Z]{2,}\d{3,}[A-Z0-9]*)'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.I)
            if match:
                return match.group(1).upper()

        return ""

    def calculate_evelta_shipping(self, price, quantity):
        """Calculate realistic Evelta shipping cost"""
        # Evelta's actual shipping structure
        if price >= 500:
            return 0  # Free shipping above ₹500
        elif quantity > 10:
            return 75  # Higher shipping for bulk orders
        else:
            return 55  # Standard shipping

    def get_quantity_pricing(self, unit_price, quantity):
        """Generate quantity-based pricing"""
        if quantity >= 10:
            discount = 0.05  # 5% discount for 10+
            return f"10+: ₹{unit_price * (1 - discount):.2f} each"
        elif quantity >= 50:
            discount = 0.10  # 10% discount for 50+
            return f"50+: ₹{unit_price * (1 - discount):.2f} each"

        return ""

class GenericScraper(BaseScraper):
    """Improved generic scraper with better data quality for sourcing decisions"""

    def perform_search(self, search_query):
        """Generic search implementation with better error handling"""
        search_url = self.config.get('search_url', '').format(query=quote_plus(search_query))

        if not search_url:
            return ""

        try:
            response = self.session.get(search_url, timeout=10)
            response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            print(f"Error fetching from {self.config['name']}: {e}")
            return ""

    def parse_results(self, html_content, quantity):
        """Improved result parsing focused on sourcing quality"""
        if not html_content:
            return []

        soup = BeautifulSoup(html_content, 'html.parser')

        # Look for common product container patterns
        possible_containers = [
            'div[class*="product"]',
            'div[class*="item"]',
            'div[class*="result"]',
            'li[class*="product"]',
            'article'
        ]

        products = []
        for selector in possible_containers:
            products = soup.select(selector)
            if products:
                break

        results = []
        seen_products = set()  # Prevent duplicates

        for product in products[:5]:  # Limit to 5 results for generic scraper
            try:
                # Extract product name with better cleaning
                name = self.extract_product_name(product)
                if not name or name in seen_products:
                    continue

                seen_products.add(name)

                # Extract price with validation
                price = self.extract_product_price(product)

                # Extract stock with realistic status
                stock = self.extract_stock_status(product)

                # Get realistic shipping cost based on supplier
                shipping = self.get_realistic_shipping_cost()

                # Extract product URL
                product_url = self.extract_product_url(product)

                # Only add if we have meaningful data
                if name != "Product found" and (price > 0 or stock != "Unknown"):
                    results.append({
                        'supplier': self.config['name'],
                        'component': name,
                        'price': price,
                        'stock': stock,
                        'shipping': shipping,
                        'total': price + shipping if price > 0 else 0,
                        'location': self.config['location'],
                        'url': product_url,
                        'specifications': self.extract_specifications(product),
                        'part_number': self.extract_part_number(product)
                    })

            except Exception as e:
                print(f"Error parsing {self.config['name']} product: {e}")
                continue

        return results

    def extract_product_name(self, product):
        """Extract clean, meaningful product name"""
        name_selectors = [
            'h1', 'h2', 'h3', 'h4',
            'a[title]', '.title', '.name', '.product-name',
            '[class*="title"]', '[class*="name"]'
        ]

        for selector in name_selectors:
            elem = product.select_one(selector)
            if elem:
                name = self.clean_text(elem.get_text() or elem.get('title', ''))
                if name and len(name) > 5 and not any(x in name.lower() for x in ['{{', 'template', 'error']):
                    return name[:100]  # Limit length

        return None

    def extract_product_price(self, product):
        """Extract price with better validation"""
        price_selectors = [
            '.price', '.cost', '.amount',
            '[class*="price"]', '[class*="cost"]', '[class*="amount"]'
        ]

        for selector in price_selectors:
            elem = product.select_one(selector)
            if elem:
                price_text = elem.get_text()
                price = self.extract_price(price_text)
                if 1 <= price <= 100000:  # Reasonable price range
                    return price

        return 0

    def extract_stock_status(self, product):
        """Extract realistic stock status"""
        stock_selectors = [
            '.stock', '.availability', '.inventory',
            '[class*="stock"]', '[class*="availability"]'
        ]

        for selector in stock_selectors:
            elem = product.select_one(selector)
            if elem:
                stock_text = elem.get_text().lower()

                # Map common stock indicators
                if any(x in stock_text for x in ['in stock', 'available', 'ready']):
                    return "In Stock"
                elif any(x in stock_text for x in ['out of stock', 'unavailable', 'sold out']):
                    return "Out of Stock"
                elif any(x in stock_text for x in ['limited', 'few left', 'low stock']):
                    return "Limited Stock"
                elif re.search(r'\d+.*stock', stock_text):
                    return f"Stock: {stock_text.strip()}"

        # Default based on supplier reliability
        if self.config['name'] in ['Evelta', 'Robu', 'ElectronicsComp']:
            return "Contact Supplier"
        else:
            return "Check Availability"

    def get_realistic_shipping_cost(self):
        """Get realistic shipping cost based on supplier"""
        shipping_costs = {
            'Robu': 49,
            'Evelta': 55,
            'ElectronicsComp': 60,
            'Sunrom': 45,
            'Probots': 50,
            'Rhydolabz': 65,
            'FabtoLab': 100,
            'CrazyPi': 40,
            'RoboCraze': 45,
            'Robokits': 50
        }

        return shipping_costs.get(self.config['name'], 50)

    def extract_product_url(self, product):
        """Extract product URL"""
        link_elem = product.find('a')
        if link_elem:
            href = link_elem.get('href', '')
            if href.startswith('/'):
                return self.config['url'] + href
            elif href.startswith('http'):
                return href

        return self.config['url']

    def extract_specifications(self, product):
        """Extract product specifications"""
        # Look for common specification patterns
        spec_text = product.get_text()
        specs = []

        # Common electronic component specs
        voltage_match = re.search(r'(\d+\.?\d*)\s*[Vv](?:olt)?', spec_text)
        if voltage_match:
            specs.append(f"{voltage_match.group(1)}V")

        current_match = re.search(r'(\d+\.?\d*)\s*[Aa](?:mp)?', spec_text)
        if current_match:
            specs.append(f"{current_match.group(1)}A")

        package_match = re.search(r'(DIP|SOP|QFP|BGA|0603|0805|1206|SOT-\d+)', spec_text, re.I)
        if package_match:
            specs.append(package_match.group(1).upper())

        return ", ".join(specs) if specs else ""

    def extract_part_number(self, product):
        """Extract manufacturer part number"""
        text = product.get_text()

        # Look for common part number patterns
        patterns = [
            r'[A-Z]{2,}\d{3,}[A-Z]*',  # Common IC patterns
            r'STM32[A-Z0-9]+',         # STM32 parts
            r'ATMEGA\d+[A-Z]*',        # AVR parts
            r'LM\d+[A-Z]*',            # Linear parts
        ]

        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(0)

        return ""

class SeleniumScraper(BaseScraper):
    """Base class for scrapers that need JavaScript rendering"""
    
    def __init__(self, supplier_config):
        super().__init__(supplier_config)
        self.driver = None
    
    def get_driver(self):
        """Get Selenium WebDriver instance"""
        if not self.driver:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            try:
                self.driver = webdriver.Chrome(options=chrome_options)
            except Exception as e:
                print(f"Error creating WebDriver: {e}")
                return None
        
        return self.driver
    
    def close_driver(self):
        """Close WebDriver"""
        if self.driver:
            self.driver.quit()
            self.driver = None
    
    def perform_search(self, search_query):
        """Search using Selenium"""
        driver = self.get_driver()
        if not driver:
            return ""
        
        search_url = self.config['search_url'].format(query=quote_plus(search_query))
        
        try:
            driver.get(search_url)
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Wait a bit for dynamic content to load
            time.sleep(2)
            
            return driver.page_source
            
        except TimeoutException:
            print(f"Timeout loading {self.config['name']}")
            return ""
        except Exception as e:
            print(f"Error with Selenium for {self.config['name']}: {e}")
            return ""

# Scraper factory function
def get_scraper(supplier_config):
    """Get appropriate scraper for a supplier"""
    scraper_class = supplier_config.get('scraper_class', 'GenericScraper')
    
    # Map scraper classes
    scraper_classes = {
        'RobuScraper': RobuScraper,
        'EveltaScraper': EveltaScraper,
        'GenericScraper': GenericScraper,
        'SeleniumScraper': SeleniumScraper
    }
    
    # For now, most scrapers will use GenericScraper
    # As we develop specific scrapers, we'll add them here
    if scraper_class in scraper_classes:
        return scraper_classes[scraper_class](supplier_config)
    else:
        return GenericScraper(supplier_config)

# Specific scraper classes (to be implemented as needed)
class ElectronicsCompScraper(GenericScraper):
    pass

class SunromScraper(GenericScraper):
    pass

class ProbotsScraper(GenericScraper):
    pass

class RhydolabzScraper(GenericScraper):
    pass

class FabtoLabScraper(GenericScraper):
    pass

# Add more specific scrapers as needed...
