#!/usr/bin/env python3
"""
Quick Search Test
Test the application with a simple search to verify everything works
"""

import sys
import os

def test_scraper_functionality():
    """Test scraper functionality without GUI"""
    print("🔍 Testing Scraper Functionality")
    print("=" * 35)
    
    try:
        from scrapers import get_scraper
        
        # Test with a simple supplier configuration
        test_supplier = {
            "name": "<PERSON>u",
            "url": "https://robu.in",
            "location": "Mumbai, Maharashtra",
            "type": "robu",
            "active": True
        }
        
        print(f"Testing scraper for: {test_supplier['name']}")
        
        # Get scraper instance
        scraper = get_scraper(test_supplier)
        print(f"✅ Scraper created: {type(scraper).__name__}")
        
        # Test search term normalization
        test_term = "10kΩ"
        normalized = scraper.normalize_component_value(test_term)
        print(f"✅ Search term normalized: '{test_term}' → '{normalized}'")
        
        # Test search query building
        search_query = scraper.build_search_query(test_term, "Any", None)
        print(f"✅ Search query built: '{search_query}'")
        
        print("✅ Scraper functionality test passed")
        return True
        
    except Exception as e:
        print(f"❌ Scraper test failed: {e}")
        return False

def test_gemini_quick():
    """Quick test of Gemini AI"""
    print("\n🤖 Testing Gemini AI Quick")
    print("=" * 30)
    
    try:
        from gemini_ai_analyzer import get_gemini_analyzer
        
        analyzer = get_gemini_analyzer()
        
        if analyzer.is_available():
            print("✅ Gemini AI available")
            
            # Quick analysis test
            print("🔍 Testing quick component analysis...")
            analysis = analyzer.analyze_component("arduino")
            
            print(f"✅ Analysis completed:")
            print(f"   Type: {analysis.component_type}")
            print(f"   Confidence: {analysis.confidence_score:.2f}")
            
            return True
        else:
            print("⚠️ Gemini AI not available (API key not configured)")
            return True  # Not a failure
            
    except Exception as e:
        print(f"❌ Gemini test failed: {e}")
        return False

def test_application_components():
    """Test key application components"""
    print("\n🏗️ Testing Application Components")
    print("=" * 40)
    
    try:
        # Test imports
        print("Testing imports...")
        
        import component_searcher
        print("✅ Main application imported")
        
        from scrapers import get_scraper
        print("✅ Scrapers imported")
        
        from gemini_ai_analyzer import get_gemini_analyzer
        print("✅ Gemini AI imported")
        
        # Test supplier loading
        print("\nTesting supplier configuration...")
        
        if os.path.exists("suppliers.json"):
            import json
            with open("suppliers.json", "r") as f:
                suppliers = json.load(f)
            
            total_suppliers = sum(len(suppliers.get(tier, [])) for tier in ['indian_tier1', 'indian_tier2', 'indian_additional'])
            print(f"✅ Suppliers loaded: {total_suppliers} total")
        else:
            print("⚠️ suppliers.json not found (will use defaults)")
        
        print("✅ Application components test passed")
        return True
        
    except Exception as e:
        print(f"❌ Application components test failed: {e}")
        return False

def show_usage_guide():
    """Show how to use the application"""
    print("\n" + "=" * 60)
    print("🚀 Your Gemini AI Component Search Application is Ready!")
    print("=" * 60)
    
    print("\n📋 HOW TO USE:")
    print("1. Run the application:")
    print("   python component_searcher.py")
    print()
    print("2. Configure Gemini AI (first time only):")
    print("   - Go to Tools → AI Configuration")
    print("   - Enter your API key: AIzaSyAHl-ePSQ6RqY2wi0IUj17JRcoALefRmlg")
    print("   - Test the connection")
    print()
    print("3. Search for components:")
    print("   - Enter component name (e.g., 'arduino uno', '10k resistor')")
    print("   - Choose search mode:")
    print("     • 🤖 Gemini AI Search (recommended)")
    print("     • 🔍 Smart Search")
    print("     • ⚡ Quick Search")
    print()
    print("4. Get intelligent results:")
    print("   - AI-powered component analysis")
    print("   - Quality-scored supplier results")
    print("   - Manufacturer recommendations")
    print("   - Real-time stock and pricing")
    
    print("\n🌟 KEY FEATURES:")
    print("• ⚡ Fast Gemini AI analysis (2-3 seconds)")
    print("• 🆓 Free usage (1,500 requests/day)")
    print("• 🇮🇳 25+ Indian suppliers prioritized")
    print("• 📊 Professional quality scoring")
    print("• 🏭 Smart manufacturer recommendations")
    print("• 📈 Real-time progress tracking")
    
    print("\n🔧 TROUBLESHOOTING:")
    print("• If search fails: Check internet connection")
    print("• If AI not working: Verify API key in Tools → AI Configuration")
    print("• If no results: Try different search terms or Quick Search mode")
    
    print("\n✅ ALL SYSTEMS READY!")

def main():
    """Run quick functionality test"""
    print("🧪 Quick Functionality Test")
    print("=" * 30)
    
    tests = [
        ("Scraper Functionality", test_scraper_functionality),
        ("Gemini AI Quick Test", test_gemini_quick),
        ("Application Components", test_application_components)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Quick Test Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Application is ready to use!")
        show_usage_guide()
    else:
        print("\n⚠️ Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    exit(0 if success else 1)
