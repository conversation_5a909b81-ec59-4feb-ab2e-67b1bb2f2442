#!/usr/bin/env python3
"""
Test Your Gemini API Key
Direct test of the provided Gemini API key for component analysis
"""

import json

def test_gemini_key():
    """Test the provided Gemini API key"""
    print("🔑 Testing Your Gemini API Key")
    print("=" * 35)
    
    # Your API key
    api_key = "AIzaSyAHl-ePSQ6RqY2wi0IUj17JRcoALefRmlg"
    
    try:
        # Import and configure Gemini
        import google.generativeai as genai
        genai.configure(api_key=api_key)
        
        # Create model
        model = genai.GenerativeModel('gemini-pro')
        print("✅ Gemini API configured with your key")
        
        # Test 1: Simple connection test
        print("\n🧪 Test 1: Simple connection test...")
        response = model.generate_content("Hello! Please respond with 'Gemini API test successful'")
        print(f"📝 Response: {response.text}")
        
        # Test 2: Component analysis
        print("\n🧪 Test 2: Component analysis test...")
        component = "arduino uno"
        
        prompt = f"""
Analyze this electronics component: "{component}"

Provide a structured analysis with:
1. Component Type
2. Manufacturer
3. Key Specifications
4. Common Package/Form Factor
5. Typical Applications
6. Compatible Alternatives

Keep response concise and technical.
"""
        
        response = model.generate_content(prompt)
        print(f"📋 Component Analysis for '{component}':")
        print("-" * 50)
        print(response.text)
        print("-" * 50)
        
        # Test 3: Multiple components quickly
        print("\n🧪 Test 3: Multiple component analysis...")
        
        test_components = ["10k resistor", "LM358 op amp", "ESP32"]
        
        for comp in test_components:
            print(f"\n🔍 Quick analysis: {comp}")
            quick_prompt = f"Briefly describe this electronics component: {comp}. Include type, typical use, and package."
            
            response = model.generate_content(quick_prompt)
            print(f"📝 {response.text[:200]}...")
        
        # Save the working configuration
        config = {
            "gemini_api_key": api_key,
            "test_date": "2025-01-27",
            "status": "working"
        }
        
        with open("gemini_config.json", "w") as f:
            json.dump(config, f, indent=2)
        
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ Your Gemini API key is working perfectly")
        print(f"💾 Configuration saved to gemini_config.json")
        print(f"🚀 Ready to integrate with component search application")
        
        return True
        
    except ImportError:
        print("❌ Google Generative AI library not installed")
        print("📦 Install with: pip install google-generativeai")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def show_integration_plan():
    """Show how this will be integrated"""
    print(f"\n🔮 Integration Plan")
    print(f"=" * 20)
    
    print(f"Now that your Gemini API key works, I will:")
    print(f"")
    print(f"1. 🔧 Update the AI analyzer to use Gemini")
    print(f"2. 🤖 Replace web automation with API calls")
    print(f"3. 📊 Add structured component analysis")
    print(f"4. 🏭 Implement manufacturer recommendations")
    print(f"5. 🔍 Enhance search with AI insights")
    print(f"6. 📈 Add quality scoring based on AI analysis")
    print(f"")
    print(f"Benefits you'll get:")
    print(f"• ⚡ Fast AI analysis (2-3 seconds)")
    print(f"• 🆓 Free usage (1,500 requests/day)")
    print(f"• 🧠 Smart component understanding")
    print(f"• 📋 Structured data extraction")
    print(f"• 🔄 Reliable service (no bot detection)")

if __name__ == "__main__":
    print("🚀 Testing Your Gemini API Key")
    print("=" * 35)
    
    if test_gemini_key():
        show_integration_plan()
        
        print(f"\n🎯 READY FOR INTEGRATION!")
        print(f"Your API key works perfectly for component analysis.")
        print(f"I can now build the full AI-powered component search application.")
    else:
        print(f"\n❌ API key test failed")
        print(f"Please check the key and try again.")
    
    input(f"\nPress Enter to continue...")
