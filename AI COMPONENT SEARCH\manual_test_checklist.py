#!/usr/bin/env python3
"""
Manual Testing Checklist
Quick systematic testing that I can run to verify everything works
"""

import requests
import json
import time

def test_server_running():
    """Test if server is running"""
    print("🔍 Testing server availability...")
    try:
        response = requests.get("http://localhost:8080", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and responding")
            return True
        else:
            print(f"❌ Server returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server not accessible: {e}")
        return False

def test_all_pages():
    """Test all main pages"""
    print("\n🧪 Testing all main pages...")
    
    pages = [
        ('/', 'Dashboard'),
        ('/components', 'Components'),
        ('/components/add', 'Add Component'),
        ('/ai-research', 'AI Research Lab'),
        ('/analytics', 'Analytics'),
        ('/system-info', 'System Info')
    ]
    
    results = []
    for path, name in pages:
        try:
            response = requests.get(f"http://localhost:8080{path}", timeout=10)
            if response.status_code == 200 and len(response.text) > 1000:
                print(f"✅ {name} - OK")
                results.append(True)
            else:
                print(f"❌ {name} - Status: {response.status_code}, Length: {len(response.text)}")
                results.append(False)
        except Exception as e:
            print(f"❌ {name} - Error: {e}")
            results.append(False)
    
    return all(results)

def test_api_endpoints():
    """Test API endpoints"""
    print("\n🧪 Testing API endpoints...")
    
    # Test GET API
    try:
        response = requests.get("http://localhost:8080/api/components", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Components API - Working")
                api_get_ok = True
            else:
                print("❌ Components API - Success=False")
                api_get_ok = False
        else:
            print(f"❌ Components API - Status: {response.status_code}")
            api_get_ok = False
    except Exception as e:
        print(f"❌ Components API - Error: {e}")
        api_get_ok = False
    
    # Test POST API
    try:
        response = requests.post("http://localhost:8080/api/ai/research", 
                               json={"component_name": "test"}, timeout=30)
        if response.status_code == 200:
            print("✅ AI Research API - Responding")
            api_post_ok = True
        else:
            print(f"❌ AI Research API - Status: {response.status_code}")
            api_post_ok = False
    except Exception as e:
        print(f"❌ AI Research API - Error: {e}")
        api_post_ok = False
    
    # Test detailed API
    try:
        response = requests.post("http://localhost:8080/api/ai/research-detailed", 
                               json={"component_name": "test"}, timeout=30)
        if response.status_code == 200:
            print("✅ AI Research Detailed API - Responding")
            api_detailed_ok = True
        else:
            print(f"❌ AI Research Detailed API - Status: {response.status_code}")
            api_detailed_ok = False
    except Exception as e:
        print(f"❌ AI Research Detailed API - Error: {e}")
        api_detailed_ok = False
    
    return api_get_ok and api_post_ok and api_detailed_ok

def test_form_submission():
    """Test form submission"""
    print("\n🧪 Testing form submission...")
    
    try:
        # Test add component
        form_data = {
            'name': 'Test Component',
            'type': 'Test',
            'description': 'Test component for validation'
        }
        
        response = requests.post("http://localhost:8080/components/add", 
                               data=form_data, timeout=10)
        
        if response.status_code in [200, 302]:
            print("✅ Form submission - Working")
            return True
        else:
            print(f"❌ Form submission - Status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Form submission - Error: {e}")
        return False

def run_quick_test():
    """Run quick systematic test"""
    print("🚀 QUICK SYSTEM TEST")
    print("=" * 40)
    
    tests = [
        ("Server Running", test_server_running),
        ("All Pages", test_all_pages),
        ("API Endpoints", test_api_endpoints),
        ("Form Submission", test_form_submission)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        result = test_func()
        results.append(result)
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 QUICK TEST RESULTS")
    print("=" * 40)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    print(f"📊 Success Rate: {passed/total*100:.1f}%")
    
    if all(results):
        print("🎉 ALL TESTS PASSED - SYSTEM READY!")
        return True
    else:
        print("🔧 SOME TESTS FAILED - NEEDS FIXING")
        return False

if __name__ == "__main__":
    success = run_quick_test()
    exit(0 if success else 1)
