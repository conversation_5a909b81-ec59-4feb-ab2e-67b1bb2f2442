#!/usr/bin/env python3
"""
Test AI Features
Verify AI analyzer and datasheet manager functionality
"""

import sys
import os
import traceback

def test_imports():
    """Test if all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        import tkinter as tk
        print("  ✅ tkinter - GUI framework")
    except ImportError as e:
        print(f"  ❌ tkinter - {e}")
        return False
    
    try:
        import requests
        print("  ✅ requests - HTTP client")
    except ImportError as e:
        print(f"  ❌ requests - {e}")
        return False
    
    try:
        from ai_analyzer import get_ai_analyzer
        print("  ✅ ai_analyzer - AI integration")
    except ImportError as e:
        print(f"  ❌ ai_analyzer - {e}")
        return False
    
    try:
        from datasheet_manager import get_datasheet_manager
        print("  ✅ datasheet_manager - Datasheet handling")
    except ImportError as e:
        print(f"  ❌ datasheet_manager - {e}")
        return False
    
    try:
        from component_intelligence import analyze_component_search
        print("  ✅ component_intelligence - Domain knowledge")
    except ImportError as e:
        print(f"  ❌ component_intelligence - {e}")
        return False
    
    return True

def test_ai_analyzer():
    """Test AI analyzer functionality"""
    print("\n🤖 Testing AI Analyzer...")
    
    try:
        from ai_analyzer import get_ai_analyzer
        
        analyzer = get_ai_analyzer()
        print(f"  ✅ AI Analyzer initialized")
        print(f"  📊 Active provider: {analyzer.active_provider}")
        print(f"  🔧 Available providers: {list(analyzer.providers.keys())}")
        
        # Test provider connections
        print("\n  🔗 Testing provider connections:")
        for provider_name in analyzer.providers:
            result = analyzer.test_provider_connection(provider_name)
            status = "✅" if result["success"] else "❌"
            print(f"    {status} {provider_name}: {result.get('status', result.get('error', 'Unknown'))}")
        
        # Test component analysis (if provider available)
        if analyzer.active_provider:
            print(f"\n  🔍 Testing component analysis with {analyzer.active_provider}:")
            try:
                analysis = analyzer.analyze_component_query("arduino uno")
                print(f"    ✅ Analysis completed")
                print(f"    📋 Component type: {analysis.component_type}")
                print(f"    🏭 Manufacturer: {analysis.manufacturer}")
                print(f"    📊 Confidence: {analysis.confidence_score:.2f}")
                print(f"    📝 Notes: {analysis.analysis_notes[:100]}...")
            except Exception as e:
                print(f"    ❌ Analysis failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ AI Analyzer test failed: {e}")
        traceback.print_exc()
        return False

def test_datasheet_manager():
    """Test datasheet manager functionality"""
    print("\n📄 Testing Datasheet Manager...")
    
    try:
        from datasheet_manager import get_datasheet_manager
        
        manager = get_datasheet_manager()
        print(f"  ✅ Datasheet Manager initialized")
        print(f"  📁 Database entries: {len(manager.datasheet_db)}")
        
        # Test directory creation
        required_dirs = ['datasheets', 'datasheets/downloaded', 'datasheets/analyzed', 'cache']
        for directory in required_dirs:
            if os.path.exists(directory):
                print(f"  ✅ Directory exists: {directory}")
            else:
                print(f"  ❌ Directory missing: {directory}")
        
        # Test manufacturer recommendations
        print("\n  🏭 Testing manufacturer recommendations:")
        manufacturers = manager.get_top_manufacturers("resistor", "10k")
        for i, mfg in enumerate(manufacturers[:3], 1):
            print(f"    #{i} {mfg['name']} - {mfg['reputation']} reputation, {mfg['availability']} availability")
        
        # Test datasheet search URLs
        print("\n  🔍 Testing datasheet URL search:")
        urls = manager.search_datasheet_urls("LM358", "Texas Instruments")
        print(f"    Found {len(urls)} potential datasheet sources")
        for url in urls[:3]:
            print(f"    📎 {url}")
        
        # Test statistics
        stats = manager.get_download_statistics()
        print(f"\n  📊 Download statistics:")
        print(f"    Total downloads: {stats['total_downloads']}")
        print(f"    Successful: {stats['successful_downloads']}")
        print(f"    Database entries: {stats['database_entries']}")
        print(f"    Total size: {stats['total_size_mb']} MB")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Datasheet Manager test failed: {e}")
        traceback.print_exc()
        return False

def test_component_intelligence():
    """Test component intelligence functionality"""
    print("\n🧠 Testing Component Intelligence...")
    
    try:
        from component_intelligence import analyze_component_search
        
        test_components = [
            "arduino uno",
            "10k resistor",
            "LM358 op amp",
            "ESP32",
            "100uF capacitor"
        ]
        
        for component in test_components:
            print(f"\n  🔍 Analyzing: '{component}'")
            analysis = analyze_component_search(component)
            
            print(f"    📋 Type: {analysis['component_type']}")
            print(f"    📦 Packages: {analysis['relevant_packages'][:3]}...")
            print(f"    ❓ Questions: {len(analysis['questions'])} clarifying questions")
            print(f"    🎯 Refinements: {len(analysis['refinements'])} suggestions")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Component Intelligence test failed: {e}")
        traceback.print_exc()
        return False

def test_main_application():
    """Test main application can be imported"""
    print("\n🖥️ Testing Main Application...")
    
    try:
        # Test import without running GUI
        import component_searcher
        print("  ✅ Main application imports successfully")
        
        # Check version constants
        print(f"  📋 App Version: {component_searcher.APP_VERSION}")
        print(f"  🖥️ UI Version: {component_searcher.UI_VERSION}")
        print(f"  ⚙️ Backend Version: {component_searcher.BACKEND_VERSION}")
        print(f"  📅 Build Date: {component_searcher.BUILD_DATE}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Main application test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 AI-Powered Component Search - Feature Test")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("AI Analyzer", test_ai_analyzer),
        ("Datasheet Manager", test_datasheet_manager),
        ("Component Intelligence", test_component_intelligence),
        ("Main Application", test_main_application)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Application is ready to use.")
        print("\n🚀 To start the application:")
        print("   python component_searcher.py")
        print("   or run: run_ai_component_searcher.bat")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        print("\n🔧 Common solutions:")
        print("   - Run: pip install -r requirements.txt")
        print("   - Install Ollama for AI features")
        print("   - Check internet connectivity")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
