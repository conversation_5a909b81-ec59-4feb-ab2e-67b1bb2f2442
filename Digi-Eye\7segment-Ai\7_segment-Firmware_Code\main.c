/**
 * Copyright (c) 2020 Raspberry Pi (Trading) Ltd.
 *
 * SPDX-License-Identifier: BSD-3-Clause
 */
#include <stdio.h>
#include<stdlib.h>
#include<string.h>
#include "pico/stdlib.h"
#include "hardware/watchdog.h"
#include "pico/bootrom.h"
#define plus 1
#define minus 2
#define clear 0
#define invalid "invalid\r\n"
uint8_t d_1[4] = {0, 1, 2, 3};
uint8_t d_2[4] = {4, 5, 6, 7};
uint8_t d_3[4] = {8, 9, 10, 11};
uint8_t dp_pin[3] = {15,14,13};
//uint8_t digit_pin[12]={0, 1, 2, 3,4, 5, 6, 7,8, 9, 10, 11};
uint8_t k=18;
#define x 16
#define y 17
#define low_bat  19
bool buf=false;  //buffer flag
void read_string(char* buffer){
    uint16_t buffer_index=0;
    while(true){
        int c = getchar_timeout_us(100);
        if(c!= PICO_ERROR_TIMEOUT && buffer_index < 20){
            buffer[buffer_index++] = (c & 0xFF);buf=true;
        }
        else {
            break;
        }
    }
    buffer[buffer_index] = '\0';
}
bool digit(char *val){
    int f[3] = {val[0]-'0',val[1]-'0',val[2]-'0'};
    for(uint8_t i =0;i<3;i++){
        //printf("%d\n",f[i]);
        if(f[i]>9||f[i]<0){
            return true;
        }
    }
    for(uint8_t i =0;i<3;i++){
        for (uint8_t k = 0; k < 4; k++) {
            if (f[i] & (0x01 << k)) {
                gpio_put((k+(i*4)), true);
            }
            else {
                gpio_put((k+(i*4)), false);
            }
        }
    }
   return false; 
}
bool digit_1(int val) {
    if(val<10&&val>=0){
        for (uint8_t k = 0; k < 4; k++) {
            if (val & (0x01 << k)) {
                gpio_put(d_1[k], true);
            }
            else {
                gpio_put(d_1[k], false);
            }
        }
        return false;
    }
    else {
        return true;
    }
}
bool digit_2(int val) {
    if(val<10&&val>=0){
        for (uint8_t k = 0; k < 4; k++) {
            if (val & (0x01 << k)) {
                gpio_put(d_2[k], true);
            }
            else {
                gpio_put(d_2[k], false);
            }
        }
        return false;
    }
    else {
        return true;
    }
}
bool digit_3(int val) {
    if(val<10&&val>=0){
        for (uint8_t k = 0; k < 4; k++) {
            if (val & (0x01 << k)) {
                gpio_put(d_3[k], true);
            }
            else {
                gpio_put(d_3[k], false);
            }
        }
        return false;
    }
    else {
        return true;
    }
}
void dp(uint8_t i){
    for(uint8_t o=0;o<3;o++){
        gpio_put(dp_pin[o],false);
    }
    if(i==4){

    }
    else {
        gpio_put(dp_pin[i],true);
    }
}
void one(bool i){
    gpio_put(k,i);
}
void sign(uint8_t i){
    if(i==plus){
        gpio_put(x,true);
        gpio_put(y,true);
    }
    else if(i==minus){
        gpio_put(x,false);
        gpio_put(y,true);
    }
    else if(i==clear){
        gpio_put(x,false);
        gpio_put(y,false);
    }

}
void init(void){
    sign(plus);
    one(true);
    digit("888");
}
int main() {

    const uint LED_PIN = 25;
    gpio_init(LED_PIN);
    gpio_set_dir(LED_PIN, GPIO_OUT);
    //gpio_pull_down(LED_PIN);
  for (uint8_t i = 0; i < 20; i++) {
    gpio_init(i);
    gpio_set_dir(i, GPIO_OUT);
  }
   // watchdog_enable(5000, 1);
    stdio_init_all();
    gpio_put(LED_PIN, 1);
    init();
    sleep_ms(1000);
    while (true) {

        char data[20];
        read_string(data);
        if(buf){
            buf = false;
            char *cmd = strtok(data, "\0");
            char j[10];
            strcpy(j,cmd);
            //printf("data=%s\n",cmd);
            char *my_data = strtok(j,".");
            uint8_t len = strlen(my_data);
            if(!strcmp(cmd,"boot\r\n")){
                reset_usb_boot(0,0);
            }
            else if(!strcmp(cmd,"lowbat\r\n")){
                gpio_put(low_bat,true);
                printf("0\r\n");
            }
            else if(!strcmp(cmd,"no_lowbat\r\n")){
                gpio_put(low_bat,false);
                printf("0\r\n");
            }
            else if(my_data[len-1]=='\n'){
                if(len==6){
                    sign(clear);
                    if(my_data[0]=='1'||my_data[0]==' '){
                        
                        char l[4];
                        l[0] = my_data[1];l[1] = my_data[2];
                        l[2] = my_data[3];l[3] = '\0';
                        if(digit(l)){
                        //if(digit_1(my_data[1]-'0')||digit_2(my_data[2]-'0')||digit_3(my_data[3]-'0')){
                            printf("invalid\r\n");
                        }
                        else {
                            sign(clear);
                            dp(4);
                            gpio_put(k,false);
                            if(my_data[0]=='1'){
                                gpio_put(k,true);
                            }
                            printf("0\r\n");
                        }
                    }
                    else {
                        printf("invalid\r\n");
                    }
                }
                else if(len==7){
                    if(my_data[0]=='+'||my_data[0]=='-'){

                        if(my_data[1]=='1'||my_data[1]==' '){
                            char l[4];
                            l[0] = my_data[2];l[1] = my_data[3];
                            l[2] = my_data[4];l[3] = '\0';
                            if(digit(l)){
                            //if(digit_1(my_data[2]-'0')||digit_2(my_data[3]-'0')||digit_3(my_data[4]-'0')){
                                printf("invalid\r\n");
                            }
                            else {
                                if(my_data[0]=='+'){
                                    sign(plus);
                                }
                                else{
                                    sign(minus);
                                }
                                gpio_put(k,true);
                                if(my_data[1]==' '){
                                    gpio_put(k,false);
                                }
                                dp(4);
                                printf("0\r\n");
                            }
                        }
                        else {
                            printf("invalid\r\n");
                        }                          
                    }
                    else {
                        printf("invalid\r\n");
                    }                                                     
                }
                else if(len == 5){
                    char l[4];
                    l[0] = my_data[0];l[1] = my_data[1];
                    l[2] = my_data[2];l[4] = '\0';
                    if(digit(l)){                   
                    //if(digit_1(my_data[0]-'0')||digit_2(my_data[1]-'0')||digit_3(my_data[2]-'0')){
                        printf("invalid\r\n");
                    }
                    else {
                        sign(clear);
                        gpio_put(k,false);
                        dp(4);
                        printf("0\r\n");
                    }
                }                
                else {
                    printf("invalid\r\n");
                }
            }
            //printf("%s",my_data);
            else {
                char *arg1 = strtok(NULL, " ");
                len = strlen(my_data);
                uint8_t len1= strlen(arg1);
                //printf("%s,%s\n",arg1,my_data);
                //arg1[len1++]='\n';
                
                if(len){
                    //printf("%d %d\n",len,len1);
                if(my_data[0]=='+'||my_data[0]=='-'){
                    if(len+len1==7){
                        
                        if(my_data[1]=='1'||my_data[1]==' '){
                            
                            char l[4];
                            uint8_t i =0;
                            for(i=0;i<len-2;i++){
                                l[i]=my_data[2+i];
                            }
                            for(uint8_t f=0;f<len1-2;f++){
                                l[i+f]=arg1[f];
                            }
                            l[3] = '\0';
                            //printf("%s",arg1);
                            if(digit(l)){
                        //if(digit_1(my_data[2]-'0')||digit_2(my_data[3]-'0')||digit_3(my_data[4]-'0')){
                                printf("invalid\r\n");
                            }
                            else {                               
                                if(my_data[0]=='+'){
                                    sign(plus);
                                }
                                else{
                                    sign(minus);
                                }
                                if(my_data[1]=='1'){
                                    gpio_put(k,true);
                                }
                                else {
                                    gpio_put(k,false);
                                }
                                
                                dp(len-2);
                                printf("0\r\n");
                            }
                        }
                        else {
                            printf("invalid\r\n");
                        }
                    }
                    else {
                        printf("invalid\r\n");
                    }                          
                }
                else if(my_data[0]=='1'){
                    if(len+len1==6){
                        
                        char l[4];
                        uint8_t i =0;
                        for(i=0;i<len-1;i++){
                            l[i]=my_data[1+i];
                        }
                        for(uint8_t f=0;f<len1-2;f++){
                            l[i+f]=arg1[f];
                        }
                        l[3] = '\0';
                        //printf("%s",l);
                        if(digit(l)){
                        //if(digit_1(my_data[2]-'0')||digit_2(my_data[3]-'0')||digit_3(my_data[4]-'0')){
                            printf("invalid\r\n");
                        }
                        else {  
                            gpio_put(k,true);                           
                            sign(clear);
                            dp(len-1);
                            printf("0\r\n");
                        }
                    }
                    else {
                        printf("invalid\r\n");
                    }
                }
                else if(len+len1==5){
                        char l[4];
                        uint8_t i =0;
                        for(i=0;i<len;i++){
                            l[i]=my_data[i];
                        }
                        for(uint8_t f=0;f<len1-2;f++){
                            l[i+f]=arg1[f];
                        }
                        l[3] = '\0';
                        //printf("%s",l);
                        if(digit(l)){
                        //if(digit_1(my_data[2]-'0')||digit_2(my_data[3]-'0')||digit_3(my_data[4]-'0')){
                            printf("invalid\r\n");
                        }
                        else {  
                            gpio_put(k,false);                           
                            sign(clear);
                            dp(len);
                            printf("0\r\n");
                        }  
                }
                else {
                    printf("invalid\r\n");
                }
                
            }
            }
            //printf("%d",strlen(my_data));
        }
    }
    return 0;
}