#!/usr/bin/env python3
"""
Test Gemini API with Working Model
Uses gemini-1.5-flash which is fast and reliable
"""

import json

def test_gemini_working():
    """Test the Gemini API key with a working model"""
    print("🔑 Testing Your Gemini API Key (Working Version)")
    print("=" * 45)
    
    # Your API key
    api_key = "AIzaSyAHl-ePSQ6RqY2wi0IUj17JRcoALefRmlg"
    
    try:
        # Import and configure Gemini
        import google.generativeai as genai
        genai.configure(api_key=api_key)
        
        print("✅ Gemini API configured with your key")
        
        # Use gemini-1.5-flash (fast and reliable)
        model_name = "gemini-1.5-flash"
        model = genai.GenerativeModel(model_name)
        print(f"🤖 Using model: {model_name}")
        
        # Test 1: Simple connection test
        print("\n🧪 Test 1: Simple connection test...")
        response = model.generate_content("Hello! Please respond with 'Gemini API test successful'")
        print(f"📝 Response: {response.text}")
        
        # Test 2: Component analysis
        print("\n🧪 Test 2: Component analysis test...")
        component = "arduino uno"
        
        prompt = f"""
Analyze this electronics component: "{component}"

Provide a structured analysis with:
1. Component Type
2. Manufacturer  
3. Key Specifications
4. Common Package/Form Factor
5. Typical Applications
6. Compatible Alternatives

Keep response concise and technical.
"""
        
        response = model.generate_content(prompt)
        print(f"📋 Component Analysis for '{component}':")
        print("-" * 50)
        print(response.text)
        print("-" * 50)
        
        # Test 3: Structured component data extraction
        print("\n🧪 Test 3: Structured data extraction...")
        
        structured_prompt = f"""
Analyze "10k resistor" and respond in this exact JSON format:
{{
    "component_type": "resistor",
    "value": "10k ohms",
    "tolerance": "typical tolerance",
    "power_rating": "typical power",
    "package_types": ["through-hole", "SMD"],
    "applications": ["pull-up", "voltage divider"],
    "manufacturers": ["Vishay", "Yageo"]
}}
"""
        
        response = model.generate_content(structured_prompt)
        print(f"📊 Structured Analysis:")
        print(response.text)
        
        # Test 4: Multiple components quickly
        print("\n🧪 Test 4: Quick multiple component analysis...")
        
        test_components = ["LM358 op amp", "ESP32", "100uF capacitor"]
        
        for comp in test_components:
            print(f"\n🔍 Quick analysis: {comp}")
            quick_prompt = f"In 1-2 sentences, describe {comp}: type, typical use, and package."
            
            response = model.generate_content(quick_prompt)
            print(f"📝 {response.text}")
        
        # Save the working configuration
        config = {
            "gemini_api_key": api_key,
            "working_model": model_name,
            "test_date": "2025-01-27",
            "status": "working",
            "api_version": "v1beta"
        }
        
        with open("gemini_config.json", "w") as f:
            json.dump(config, f, indent=2)
        
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ Your Gemini API key is working perfectly")
        print(f"🤖 Using fast model: {model_name}")
        print(f"💾 Configuration saved to gemini_config.json")
        print(f"🚀 Ready to integrate with component search application")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def show_performance_stats():
    """Show expected performance"""
    print(f"\n📊 Performance Stats")
    print(f"=" * 20)
    
    print(f"With your Gemini API key:")
    print(f"• ⚡ Response time: 2-3 seconds")
    print(f"• 🆓 Daily limit: 1,500 requests")
    print(f"• 📈 Monthly limit: 1 million tokens")
    print(f"• 🔄 Rate limit: 15 requests/minute")
    print(f"• 💰 Cost: FREE")
    print(f"")
    print(f"Perfect for component search:")
    print(f"• 🔍 ~10 searches per day = 10 API calls")
    print(f"• 📊 Each search uses ~100 tokens")
    print(f"• 🎯 Total monthly usage: ~30,000 tokens")
    print(f"• ✅ Well within free limits!")

def show_integration_preview():
    """Show what the integration will look like"""
    print(f"\n🔮 Integration Preview")
    print(f"=" * 25)
    
    print(f"Your enhanced component search will work like this:")
    print(f"")
    print(f"1. 👤 User enters: 'arduino uno'")
    print(f"2. 🤖 Gemini analyzes: 'Microcontroller development board'")
    print(f"3. 📊 AI extracts: Specifications, packages, alternatives")
    print(f"4. 🏭 AI suggests: Top manufacturers (Arduino, SparkFun, etc.)")
    print(f"5. 🔍 Enhanced search: Searches Indian suppliers with AI insights")
    print(f"6. 📈 Quality scoring: AI-powered result validation")
    print(f"7. 📋 Smart results: Organized by relevance and quality")
    print(f"")
    print(f"User experience:")
    print(f"• 🚀 Faster searches with AI intelligence")
    print(f"• 🎯 More accurate results")
    print(f"• 📊 Better component understanding")
    print(f"• 🔄 Smarter alternative suggestions")

if __name__ == "__main__":
    print("🚀 Testing Your Gemini API Key (Final Test)")
    print("=" * 50)
    
    if test_gemini_working():
        show_performance_stats()
        show_integration_preview()
        
        print(f"\n🎯 SUCCESS!")
        print(f"Your Gemini API key works perfectly!")
        print(f"Ready to build the AI-powered component search application!")
        
        print(f"\n🚀 READY TO PROCEED!")
        print(f"I can now integrate this into your component search application.")
        print(f"You'll have fast, free, reliable AI component analysis!")
    else:
        print(f"\n❌ API key test failed")
        print(f"Please check your internet connection and try again.")
    
    input(f"\nPress Enter to continue...")
