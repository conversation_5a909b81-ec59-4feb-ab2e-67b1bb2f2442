#!/usr/bin/env python3
"""
Verify Gemini API Installation
Quick verification that Gemini API is properly installed
"""

def test_gemini_import():
    """Test if Gemini can be imported"""
    print("🧪 Testing Gemini API import...")
    
    try:
        import google.generativeai as genai
        print("✅ Google Generative AI library imported successfully")
        print(f"📦 Library version: {genai.__version__ if hasattr(genai, '__version__') else 'Unknown'}")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def show_setup_instructions():
    """Show setup instructions for Gemini API"""
    print("\n📋 Google Gemini API Setup Instructions")
    print("=" * 45)
    
    print("\n🔑 Step 1: Get Your Free API Key")
    print("1. Go to: https://aistudio.google.com/")
    print("2. Sign in with your Google account")
    print("3. Click 'Get API key' or 'Create API key'")
    print("4. Choose 'Create API key in new project'")
    print("5. Copy the API key (starts with 'AIza...')")
    
    print("\n💰 Step 2: Free Tier Benefits")
    print("• 15 requests per minute")
    print("• 1,500 requests per day")
    print("• 1 million tokens per month")
    print("• No credit card required")
    print("• No expiration")
    
    print("\n🧪 Step 3: Test Your API Key")
    print("Once you have your API key, you can test it with:")
    print("```python")
    print("import google.generativeai as genai")
    print("genai.configure(api_key='YOUR_API_KEY_HERE')")
    print("model = genai.GenerativeModel('gemini-pro')")
    print("response = model.generate_content('Hello!')")
    print("print(response.text)")
    print("```")
    
    print("\n🔧 Step 4: Integration with Component Search")
    print("After testing, the API key will be integrated into:")
    print("• AI-powered component analysis")
    print("• Intelligent search refinements")
    print("• Specification extraction")
    print("• Manufacturer recommendations")

def show_gemini_advantages():
    """Show advantages of Gemini API"""
    print("\n🌟 Why Gemini API is Perfect for Component Search")
    print("=" * 55)
    
    advantages = [
        ("🆓 Completely Free", "No cost for component analysis"),
        ("⚡ Fast Response", "2-3 seconds vs 10-30 for local AI"),
        ("🧠 Smart Analysis", "Excellent at technical component data"),
        ("🔄 Always Updated", "Latest AI model automatically"),
        ("📊 Reliable", "99.9% uptime, enterprise-grade"),
        ("🔧 Easy Setup", "Just one API key, no installation"),
        ("💻 Low Resource", "No local GPU or RAM requirements"),
        ("🌐 Global Access", "Works from anywhere with internet")
    ]
    
    for title, description in advantages:
        print(f"  {title}: {description}")
    
    print(f"\n📈 Perfect for Component Analysis:")
    print(f"• Understands electronics terminology")
    print(f"• Extracts specifications accurately")
    print(f"• Suggests compatible alternatives")
    print(f"• Identifies package types")
    print(f"• Recommends manufacturers")

def main():
    """Main verification function"""
    print("🤖 Gemini API Verification")
    print("=" * 30)
    
    # Test import
    if test_gemini_import():
        print("\n✅ Gemini API is properly installed!")
        print("🎯 Ready for integration with component search")
        
        show_setup_instructions()
        show_gemini_advantages()
        
        print(f"\n🚀 NEXT STEPS:")
        print(f"1. Get your free API key from https://aistudio.google.com/")
        print(f"2. Test the API key with the provided code")
        print(f"3. Integrate with the component search application")
        print(f"4. Enjoy AI-powered component analysis!")
        
        return True
    else:
        print("\n❌ Gemini API installation failed")
        print("🔧 Try: pip install google-generativeai")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎉 SUCCESS: Gemini API is ready to use!")
        print(f"💡 This is the perfect solution for fast, free AI component analysis")
    else:
        print(f"\n❌ FAILED: Please install the required library")
    
    print(f"\nPress Enter to continue...")
    input()
