#!/usr/bin/env python3
"""
Enhanced Tab Buttons - Modern, Professional Design
Beautiful workflow tabs with improved styling
"""

import tkinter as tk
from tkinter import ttk

class EnhancedTabButtons:
    """Modern, professional workflow tab buttons"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("✨ Enhanced Tab Buttons - Modern Design")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#ffffff')
        self.root.state('zoomed')
        
        self.current_step = 1
        self.setup_ui()
    
    def setup_ui(self):
        """Setup UI with enhanced tab buttons"""
        
        # Enhanced workflow tabs
        self.setup_modern_workflow_tabs()
        
        # Main content
        self.main_content = tk.Frame(self.root, bg='#ffffff')
        self.main_content.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Demo content
        self.show_demo_content()
    
    def setup_modern_workflow_tabs(self):
        """Create modern, professional workflow tabs"""
        
        # Tab container with gradient-like background
        tab_container = tk.Frame(self.root, bg='#f8f9fa', height=120)
        tab_container.pack(fill='x', padx=20, pady=15)
        tab_container.pack_propagate(False)
        
        # Title with better styling
        title_frame = tk.Frame(tab_container, bg='#f8f9fa')
        title_frame.pack(fill='x', pady=8)
        
        tk.Label(title_frame, text="✨ Modern Component Search Workflow", 
                font=('Arial', 16, 'bold'), bg='#f8f9fa', fg='#2c3e50').pack()
        
        tk.Label(title_frame, text="Click any step to navigate directly", 
                font=('Arial', 10), bg='#f8f9fa', fg='#7f8c8d').pack()
        
        # Tabs container
        tabs_frame = tk.Frame(tab_container, bg='#f8f9fa')
        tabs_frame.pack(expand=True, fill='x', pady=12)
        
        # Enhanced tab definitions
        self.tabs = [
            {"num": "1", "icon": "🔍", "title": "Search", "desc": "Enter component", "step": 1},
            {"num": "2", "icon": "🤖", "title": "AI Analysis", "desc": "Review insights", "step": 2},
            {"num": "3", "icon": "📊", "title": "Results", "desc": "Compare options", "step": 3},
            {"num": "4", "icon": "📄", "title": "Details", "desc": "View specs", "step": 4},
            {"num": "5", "icon": "✅", "title": "Decision", "desc": "Select & export", "step": 5}
        ]
        
        self.tab_widgets = []
        
        # Create modern tabs
        for i, tab in enumerate(self.tabs):
            
            # Outer container for shadow effect
            tab_outer = tk.Frame(tabs_frame, bg='#f8f9fa')
            tab_outer.pack(side='left', expand=True, fill='both', padx=10, pady=5)
            
            # Shadow frame (bottom-right offset)
            shadow_frame = tk.Frame(tab_outer, bg='#bdc3c7', height=85)
            shadow_frame.place(x=3, y=3, relwidth=1, relheight=1)
            
            # Main tab frame
            tab_frame = tk.Frame(tab_outer, bg='#ffffff', relief='flat', bd=0, cursor='hand2')
            tab_frame.place(x=0, y=0, relwidth=1, relheight=1)
            
            # Make entire tab clickable
            tab_frame.bind('<Button-1>', lambda e, step=tab['step']: self.go_to_step(step))
            
            # Top section with number and icon
            top_section = tk.Frame(tab_frame, bg='#ffffff')
            top_section.pack(fill='x', pady=8)
            top_section.bind('<Button-1>', lambda e, step=tab['step']: self.go_to_step(step))
            
            # Number badge (circular)
            number_frame = tk.Frame(top_section, bg='#ffffff')
            number_frame.pack()
            number_frame.bind('<Button-1>', lambda e, step=tab['step']: self.go_to_step(step))
            
            # Create circular number badge
            number_canvas = tk.Canvas(number_frame, width=40, height=40, 
                                    bg='#ffffff', highlightthickness=0)
            number_canvas.pack()
            number_canvas.bind('<Button-1>', lambda e, step=tab['step']: self.go_to_step(step))
            
            # Draw circle
            circle_id = number_canvas.create_oval(5, 5, 35, 35, 
                                                fill='#ecf0f1', outline='#bdc3c7', width=2)
            
            # Add number text
            text_id = number_canvas.create_text(20, 20, text=tab['num'], 
                                              font=('Arial', 14, 'bold'), fill='#7f8c8d')
            
            # Icon below number
            icon_label = tk.Label(top_section, text=tab['icon'], 
                                 font=('Arial', 16), bg='#ffffff')
            icon_label.pack(pady=2)
            icon_label.bind('<Button-1>', lambda e, step=tab['step']: self.go_to_step(step))
            
            # Title
            title_label = tk.Label(tab_frame, text=tab['title'], 
                                  font=('Arial', 11, 'bold'),
                                  bg='#ffffff', fg='#2c3e50')
            title_label.pack(pady=2)
            title_label.bind('<Button-1>', lambda e, step=tab['step']: self.go_to_step(step))
            
            # Description
            desc_label = tk.Label(tab_frame, text=tab['desc'], 
                                 font=('Arial', 9),
                                 bg='#ffffff', fg='#7f8c8d')
            desc_label.pack(pady=(0, 8))
            desc_label.bind('<Button-1>', lambda e, step=tab['step']: self.go_to_step(step))
            
            self.tab_widgets.append({
                'outer': tab_outer,
                'shadow': shadow_frame,
                'frame': tab_frame,
                'canvas': number_canvas,
                'circle_id': circle_id,
                'text_id': text_id,
                'icon': icon_label,
                'title': title_label,
                'desc': desc_label
            })
            
            # Modern arrow between tabs
            if i < len(self.tabs) - 1:
                arrow_frame = tk.Frame(tabs_frame, bg='#f8f9fa', width=25)
                arrow_frame.pack(side='left', fill='y', pady=25)
                arrow_frame.pack_propagate(False)
                
                # Create modern arrow
                arrow_canvas = tk.Canvas(arrow_frame, width=25, height=30, 
                                       bg='#f8f9fa', highlightthickness=0)
                arrow_canvas.pack(expand=True)
                
                # Draw arrow shape
                arrow_canvas.create_polygon([5, 15, 15, 10, 15, 20], 
                                          fill='#bdc3c7', outline='#95a5a6')
        
        # Update initial appearance
        self.update_modern_tab_appearance()
    
    def update_modern_tab_appearance(self):
        """Update modern tab appearance based on current step"""
        
        for i, tab_widget in enumerate(self.tab_widgets):
            step_num = i + 1
            canvas = tab_widget['canvas']
            circle_id = tab_widget['circle_id']
            text_id = tab_widget['text_id']
            
            if step_num == self.current_step:
                # Current step - MODERN BLUE THEME
                tab_widget['shadow'].config(bg='#2980b9')
                tab_widget['frame'].config(bg='#3498db')
                canvas.config(bg='#3498db')
                canvas.itemconfig(circle_id, fill='#ffffff', outline='#2980b9', width=3)
                canvas.itemconfig(text_id, fill='#2980b9', font=('Arial', 16, 'bold'))
                tab_widget['icon'].config(bg='#3498db', fg='#ffffff', font=('Arial', 18))
                tab_widget['title'].config(bg='#3498db', fg='#ffffff', font=('Arial', 12, 'bold'))
                tab_widget['desc'].config(bg='#3498db', fg='#ecf0f1')
                
            elif step_num < self.current_step:
                # Completed step - MODERN GREEN THEME
                tab_widget['shadow'].config(bg='#27ae60')
                tab_widget['frame'].config(bg='#2ecc71')
                canvas.config(bg='#2ecc71')
                canvas.itemconfig(circle_id, fill='#ffffff', outline='#27ae60', width=3)
                canvas.itemconfig(text_id, fill='#27ae60', font=('Arial', 16, 'bold'))
                tab_widget['icon'].config(bg='#2ecc71', fg='#ffffff', font=('Arial', 18))
                tab_widget['title'].config(bg='#2ecc71', fg='#ffffff', font=('Arial', 12, 'bold'))
                tab_widget['desc'].config(bg='#2ecc71', fg='#d5f4e6')
                
            else:
                # Future step - ELEGANT GRAY THEME
                tab_widget['shadow'].config(bg='#bdc3c7')
                tab_widget['frame'].config(bg='#ffffff')
                canvas.config(bg='#ffffff')
                canvas.itemconfig(circle_id, fill='#ecf0f1', outline='#bdc3c7', width=2)
                canvas.itemconfig(text_id, fill='#7f8c8d', font=('Arial', 14, 'bold'))
                tab_widget['icon'].config(bg='#ffffff', fg='#7f8c8d', font=('Arial', 16))
                tab_widget['title'].config(bg='#ffffff', fg='#2c3e50', font=('Arial', 11, 'bold'))
                tab_widget['desc'].config(bg='#ffffff', fg='#7f8c8d')
    
    def show_demo_content(self):
        """Show demo content"""
        
        # Clear content
        for widget in self.main_content.winfo_children():
            widget.destroy()
        
        # Demo header
        header_frame = tk.Frame(self.main_content, bg='#ffffff')
        header_frame.pack(fill='x', pady=20)
        
        step_names = ["🔍 Component Search", "🤖 AI Analysis", "📊 Search Results", "📄 Component Details", "✅ Final Decision"]
        current_name = step_names[self.current_step - 1]
        
        tk.Label(header_frame, text=current_name, 
                font=('Arial', 24, 'bold'), bg='#ffffff', fg='#2c3e50').pack(side='left')
        
        tk.Label(header_frame, text=f"Step {self.current_step} of 5", 
                font=('Arial', 14), bg='#ffffff', fg='#7f8c8d').pack(side='right')
        
        # Demo content area
        content_frame = tk.Frame(self.main_content, bg='#f8f9fa', relief='flat', bd=0)
        content_frame.pack(fill='both', expand=True, pady=20)
        
        # Large demo text
        demo_text = tk.Text(content_frame, font=('Arial', 12), height=20,
                           bg='#ffffff', relief='flat', padx=30, pady=30)
        demo_text.pack(fill='both', expand=True, padx=30, pady=30)
        
        demo_content = f"""✨ ENHANCED TAB DESIGN DEMO

CURRENT STEP: {current_name}

🎨 DESIGN IMPROVEMENTS:
✅ Modern shadow effects for depth
✅ Circular number badges with better styling
✅ Larger, more prominent icons
✅ Professional color schemes
✅ Smooth visual transitions
✅ Better spacing and typography

🖱️ INTERACTION:
• Click any tab to navigate directly
• Visual feedback on hover and selection
• Clear current/completed/future states
• Professional appearance

🎯 TAB STATES:
• Current Step: Blue theme with white circle
• Completed Steps: Green theme with checkmark feel
• Future Steps: Clean gray theme, still clickable

💡 FEATURES:
• Shadow effects for modern depth
• Circular number badges
• Icon + text combination
• Professional color palette
• Responsive design
• Smooth state transitions

Click any tab above to test the navigation!"""
        
        demo_text.insert('1.0', demo_content)
        demo_text.config(state='disabled')
        
        # Navigation buttons
        nav_frame = tk.Frame(self.main_content, bg='#ffffff')
        nav_frame.pack(fill='x', pady=20)
        
        if self.current_step > 1:
            tk.Button(nav_frame, text="⬅️ Previous", 
                     command=lambda: self.go_to_step(self.current_step - 1),
                     bg='#95a5a6', fg='white', font=('Arial', 12),
                     padx=20, pady=10, relief='flat', bd=0).pack(side='left')
        
        if self.current_step < 5:
            tk.Button(nav_frame, text="Next ➡️", 
                     command=lambda: self.go_to_step(self.current_step + 1),
                     bg='#3498db', fg='white', font=('Arial', 12, 'bold'),
                     padx=20, pady=10, relief='flat', bd=0).pack(side='right')
    
    def go_to_step(self, step):
        """Navigate to specific step"""
        self.current_step = step
        self.update_modern_tab_appearance()
        self.show_demo_content()
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

if __name__ == "__main__":
    print("✨ Enhanced Tab Buttons Demo")
    print("=" * 30)
    print("🎨 Modern, professional tab design")
    print("✅ Shadow effects and depth")
    print("✅ Circular number badges")
    print("✅ Better icons and typography")
    print("✅ Professional color schemes")
    print("✅ Smooth visual transitions")
    
    app = EnhancedTabButtons()
    app.run()
