#!/usr/bin/env python3
"""
Test Google Gemini API
Simple test to verify Gemini API works for component analysis
"""

import os
import json
import requests
import time

class GeminiTester:
    """Test Google Gemini API functionality"""
    
    def __init__(self, api_key=None):
        self.api_key = api_key or os.getenv('GEMINI_API_KEY')
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"
    
    def test_api_connection(self):
        """Test if API key works"""
        print("🔑 Testing Gemini API connection...")
        
        if not self.api_key:
            print("❌ No API key provided")
            print("💡 Set GEMINI_API_KEY environment variable or pass it directly")
            return False
        
        # Simple test query
        test_data = {
            "contents": [{
                "parts": [{"text": "Hello! Please respond with just 'API test successful' to confirm you're working."}]
            }]
        }
        
        try:
            response = requests.post(
                f"{self.base_url}?key={self.api_key}",
                headers={"Content-Type": "application/json"},
                json=test_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and result['candidates']:
                    text = result['candidates'][0]['content']['parts'][0]['text']
                    print(f"✅ API connection successful!")
                    print(f"📝 Response: {text}")
                    return True
                else:
                    print(f"❌ Unexpected response format: {result}")
                    return False
            else:
                print(f"❌ API request failed: {response.status_code}")
                print(f"📝 Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Connection test failed: {e}")
            return False
    
    def analyze_component(self, component_query):
        """Analyze a component using Gemini"""
        print(f"\n🔍 Analyzing component: '{component_query}'")
        
        # Create analysis prompt
        prompt = f"""
Analyze this electronics component: "{component_query}"

Please provide a structured analysis in JSON format with these fields:
{{
    "component_type": "type of component (resistor, capacitor, IC, etc.)",
    "manufacturer": "likely manufacturer if identifiable",
    "specifications": {{"key": "value pairs of specifications"}},
    "package_options": ["list of common package types"],
    "applications": ["typical use cases"],
    "alternatives": ["similar components"],
    "confidence": "confidence level 0.0-1.0"
}}

Keep the response technical and concise.
"""
        
        data = {
            "contents": [{
                "parts": [{"text": prompt}]
            }]
        }
        
        try:
            response = requests.post(
                f"{self.base_url}?key={self.api_key}",
                headers={"Content-Type": "application/json"},
                json=data,
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and result['candidates']:
                    text = result['candidates'][0]['content']['parts'][0]['text']
                    print(f"✅ Analysis completed!")
                    print(f"📝 Raw response:")
                    print(text)
                    
                    # Try to extract JSON
                    try:
                        # Look for JSON in the response
                        import re
                        json_match = re.search(r'\{.*\}', text, re.DOTALL)
                        if json_match:
                            analysis_json = json.loads(json_match.group())
                            print(f"\n📊 Parsed Analysis:")
                            for key, value in analysis_json.items():
                                print(f"  {key}: {value}")
                            return analysis_json
                        else:
                            print("⚠️ No JSON found in response, but got text analysis")
                            return {"raw_analysis": text, "success": True}
                    except json.JSONDecodeError:
                        print("⚠️ Could not parse JSON, but got analysis")
                        return {"raw_analysis": text, "success": True}
                else:
                    print(f"❌ No response content")
                    return None
            else:
                print(f"❌ Analysis failed: {response.status_code}")
                print(f"📝 Error: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            return None
    
    def test_rate_limits(self):
        """Test API rate limits"""
        print(f"\n⏱️ Testing rate limits...")
        
        # Send a few quick requests
        for i in range(3):
            print(f"📤 Request {i+1}/3...")
            
            data = {
                "contents": [{
                    "parts": [{"text": f"Quick test {i+1}. Just say 'OK {i+1}'."}]
                }]
            }
            
            start_time = time.time()
            
            try:
                response = requests.post(
                    f"{self.base_url}?key={self.api_key}",
                    headers={"Content-Type": "application/json"},
                    json=data,
                    timeout=10
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status_code == 200:
                    print(f"  ✅ Success in {response_time:.2f}s")
                else:
                    print(f"  ❌ Failed: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ Error: {e}")
            
            # Small delay between requests
            time.sleep(1)
        
        print(f"✅ Rate limit test completed")

def setup_api_key():
    """Help user set up API key"""
    print("🔑 Gemini API Key Setup")
    print("=" * 30)
    
    # Check if already set
    existing_key = os.getenv('GEMINI_API_KEY')
    if existing_key:
        print(f"✅ API key already set: {existing_key[:10]}...")
        return existing_key
    
    print("📋 To get your free Gemini API key:")
    print("1. Go to: https://aistudio.google.com/")
    print("2. Sign in with Google account")
    print("3. Click 'Get API key' or 'Create API key'")
    print("4. Copy the key (starts with 'AIza...')")
    print()
    
    api_key = input("🔑 Enter your Gemini API key: ").strip()
    
    if api_key:
        # Save to environment for this session
        os.environ['GEMINI_API_KEY'] = api_key
        print(f"✅ API key set for this session")
        
        # Offer to save permanently
        save_permanently = input("💾 Save API key permanently? (y/n): ").lower().strip()
        if save_permanently == 'y':
            try:
                # Create config file
                config = {"gemini_api_key": api_key}
                with open("gemini_config.json", "w") as f:
                    json.dump(config, f, indent=2)
                print("✅ API key saved to gemini_config.json")
            except Exception as e:
                print(f"⚠️ Could not save config: {e}")
        
        return api_key
    else:
        print("❌ No API key provided")
        return None

def main():
    """Main test function"""
    print("🤖 Google Gemini API Test")
    print("=" * 30)
    print("Testing Gemini API for component analysis")
    print()
    
    # Setup API key
    api_key = setup_api_key()
    if not api_key:
        print("❌ Cannot proceed without API key")
        return False
    
    # Create tester
    tester = GeminiTester(api_key)
    
    # Test connection
    if not tester.test_api_connection():
        print("❌ API connection failed")
        return False
    
    # Test component analysis
    test_components = [
        "arduino uno",
        "10k resistor",
        "LM358 op amp"
    ]
    
    print(f"\n🧪 Testing component analysis...")
    
    for component in test_components:
        analysis = tester.analyze_component(component)
        if analysis:
            print(f"✅ {component}: Analysis successful")
        else:
            print(f"❌ {component}: Analysis failed")
        
        # Small delay between requests
        time.sleep(2)
    
    # Test rate limits
    tester.test_rate_limits()
    
    print(f"\n🎉 Gemini API testing completed!")
    print(f"✅ Ready to integrate with component search application")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        
        if success:
            print(f"\n🚀 NEXT STEPS:")
            print(f"✅ Gemini API is working")
            print(f"📝 Ready to implement in main application")
            print(f"🤖 Component analysis will be fast and reliable")
            print(f"💰 Free tier: 1,500 requests/day (plenty for component search)")
        else:
            print(f"\n❌ Setup incomplete")
            print(f"🔧 Please check your API key and try again")
    
    except KeyboardInterrupt:
        print(f"\n⏹️ Test interrupted")
    
    input(f"\nPress Enter to exit...")
