# P:\KiCadProjectBrowserTool\run.py
from project_browser import app # Imports the app instance from project_browser/__init__.py
from project_browser import config # Imports config to use SERVER_HOST, SERVER_PORT etc.
# Removed os, datetime, sys, traceback, scan_projects_generator as run.py no longer does initial scan

if __name__ == '__main__':
    print(f"--- KiCad Project Browser Server v{config.SERVER_VERSION} (via run.py) ---")
    print(f"Root directory to scan: {config.ROOT_DIR_TO_SCAN}") # From config
    print(f"Project index file: {config.INDEX_FILE_PATH}") # From config
    print(f"Flask template folder: {app.template_folder}")
    print(f"Static folder: {app.static_folder}")
    print(f"Static URL path: {app.static_url_path}")
    print(f"Open http://{config.SERVER_HOST}:{config.SERVER_PORT} in your browser.")
    print(f"Note: Initial scan, if needed, will be triggered by the frontend UI or the first API call to /api/projects.")

    app.run(
        host=config.SERVER_HOST,
        port=config.SERVER_PORT,
        debug=config.DEBUG_MODE,
        use_reloader=config.USE_RELOADER 
    )