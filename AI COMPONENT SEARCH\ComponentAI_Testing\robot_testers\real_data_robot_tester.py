#!/usr/bin/env python3
"""
REAL DATA ROBOT TESTER - UNICODE-FREE VERSION
Tests ComponentAI with ACTUAL components, REAL AI calls, REAL database operations
SEPARATE from product code - points to running product via URL
"""

import time
import random
import requests
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class RealDataRobotTester:
    """Robot that tests with REAL data and REAL workflows - UNICODE-FREE VERSION"""
    
    def __init__(self, product_url="http://localhost:8080"):
        self.product_url = product_url  # Points to running product
        self.driver = None
        self.wait = None
        self.test_components = [
            "LM324",      # Op-amp
            "Arduino Uno R3",
            "ESP32-WROOM-32"
        ]
        
    def setup_browser(self):
        """Setup browser for real testing"""
        print("Setting up REAL DATA Robot Tester (UNICODE-FREE VERSION)...")
        print(f"   Testing product at: {self.product_url}")
        print("   Will make REAL AI/LLM calls")
        print("   Will verify DATABASE storage")
        
        options = Options()
        options.add_argument("--start-maximized")
        options.add_argument("--disable-web-security")
        
        try:
            self.driver = webdriver.Chrome(options=options)
            self.wait = WebDriverWait(self.driver, 15)
            print("SUCCESS: Real data robot browser ready!")
            return True
        except Exception as e:
            print(f"ERROR: Browser setup failed: {e}")
            return False

    def inject_real_data_overlay(self):
        """Inject overlay for real data testing - UNICODE-FREE"""
        overlay_html = """
        <div id="real-data-overlay" style="
            position: fixed; top: 10px; right: 10px; width: 400px;
            background: rgba(0,0,0,0.95); color: white; padding: 15px;
            border-radius: 10px; font-family: monospace; font-size: 11px;
            z-index: 10000; border: 2px solid #ff6600; max-height: 80vh; overflow-y: auto;
        ">
            <h3 style="color: #ff6600; margin: 0 0 10px 0;">REAL DATA ROBOT (TESTING)</h3>
            <div id="current-workflow" style="color: #ffff00; margin-bottom: 10px;">Testing real data workflows...</div>
            <div id="workflow-steps" style="font-size: 10px; color: #ccc;">
                <div>Workflow Steps:</div>
                <div id="step-1" style="margin-left: 10px;">1. [WAIT] Select real component</div>
                <div id="step-2" style="margin-left: 10px;">2. [WAIT] Make AI/LLM call</div>
                <div id="step-3" style="margin-left: 10px;">3. [WAIT] Get real data back</div>
                <div id="step-4" style="margin-left: 10px;">4. [WAIT] Fill complete form</div>
                <div id="step-5" style="margin-left: 10px;">5. [WAIT] Save to database</div>
                <div id="step-6" style="margin-left: 10px;">6. [WAIT] Verify in database</div>
            </div>
            <div id="ai-response" style="margin-top: 10px; padding: 5px; background: rgba(0,100,0,0.3); font-size: 9px; max-height: 200px; overflow-y: auto;">
                <div style="color: #00ff00;">AI Response:</div>
                <div id="ai-content">Waiting for AI call...</div>
            </div>
        </div>
        """

        try:
            self.driver.execute_script(f"""
                if (!document.getElementById('real-data-overlay')) {{
                    document.body.insertAdjacentHTML('beforeend', `{overlay_html}`);
                }}
            """)
        except:
            pass

    def update_overlay(self, workflow="", step_num=0, step_status="", ai_response=""):
        """Update real data overlay - UNICODE-FREE"""
        try:
            if workflow:
                self.driver.execute_script(f"""
                    var el = document.getElementById('current-workflow');
                    if (el) el.textContent = '{workflow}';
                """)

            if step_num > 0:
                status_text = "[DONE]" if step_status == "done" else "[RUN]" if step_status == "running" else "[FAIL]" if step_status == "failed" else "[WAIT]"
                self.driver.execute_script(f"""
                    var el = document.getElementById('step-{step_num}');
                    if (el) {{
                        var text = el.textContent;
                        var newText = text.replace(/\\[WAIT\\]|\\[RUN\\]|\\[DONE\\]|\\[FAIL\\]/, '{status_text}');
                        el.textContent = newText;
                        el.style.color = '{step_status}' === 'done' ? '#00ff00' : '{step_status}' === 'running' ? '#ffff00' : '{step_status}' === 'failed' ? '#ff0000' : '#ccc';
                    }}
                """)

            if ai_response:
                # Escape the response for JavaScript
                escaped_response = ai_response.replace("'", "\\'").replace('"', '\\"').replace('\n', '\\n')[:500]
                self.driver.execute_script(f"""
                    var el = document.getElementById('ai-content');
                    if (el) el.textContent = '{escaped_response}...';
                """)

            time.sleep(0.5)
        except Exception as e:
            print(f"Overlay update failed: {e}")

    def test_real_ai_research_workflow(self, component_name):
        """Test complete AI research workflow with REAL data"""
        print(f"\nTesting REAL AI Research Workflow for: {component_name}")
        
        try:
            # Navigate to AI Research Lab
            self.driver.get(f"{self.product_url}/ai-research")
            time.sleep(2)
            self.inject_real_data_overlay()

            self.update_overlay(f"Testing AI Research: {component_name}", 1, "running")

            # Step 1: Select real component
            component_input = self.wait.until(EC.presence_of_element_located((By.ID, "componentName")))
            component_input.clear()

            # Type component name like a human
            for char in component_name:
                component_input.send_keys(char)
                time.sleep(0.1)  # Human-like typing

            self.update_overlay(f"Entered component: {component_name}", 1, "done")
            print(f"   Entered component: {component_name}")
            time.sleep(1)
            
            # Step 2: Make REAL AI/LLM call
            self.update_overlay(f"Making REAL AI call for {component_name}", 2, "running")
            print(f"   Making REAL AI call for {component_name}")

            research_btn = self.driver.find_element(By.ID, "researchBtn")

            # Highlight button
            self.driver.execute_script("arguments[0].style.border='3px solid orange'", research_btn)
            time.sleep(1)

            # Click to start REAL AI research
            research_btn.click()

            self.update_overlay(f"AI call initiated...", 2, "running")
            print(f"   AI call initiated...")
            
            # Step 3: Wait for REAL AI response
            try:
                # Wait for research status to appear
                research_status = self.wait.until(EC.visibility_of_element_located((By.ID, "researchStatus")))
                self.update_overlay(f"AI processing {component_name}...", 3, "running")
                print(f"   AI processing {component_name}...")

                # Wait for research to complete (real AI takes time)
                time.sleep(8)  # Give AI time to respond

                # Check for results
                results_div = self.driver.find_element(By.ID, "researchResults")
                results_text = results_div.text

                if len(results_text) > 100:  # Real AI response should be substantial
                    self.update_overlay(f"Got REAL AI response!", 3, "done", results_text)
                    print(f"   SUCCESS: AI Response received: {len(results_text)} characters")
                    
                    # Step 4: Extract data and fill form
                    if "Research Results" in results_text or len(results_text) > 200:
                        self.update_overlay(f"Processing AI data for form...", 4, "running")
                        print(f"   Processing AI data for form...")

                        # Navigate to Add Component to fill form with AI data
                        self.driver.get(f"{self.product_url}/components/add")
                        time.sleep(2)
                        self.inject_real_data_overlay()

                        # Fill form with AI-derived data
                        success = self.fill_form_with_ai_data(component_name, results_text)

                        if success:
                            self.update_overlay(f"Form filled with AI data", 4, "done")
                            print(f"   Form filled with AI data")

                            # Step 5: Save to database
                            self.update_overlay(f"Saving to database...", 5, "running")
                            print(f"   Saving to database...")

                            submit_btn = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
                            self.driver.execute_script("arguments[0].style.border='3px solid green'", submit_btn)
                            time.sleep(1)

                            submit_btn.click()
                            time.sleep(3)

                            # Step 6: Verify in database
                            if "/components" in self.driver.current_url and "/add" not in self.driver.current_url:
                                self.update_overlay(f"Verifying in database...", 6, "running")
                                print(f"   Verifying in database...")

                                # Check if component appears in list
                                time.sleep(2)
                                page_source = self.driver.page_source

                                if component_name in page_source:
                                    self.update_overlay(f"COMPLETE WORKFLOW SUCCESS!", 6, "done")
                                    print(f"   SUCCESS: COMPLETE WORKFLOW SUCCESS!")
                                    print(f"   SUCCESS: {component_name} in database!")
                                    return True
                                else:
                                    print(f"   ERROR: Not found in database")
                                    return False
                            else:
                                print(f"   ERROR: Form submission failed")
                                return False
                        else:
                            print(f"   ERROR: Form filling failed")
                            return False
                    else:
                        print(f"   ERROR: No valid AI results")
                        return False
                else:
                    print(f"   ERROR: AI response too short: {len(results_text)} chars")
                    return False
                    
            except Exception as wait_error:
                print(f"   ERROR: AI call timeout: {wait_error}")
                return False
                
        except Exception as e:
            print(f"   ERROR: Real AI workflow failed: {e}")
            return False
    
    def fill_form_with_ai_data(self, component_name, ai_response):
        """Fill form with data derived from AI response"""
        try:
            # Fill component name
            name_field = self.driver.find_element(By.ID, "name")
            name_field.clear()
            
            # Type component name like human
            for char in component_name:
                name_field.send_keys(char)
                time.sleep(0.05)
            
            # Determine component type from AI response or component name
            component_type = self.determine_component_type(component_name, ai_response)
            
            type_field = self.driver.find_element(By.ID, "type")
            type_field.clear()
            
            for char in component_type:
                type_field.send_keys(char)
                time.sleep(0.05)
            
            # Fill description with AI-derived info
            desc_field = self.driver.find_element(By.ID, "description")
            desc_field.clear()
            
            description = f"Component researched via AI: {component_name}. AI Analysis: {ai_response[:200]}... [Added by Robot Tester at {time.strftime('%Y-%m-%d %H:%M:%S')}]"
            
            for char in description:
                desc_field.send_keys(char)
                time.sleep(0.02)
            
            print(f"   SUCCESS: Form filled with AI-derived data")
            return True
            
        except Exception as e:
            print(f"   ERROR: Form filling failed: {e}")
            return False
    
    def determine_component_type(self, component_name, ai_response):
        """Determine component type from name and AI response"""
        name_lower = component_name.lower()
        
        if any(term in name_lower for term in ['lm324', 'op-amp', 'amplifier']):
            return "Operational Amplifier"
        elif any(term in name_lower for term in ['arduino', 'uno']):
            return "Development Board"
        elif any(term in name_lower for term in ['esp32', 'esp8266']):
            return "WiFi Microcontroller"
        else:
            return "Electronic Component"
    
    def verify_database_entry(self, component_name):
        """Verify component was actually saved to database"""
        try:
            # Check via API
            response = requests.get(f"{self.product_url}/api/components")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    components = data.get('components', [])
                    for comp in components:
                        if component_name in comp.get('name', ''):
                            print(f"   SUCCESS: Database verification: {component_name} found in API")
                            return True
            
            print(f"   ERROR: Database verification: {component_name} NOT found in API")
            return False
            
        except Exception as e:
            print(f"   ERROR: Database verification failed: {e}")
            return False
    
    def discover_all_pages(self):
        """Discover all pages in the application"""
        print("Discovering all pages...")

        pages = [
            ('/', 'Dashboard'),
            ('/components', 'Components List'),
            ('/components/add', 'Add Component'),
            ('/ai-research', 'AI Research Lab'),
            ('/analytics', 'Analytics'),
            ('/system-info', 'System Info')
        ]

        # Discover component detail pages
        try:
            response = requests.get(f"{self.product_url}/api/components")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    components = data.get('components', [])
                    for comp in components[:5]:  # Test first 5 components
                        comp_id = comp.get('id')
                        if comp_id:
                            pages.append((f'/components/{comp_id}', f'Component {comp_id} Detail'))
                            pages.append((f'/components/{comp_id}/edit', f'Edit Component {comp_id}'))
        except:
            pass

        return pages

    def discover_page_elements(self, page_url, page_name):
        """Discover all interactive elements on a page"""
        self.driver.get(page_url)
        time.sleep(2)
        self.inject_real_data_overlay()

        elements = {
            'links': [],
            'buttons': [],
            'inputs': [],
            'navigation': []
        }

        try:
            # Find all links
            links = self.driver.find_elements(By.TAG_NAME, "a")
            for link in links:
                try:
                    text = link.text.strip()
                    href = link.get_attribute('href')
                    if text and href:
                        elements['links'].append({
                            'text': text,
                            'href': href,
                            'element': link
                        })
                except:
                    pass

            # Find all buttons
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            for button in buttons:
                try:
                    text = button.text.strip() or button.get_attribute('title') or 'Button'
                    button_type = button.get_attribute('type') or 'button'
                    elements['buttons'].append({
                        'text': text,
                        'type': button_type,
                        'element': button
                    })
                except:
                    pass

            # Find all inputs
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            for input_el in inputs:
                try:
                    input_type = input_el.get_attribute('type') or 'text'
                    input_name = input_el.get_attribute('name') or input_el.get_attribute('id') or 'input'
                    elements['inputs'].append({
                        'name': input_name,
                        'type': input_type,
                        'element': input_el
                    })
                except:
                    pass

            # Find navigation elements
            nav_elements = self.driver.find_elements(By.CSS_SELECTOR, ".navbar-nav .nav-link, .nav-item a")
            for nav in nav_elements:
                try:
                    text = nav.text.strip()
                    href = nav.get_attribute('href')
                    if text and href:
                        elements['navigation'].append({
                            'text': text,
                            'href': href,
                            'element': nav
                        })
                except:
                    pass

        except Exception as e:
            print(f"Error discovering elements on {page_name}: {e}")

        return elements

    def test_page_comprehensively(self, page_url, page_name):
        """Test all elements on a page comprehensively"""
        print(f"\nTesting page comprehensively: {page_name}")

        try:
            elements = self.discover_page_elements(page_url, page_name)

            total_elements = sum(len(items) for items in elements.values())
            tested_elements = 0
            passed_elements = 0
            failed_elements = 0

            self.update_overlay(f"Testing {page_name}: {total_elements} elements found", 0, "running")

            # Test each category of elements
            for category, items in elements.items():
                if not items:
                    continue

                print(f"   Testing {len(items)} {category}")

                for i, item in enumerate(items):
                    tested_elements += 1
                    element_text = item.get('text', item.get('name', item.get('id', 'Unknown')))

                    self.update_overlay(f"Testing {category}: {element_text}", 0, "running")

                    try:
                        result = self.test_element(item, category, page_name)
                        if result:
                            passed_elements += 1
                            print(f"      SUCCESS: {category}: {element_text}")
                        else:
                            failed_elements += 1
                            print(f"      FAILED: {category}: {element_text}")
                    except Exception as e:
                        failed_elements += 1
                        print(f"      ERROR: {category}: {element_text} - {str(e)}")

                    time.sleep(0.5)  # Let you see each test

            # Page results
            success_rate = (passed_elements / total_elements * 100) if total_elements > 0 else 0

            self.update_overlay(f"{page_name} complete: {success_rate:.1f}% success", 0, "done")

            print(f"   Page Results: {passed_elements}/{total_elements} passed ({success_rate:.1f}%)")

            return {
                'page': page_name,
                'total': total_elements,
                'passed': passed_elements,
                'failed': failed_elements,
                'success_rate': success_rate
            }

        except Exception as e:
            print(f"   ERROR: Page testing failed: {e}")
            return {'page': page_name, 'total': 0, 'passed': 0, 'failed': 1, 'success_rate': 0}

    def test_element(self, item, category, page_name):
        """Test individual element"""
        try:
            element = item['element']

            # Highlight element
            self.driver.execute_script("arguments[0].style.border='3px solid red'", element)
            time.sleep(0.3)

            if category == 'links':
                return self.test_link(item, element)
            elif category == 'buttons':
                return self.test_button(item, element, page_name)
            elif category == 'inputs':
                return self.test_input(item, element)
            elif category == 'navigation':
                return self.test_navigation(item, element)

            return True

        except Exception as e:
            print(f"Element test failed: {str(e)}")
            return False

    def test_link(self, item, element):
        """Test link with real navigation"""
        try:
            href = item['href']
            if href.startswith('http') and self.product_url not in href:
                # External link - verify it's valid
                return True
            else:
                # Internal link - click and verify navigation
                original_url = self.driver.current_url
                element.click()
                time.sleep(2)

                # Check if URL changed or page loaded
                new_url = self.driver.current_url
                if new_url != original_url or "404" not in self.driver.page_source:
                    # Go back
                    self.driver.back()
                    time.sleep(1)
                    self.inject_real_data_overlay()  # Re-inject overlay
                    return True
                else:
                    return False
        except:
            return False

    def test_button(self, item, element, page_name):
        """Test button with real actions"""
        try:
            button_text = item['text'].lower()
            button_type = item['type']

            if 'research' in button_text and 'ai' in button_text:
                return self.test_ai_research_button_simple(element)
            elif button_type == 'submit':
                return self.test_submit_button(element, page_name)
            elif 'delete' in button_text:
                return True  # Don't actually delete
            elif 'edit' in button_text:
                return self.test_edit_button(element)
            else:
                # Generic button test
                element.click()
                time.sleep(1)
                return True
        except:
            return False

    def test_ai_research_button_simple(self, element):
        """Simple test of AI research button"""
        try:
            # Fill component input first
            component_input = self.driver.find_element(By.ID, "componentName")
            component_input.clear()
            component_input.send_keys("Test Component")

            # Click research button
            element.click()
            time.sleep(2)

            # Check if research started
            try:
                research_status = self.driver.find_element(By.ID, "researchStatus")
                return research_status.is_displayed()
            except:
                return False
        except:
            return False

    def test_submit_button(self, element, page_name):
        """Test form submission"""
        try:
            if 'add' in page_name.lower() and 'component' in page_name.lower():
                # Don't submit empty forms
                return True
            else:
                # Generic form test - don't submit
                return True
        except:
            return False

    def test_input(self, item, element):
        """Test input with appropriate data"""
        try:
            input_type = item['type']
            input_name = item['name'].lower()

            if input_type in ['text', 'search']:
                element.clear()
                element.send_keys("Test Data")
                time.sleep(0.3)
                element.clear()
            elif input_type == 'email':
                element.clear()
                element.send_keys("<EMAIL>")
                time.sleep(0.3)
                element.clear()
            elif input_type == 'checkbox':
                element.click()
                time.sleep(0.2)
                element.click()

            return True
        except:
            return False

    def test_navigation(self, item, element):
        """Test navigation"""
        try:
            original_url = self.driver.current_url
            element.click()
            time.sleep(2)

            # Verify navigation worked
            new_url = self.driver.current_url
            if new_url != original_url:
                # Go back
                self.driver.back()
                time.sleep(1)
                self.inject_real_data_overlay()  # Re-inject overlay
                return True
            else:
                return False
        except:
            return False

    def test_edit_button(self, element):
        """Test edit button navigation"""
        try:
            original_url = self.driver.current_url
            element.click()
            time.sleep(2)

            # Check if navigated to edit page
            new_url = self.driver.current_url
            if 'edit' in new_url or new_url != original_url:
                self.driver.back()
                time.sleep(1)
                self.inject_real_data_overlay()  # Re-inject overlay
                return True
            else:
                return False
        except:
            return False

    def run_real_data_tests(self):
        """Run comprehensive real data tests + comprehensive UI tests"""
        print("STARTING COMPREHENSIVE REAL DATA ROBOT TESTING")
        print("=" * 60)
        print(f"Testing product at: {self.product_url}")
        print("PHASE 1: Testing EVERY page and EVERY button!")
        print("PHASE 2: Testing REAL AI workflows!")
        print("PHASE 3: Verifying REAL database operations!")
        print("=" * 60)

        if not self.setup_browser():
            return False

        all_results = []

        try:
            # PHASE 1: Comprehensive UI Testing
            print("\nPHASE 1: COMPREHENSIVE UI TESTING")
            print("=" * 40)

            all_pages = self.discover_all_pages()
            print(f"Found {len(all_pages)} pages to test comprehensively")

            ui_results = []
            for page_url, page_name in all_pages:
                full_url = f"{self.product_url}{page_url}"
                result = self.test_page_comprehensively(full_url, page_name)
                ui_results.append(result)
                time.sleep(2)  # Pause between pages

            # PHASE 2: Real Data AI Testing
            print("\nPHASE 2: REAL DATA AI TESTING")
            print("=" * 40)

            ai_results = []
            for i, component in enumerate(self.test_components):
                print(f"\nReal Data Test {i+1}/{len(self.test_components)}: {component}")

                result = self.test_real_ai_research_workflow(component)
                ai_results.append({
                    'component': component,
                    'success': result
                })

                if result:
                    print(f"   SUCCESS: REAL DATA SUCCESS: {component}")
                    # Verify in database
                    db_verified = self.verify_database_entry(component)
                    ai_results[-1]['db_verified'] = db_verified
                else:
                    print(f"   ERROR: REAL DATA FAILED: {component}")
                    ai_results[-1]['db_verified'] = False

                time.sleep(3)  # Pause between tests
            
            # PHASE 3: Final Results
            print("\nPHASE 3: COMPREHENSIVE RESULTS")
            print("=" * 40)

            # UI Testing Results
            total_ui_elements = sum(r['total'] for r in ui_results)
            passed_ui_elements = sum(r['passed'] for r in ui_results)
            ui_success_rate = (passed_ui_elements / total_ui_elements * 100) if total_ui_elements > 0 else 0

            print(f"\nUI TESTING RESULTS:")
            print(f"Pages Tested: {len(ui_results)}")
            print(f"Total UI Elements: {total_ui_elements}")
            print(f"Passed Elements: {passed_ui_elements}")
            print(f"UI Success Rate: {ui_success_rate:.1f}%")

            print(f"\nPage-by-Page Results:")
            for result in ui_results:
                print(f"   {result['page']}: {result['passed']}/{result['total']} ({result['success_rate']:.1f}%)")

            # AI Testing Results
            successful_ai_tests = sum(1 for r in ai_results if r['success'])
            db_verified_tests = sum(1 for r in ai_results if r.get('db_verified', False))
            ai_success_rate = (successful_ai_tests / len(ai_results) * 100) if ai_results else 0

            print(f"\nAI WORKFLOW TESTING RESULTS:")
            print(f"Components Tested: {len(ai_results)}")
            print(f"Successful Workflows: {successful_ai_tests}")
            print(f"Database Verified: {db_verified_tests}")
            print(f"AI Success Rate: {ai_success_rate:.1f}%")

            print(f"\nAI Workflow Results:")
            for result in ai_results:
                status = "SUCCESS" if result['success'] else "FAILED"
                db_status = "DB-OK" if result.get('db_verified', False) else "DB-FAIL"
                print(f"   {result['component']}: {status} {db_status}")

            # Overall Results
            overall_success = ui_success_rate >= 80 and ai_success_rate >= 80

            print(f"\nOVERALL COMPREHENSIVE TESTING RESULTS:")
            print(f"UI Testing: {ui_success_rate:.1f}% ({passed_ui_elements}/{total_ui_elements})")
            print(f"AI Testing: {ai_success_rate:.1f}% ({successful_ai_tests}/{len(ai_results)})")
            print(f"Overall Status: {'SUCCESS' if overall_success else 'NEEDS ATTENTION'}")

            if overall_success:
                print("ALL COMPREHENSIVE TESTS PASSED!")
            elif ui_success_rate >= 80 or ai_success_rate >= 80:
                print("SOME COMPREHENSIVE TESTS PASSED!")
            else:
                print("COMPREHENSIVE TESTS NEED ATTENTION!")

            print("\nBrowser will stay open for 30 seconds for review...")
            time.sleep(30)

            return overall_success
            
        except Exception as e:
            print(f"ERROR: Real data testing failed: {e}")
            return False
        
        finally:
            if self.driver:
                print("Closing real data robot browser...")
                self.driver.quit()

def main():
    """Main real data testing function"""
    print("ComponentAI REAL DATA Robot Tester (UNICODE-FREE VERSION)")
    print("=" * 60)
    print("Tests with ACTUAL components like LM324")
    print("Makes REAL AI/LLM calls and verifies database!")
    print("SEPARATE from product code - points to running product")
    print("=" * 60)
    
    # Check if product server is running
    product_url = "http://localhost:8080"
    try:
        response = requests.get(product_url, timeout=5)
        print(f"SUCCESS: ComponentAI product server is running at {product_url}")
    except:
        print(f"ERROR: ComponentAI product server is NOT running at {product_url}")
        print("Please start the product server first:")
        print("  cd ComponentAI")
        print("  python launch_admin.py")
        return False
    
    robot = RealDataRobotTester(product_url)
    success = robot.run_real_data_tests()
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
