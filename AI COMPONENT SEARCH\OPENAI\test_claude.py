#!/usr/bin/env python3
"""
Test script to verify Claude integration with DeerFlow
"""

import os
from langchain_anthropic import ChatAnthropic

def test_claude_direct():
    """Test Claude directly using langchain-anthropic"""
    print("Testing Claude directly...")
    
    # Set up <PERSON> client
    claude = ChatAnthropic(
        model="claude-3-5-sonnet-20241022",
        api_key="************************************************************************************************************"
    )
    
    try:
        response = claude.invoke("Hello! Please respond with '<PERSON> is working!' to confirm the connection.")
        print(f"✅ Claude Response: {response.content}")
        return True
    except Exception as e:
        print(f"❌ Claude Error: {e}")
        return False

def test_deer_flow_llm():
    """Test DeerFlow's LLM loading with <PERSON>"""
    print("\nTesting DeerFlow LLM loading...")
    
    try:
        # Set environment variable for Anthropic
        os.environ["ANTHROPIC_API_KEY"] = "************************************************************************************************************"
        
        from src.llms.llm import get_llm_by_type
        
        llm = get_llm_by_type("basic")
        response = llm.invoke("Hello! Please respond with 'DeerFlow LLM is working!' to confirm.")
        print(f"✅ DeerFlow LLM Response: {response.content}")
        return True
    except Exception as e:
        print(f"❌ DeerFlow LLM Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Claude Integration with DeerFlow\n")
    
    # Test 1: Direct Claude
    claude_works = test_claude_direct()
    
    # Test 2: DeerFlow LLM
    deer_flow_works = test_deer_flow_llm()
    
    print(f"\n📊 Test Results:")
    print(f"   Claude Direct: {'✅ PASS' if claude_works else '❌ FAIL'}")
    print(f"   DeerFlow LLM:  {'✅ PASS' if deer_flow_works else '❌ FAIL'}")
    
    if claude_works and deer_flow_works:
        print(f"\n🎉 All tests passed! Claude is ready to use with DeerFlow!")
    elif claude_works:
        print(f"\n⚠️  Claude works directly but DeerFlow integration needs fixing.")
    else:
        print(f"\n❌ Claude connection failed. Check your API key and network.")
