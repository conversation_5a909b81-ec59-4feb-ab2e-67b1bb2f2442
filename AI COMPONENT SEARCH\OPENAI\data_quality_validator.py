#!/usr/bin/env python3
"""
Data Quality Validator for Component Search Results
Ensures sourcing-grade data quality for professional procurement decisions
"""

import re
from typing import List, Dict, Any

class SourcingDataValidator:
    """Validates component search results for professional sourcing quality"""
    
    def __init__(self):
        self.quality_issues = []
        self.validation_rules = {
            'component_name': self.validate_component_name,
            'price': self.validate_price,
            'stock': self.validate_stock_status,
            'shipping': self.validate_shipping_cost,
            'specifications': self.validate_specifications,
            'supplier': self.validate_supplier_info
        }
    
    def validate_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Validate search results and return quality report"""
        self.quality_issues = []
        validated_results = []
        
        for result in results:
            validation_score, issues = self.validate_single_result(result)
            
            if validation_score >= 60:  # Minimum quality threshold
                validated_results.append({
                    **result,
                    'quality_score': validation_score,
                    'quality_issues': issues
                })
            else:
                self.quality_issues.extend(issues)
        
        return {
            'validated_results': validated_results,
            'quality_report': self.generate_quality_report(results, validated_results),
            'rejected_count': len(results) - len(validated_results)
        }
    
    def validate_single_result(self, result: Dict[str, Any]) -> tuple:
        """Validate a single search result"""
        score = 100
        issues = []
        
        for field, validator in self.validation_rules.items():
            field_score, field_issues = validator(result.get(field, ''), result)
            score = min(score, field_score)
            issues.extend(field_issues)
        
        return score, issues
    
    def validate_component_name(self, name: str, result: Dict) -> tuple:
        """Validate component name quality"""
        issues = []
        score = 100
        
        if not name or len(name) < 5:
            issues.append("Component name too short or missing")
            score = 0
        elif any(x in name.lower() for x in ['{{', 'template', 'error', 'product found']):
            issues.append("Component name contains template errors")
            score = 0
        elif name.lower() in ['check website', 'contact supplier', 'product found']:
            issues.append("Generic placeholder component name")
            score = 20
        elif len(name) > 100:
            issues.append("Component name too long")
            score = 70
        
        return score, issues
    
    def validate_price(self, price: Any, result: Dict) -> tuple:
        """Validate price information"""
        issues = []
        score = 100
        
        try:
            price_val = float(price) if price else 0
            
            if price_val <= 0:
                issues.append("Price missing or zero")
                score = 0
            elif price_val < 1:
                issues.append("Price suspiciously low")
                score = 30
            elif price_val > 100000:
                issues.append("Price suspiciously high")
                score = 50
            
        except (ValueError, TypeError):
            issues.append("Invalid price format")
            score = 0
        
        return score, issues
    
    def validate_stock_status(self, stock: str, result: Dict) -> tuple:
        """Validate stock status information"""
        issues = []
        score = 100
        
        if not stock:
            issues.append("Stock status missing")
            score = 30
        elif stock.lower() in ['check website', 'contact supplier']:
            issues.append("Non-actionable stock status")
            score = 40
        elif stock.lower() == 'unknown':
            issues.append("Unknown stock status")
            score = 20
        elif any(x in stock.lower() for x in ['in stock', 'available', 'limited']):
            score = 100  # Good stock info
        elif 'out of stock' in stock.lower():
            score = 80   # Clear but negative info
        
        return score, issues
    
    def validate_shipping_cost(self, shipping: Any, result: Dict) -> tuple:
        """Validate shipping cost information"""
        issues = []
        score = 100
        
        try:
            shipping_val = float(shipping) if shipping else 0
            supplier = result.get('supplier', '')
            
            # Check for suspicious uniform shipping costs
            if shipping_val == 50 and supplier not in ['Probots', 'Robokits']:
                issues.append("Suspicious uniform shipping cost (₹50)")
                score = 60
            elif shipping_val < 0:
                issues.append("Invalid negative shipping cost")
                score = 0
            elif shipping_val > 500:
                issues.append("Shipping cost too high")
                score = 40
            
        except (ValueError, TypeError):
            issues.append("Invalid shipping cost format")
            score = 0
        
        return score, issues
    
    def validate_specifications(self, specs: str, result: Dict) -> tuple:
        """Validate component specifications"""
        issues = []
        score = 100
        
        if not specs:
            issues.append("No specifications provided")
            score = 60  # Not critical but reduces usefulness
        elif len(specs) < 3:
            issues.append("Minimal specification information")
            score = 70
        
        return score, issues
    
    def validate_supplier_info(self, supplier: str, result: Dict) -> tuple:
        """Validate supplier information"""
        issues = []
        score = 100
        
        if not supplier:
            issues.append("Supplier name missing")
            score = 0
        
        location = result.get('location', '')
        if not location:
            issues.append("Supplier location missing")
            score = 80
        
        url = result.get('url', '')
        if not url or url == 'Click to open':
            issues.append("Supplier URL missing or placeholder")
            score = 70
        
        return score, issues
    
    def generate_quality_report(self, original_results: List, validated_results: List) -> Dict:
        """Generate comprehensive quality report"""
        total_original = len(original_results)
        total_validated = len(validated_results)
        
        if total_validated == 0:
            avg_quality = 0
        else:
            avg_quality = sum(r.get('quality_score', 0) for r in validated_results) / total_validated
        
        # Count common issues
        issue_counts = {}
        for result in validated_results:
            for issue in result.get('quality_issues', []):
                issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        return {
            'total_results': total_original,
            'validated_results': total_validated,
            'rejection_rate': ((total_original - total_validated) / total_original * 100) if total_original > 0 else 0,
            'average_quality_score': avg_quality,
            'common_issues': sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:5],
            'quality_grade': self.get_quality_grade(avg_quality),
            'sourcing_readiness': self.assess_sourcing_readiness(validated_results)
        }
    
    def get_quality_grade(self, score: float) -> str:
        """Get quality grade based on score"""
        if score >= 90:
            return "A+ (Excellent for sourcing)"
        elif score >= 80:
            return "A (Good for sourcing)"
        elif score >= 70:
            return "B (Acceptable with verification)"
        elif score >= 60:
            return "C (Requires manual verification)"
        else:
            return "F (Not suitable for sourcing)"
    
    def assess_sourcing_readiness(self, results: List) -> Dict:
        """Assess readiness for professional sourcing decisions"""
        if not results:
            return {
                'ready': False,
                'reason': 'No validated results available',
                'recommendations': ['Expand search terms', 'Try different suppliers', 'Check component availability']
            }
        
        high_quality_results = [r for r in results if r.get('quality_score', 0) >= 80]
        actionable_stock = [r for r in results if 'in stock' in r.get('stock', '').lower() or 'available' in r.get('stock', '').lower()]
        
        recommendations = []
        
        if len(high_quality_results) < 3:
            recommendations.append("Get quotes from more suppliers for better comparison")
        
        if len(actionable_stock) == 0:
            recommendations.append("Contact suppliers directly for stock confirmation")
        
        if not any('specifications' in r and r['specifications'] for r in results):
            recommendations.append("Request detailed specifications from suppliers")
        
        ready = len(high_quality_results) >= 2 and len(actionable_stock) >= 1
        
        return {
            'ready': ready,
            'high_quality_options': len(high_quality_results),
            'actionable_stock_options': len(actionable_stock),
            'recommendations': recommendations
        }

def validate_search_results(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Main function to validate search results"""
    validator = SourcingDataValidator()
    return validator.validate_results(results)

if __name__ == "__main__":
    # Test with sample data
    sample_results = [
        {
            'supplier': 'Evelta',
            'component': 'Arduino Uno R3 Original',
            'price': 650,
            'stock': 'In Stock (25 units)',
            'shipping': 55,
            'specifications': '5V, USB, ATmega328P',
            'location': 'Mumbai, Maharashtra',
            'url': 'https://evelta.com/arduino-uno-r3'
        },
        {
            'supplier': 'TestSupplier',
            'component': '{{titleCorp(item)}}',
            'price': 0,
            'stock': 'Check Website',
            'shipping': 50,
            'specifications': '',
            'location': '',
            'url': 'Click to open'
        }
    ]
    
    validation_result = validate_search_results(sample_results)
    
    print("Data Quality Validation Report:")
    print("=" * 50)
    print(f"Quality Grade: {validation_result['quality_report']['quality_grade']}")
    print(f"Validated Results: {validation_result['quality_report']['validated_results']}")
    print(f"Rejection Rate: {validation_result['quality_report']['rejection_rate']:.1f}%")
    print(f"Sourcing Ready: {validation_result['quality_report']['sourcing_readiness']['ready']}")
