# P:\KiCadProjectBrowserTool\project_browser\utils.py

def format_size(size_bytes):
    """Converts a size in bytes to a human-readable string (KB, MB, GB)."""
    if size_bytes == 0:
        return "0B"
    size_name = ("B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB")
    # Determine the appropriate unit, ensuring i is within bounds
    i = 0
    if size_bytes > 0 : # only proceed if size_bytes is positive
        # Calculate bit_length safely
        abs_size = abs(size_bytes)
        if abs_size > 0: # bit_length is undefined for 0
            i = int(min(len(size_name) - 1, abs_size.bit_length() // 10))

    p = 1024 ** i
    s = round(size_bytes / p, 1) # Show one decimal place
    return f"{s}{size_name[i]}"

def log_scan_error(os_error):
    """Callback function for os.walk to log errors accessing paths."""
    # Simple print for now, could be expanded to log to a file
    print(f"  [OS_WALK_ERROR] Could not access: {getattr(os_error, 'filename', 'N/A')} - {getattr(os_error, 'strerror', 'N/A')}. Skipping.")

if __name__ == '__main__':
    # Simple test cases if you run this file directly
    print("--- Testing utility functions ---")
    print(f"format_size(0): {format_size(0)}")
    print(f"format_size(1023): {format_size(1023)}")
    print(f"format_size(1024): {format_size(1024)}")
    print(f"format_size(1500000): {format_size(1500000)}") # Approx 1.4MB
    print(f"format_size(2500000000): {format_size(2500000000)}") # Approx 2.3GB

    # Test log_scan_error (mocking an OSError)
    class MockOSError:
        def __init__(self, filename, strerror):
            self.filename = filename
            self.strerror = strerror

    print("\nTesting log_scan_error:")
    mock_error = MockOSError("/path/to/locked_file", "Permission denied")
    log_scan_error(mock_error)
    mock_error_no_filename = MockOSError(None, "Some other error") # Test getattr safety
    log_scan_error(mock_error_no_filename)