{% extends "base.html" %}

{% block title %}Edit {{ component.name }} - ComponentAI Admin{% endblock %}
{% block page_title %}Edit {{ component.name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <form method="POST">
            <div class="mb-3">
                <label for="name" class="form-label">Component Name *</label>
                <input type="text" class="form-control" id="name" name="name" 
                       value="{{ component.name }}" required>
            </div>
            
            <div class="mb-3">
                <label for="type" class="form-label">Type *</label>
                <input type="text" class="form-control" id="type" name="type" 
                       value="{{ component.type }}" required>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea class="form-control" id="description" name="description" rows="3">{{ component.description }}</textarea>
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-save"></i> Save Changes
                </button>
                <a href="{{ url_for('view_component', component_id=component.id) }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Cancel
                </a>
            </div>
        </form>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-info-circle"></i> Component Info</h6>
            </div>
            <div class="card-body">
                <p><strong>ID:</strong> {{ component.id }}</p>
                <p><strong>Current Name:</strong> {{ component.name }}</p>
                <p><strong>Current Type:</strong> {{ component.type }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
