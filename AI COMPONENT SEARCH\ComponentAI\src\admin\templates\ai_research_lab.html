{% extends "base.html" %}

{% block title %}AI Research Lab - ComponentAI Admin{% endblock %}
{% block page_title %}AI Research Lab{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-search"></i> Component Research</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="componentName" class="form-label">Component Name</label>
                    <input type="text" class="form-control" id="componentName" 
                           placeholder="e.g., ESP32-S3, Arduino Nano 33 IoT">
                </div>
                
                <button class="btn btn-primary" onclick="startResearch()" id="researchBtn">
                    <i class="bi bi-robot"></i> Start AI Research
                </button>
                
                <div class="mt-3" id="researchStatus" style="display: none;">
                    <div class="alert alert-info">
                        <div class="d-flex align-items-center">
                            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                            <span>AI research in progress...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-gear"></i> Research Results</h6>
            </div>
            <div class="card-body">
                <div id="researchResults">
                    <p class="text-muted">Enter a component name and start research to see results here.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h6><i class="bi bi-info-circle"></i> How AI Research Works</h6>
            <ol>
                <li><strong>Query Analysis:</strong> AI analyzes the component name</li>
                <li><strong>Component Research:</strong> AI researches component details</li>
                <li><strong>Data Validation:</strong> System validates and structures the data</li>
                <li><strong>Preview & Review:</strong> You can review all data before accepting</li>
            </ol>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
async function startResearch() {
    const componentName = document.getElementById('componentName').value.trim();
    
    if (!componentName) {
        alert('Please enter a component name');
        return;
    }
    
    // Show loading
    document.getElementById('researchStatus').style.display = 'block';
    document.getElementById('researchBtn').disabled = true;
    
    try {
        const response = await fetch('/api/ai/research-detailed', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ component_name: componentName })
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayResults(data);
        } else {
            displayError(data.error || 'Research failed');
        }
        
    } catch (error) {
        displayError(`Network error: ${error.message}`);
    } finally {
        document.getElementById('researchStatus').style.display = 'none';
        document.getElementById('researchBtn').disabled = false;
    }
}

function displayResults(data) {
    const resultsDiv = document.getElementById('researchResults');
    
    let html = `
        <h6>Research Results for: ${data.component_name}</h6>
        <div class="mt-3">
            <strong>Steps Completed:</strong>
            <ul>
    `;
    
    data.steps.forEach(step => {
        html += `<li>${step.name}: ${step.status}</li>`;
    });
    
    html += `
            </ul>
        </div>
        
        <div class="mt-3">
            <strong>Component Data:</strong>
            <pre class="bg-light p-2">${JSON.stringify(data.final_component, null, 2)}</pre>
        </div>
        
        <div class="mt-3">
            <button class="btn btn-success" onclick="acceptComponent()">
                <i class="bi bi-check"></i> Accept & Add to Database
            </button>
        </div>
    `;
    
    resultsDiv.innerHTML = html;
}

function displayError(error) {
    const resultsDiv = document.getElementById('researchResults');
    resultsDiv.innerHTML = `
        <div class="alert alert-danger">
            <strong>Research Failed:</strong> ${error}
        </div>
    `;
}

function acceptComponent() {
    alert('Component would be added to database (mock functionality)');
}
</script>
{% endblock %}
