#!/usr/bin/env python3
"""
Fixed Text Only - Keep Full Workflow Visibility
Only fix text occlusion, keep all other good features
"""

import tkinter as tk
from tkinter import ttk

class FixedTextOnlyUI:
    """Fix ONLY text occlusion, keep full workflow visibility"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 Fixed Text Only - Full Workflow Visible")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#ffffff')
        self.root.state('zoomed')  # Maximize
        
        self.current_step = 1
        self.setup_ui()
    
    def setup_ui(self):
        """Setup UI with fixed text but full workflow visibility"""
        
        # Full workflow steps - ALL VISIBLE
        self.setup_full_workflow_fixed()
        
        # Main content
        self.main_content = tk.Frame(self.root, bg='#ffffff')
        self.main_content.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Show current step
        self.show_step_content()
    
    def setup_full_workflow_fixed(self):
        """Full workflow with FIXED text occlusion only"""
        
        # Workflow frame - taller to prevent occlusion
        workflow_frame = tk.Frame(self.root, bg='#f8f9fa', height=120)
        workflow_frame.pack(fill='x', padx=20, pady=10)
        workflow_frame.pack_propagate(False)
        
        # Title
        tk.Label(workflow_frame, text="📋 Component Search Workflow - All Steps Visible", 
                font=('Arial', 14, 'bold'), bg='#f8f9fa', fg='#495057').pack(pady=(10,5))
        
        # Steps container - FULL WIDTH
        steps_container = tk.Frame(workflow_frame, bg='#f8f9fa')
        steps_container.pack(expand=True, fill='x', pady=10)
        
        # ALL 5 steps visible with FIXED text
        steps = [
            ("1", "🔍 Search", "Enter component"),
            ("2", "🤖 AI Analysis", "Review insights"),
            ("3", "📊 Results", "Compare suppliers"),
            ("4", "📄 Details", "View datasheets"),
            ("5", "✅ Decision", "Select & export")
        ]
        
        self.step_widgets = []
        
        # Calculate EXACT spacing to prevent occlusion
        total_steps = len(steps)
        total_arrows = total_steps - 1
        
        # Use grid layout for PERFECT spacing
        for i, (num, title, desc) in enumerate(steps):
            
            # Step container - EXACT width calculation
            step_frame = tk.Frame(steps_container, bg='#f8f9fa')
            step_frame.grid(row=0, column=i*2, padx=15, pady=5, sticky='ew')
            
            # Configure grid weights for equal distribution
            steps_container.grid_columnconfigure(i*2, weight=1)
            
            # Step circle
            circle_bg = '#007bff' if i+1 == self.current_step else '#28a745' if i+1 < self.current_step else '#e9ecef'
            text_color = 'white' if i+1 <= self.current_step else '#6c757d'
            
            circle = tk.Label(step_frame, text=num, 
                             font=('Arial', 16, 'bold'),
                             bg=circle_bg, fg=text_color,
                             width=3, height=1, relief='solid', bd=0)
            circle.pack(pady=2)
            
            # Step title - FIXED width to prevent overlap
            title_color = '#007bff' if i+1 == self.current_step else '#28a745' if i+1 < self.current_step else '#6c757d'
            title_label = tk.Label(step_frame, text=title, 
                                  font=('Arial', 11, 'bold'),
                                  bg='#f8f9fa', fg=title_color,
                                  width=12)  # FIXED WIDTH
            title_label.pack()
            
            # Step description - FIXED width and wrapping
            desc_label = tk.Label(step_frame, text=desc, 
                                 font=('Arial', 9),
                                 bg='#f8f9fa', fg='#6c757d',
                                 width=12, wraplength=80)  # FIXED WIDTH + WRAP
            desc_label.pack()
            
            self.step_widgets.append((circle, title_label, desc_label))
            
            # Arrow between steps - GRID positioned
            if i < len(steps) - 1:
                arrow_frame = tk.Frame(steps_container, bg='#f8f9fa')
                arrow_frame.grid(row=0, column=i*2+1, padx=5, pady=20)
                
                tk.Label(arrow_frame, text="→", font=('Arial', 18), 
                        bg='#f8f9fa', fg='#dee2e6').pack()
    
    def update_workflow_step(self, step):
        """Update workflow step indicator"""
        self.current_step = step
        
        for i, (circle, title_label, desc_label) in enumerate(self.step_widgets):
            if i + 1 == step:
                # Current step - blue
                circle.config(bg='#007bff', fg='white')
                title_label.config(fg='#007bff')
                desc_label.config(fg='#495057')
            elif i + 1 < step:
                # Completed step - green
                circle.config(bg='#28a745', fg='white')
                title_label.config(fg='#28a745')
                desc_label.config(fg='#6c757d')
            else:
                # Future step - gray (but VISIBLE)
                circle.config(bg='#e9ecef', fg='#6c757d')
                title_label.config(fg='#6c757d')
                desc_label.config(fg='#6c757d')
    
    def show_step_content(self):
        """Show content for current step"""
        
        # Clear content
        for widget in self.main_content.winfo_children():
            widget.destroy()
        
        # Step header
        header_frame = tk.Frame(self.main_content, bg='#ffffff')
        header_frame.pack(fill='x', pady=20)
        
        step_names = ["🔍 Component Search", "🤖 AI Analysis", "📊 Search Results", "📄 Component Details", "✅ Final Decision"]
        current_name = step_names[self.current_step - 1]
        
        tk.Label(header_frame, text=current_name, 
                font=('Arial', 24, 'bold'), bg='#ffffff', fg='#007bff').pack(side='left')
        
        progress_text = f"Step {self.current_step} of 5"
        tk.Label(header_frame, text=progress_text, 
                font=('Arial', 14), bg='#ffffff', fg='#6c757d').pack(side='right')
        
        # Content based on step
        if self.current_step == 1:
            self.show_search_content()
        elif self.current_step == 2:
            self.show_analysis_content()
        elif self.current_step == 3:
            self.show_results_content()
        elif self.current_step == 4:
            self.show_details_content()
        elif self.current_step == 5:
            self.show_decision_content()
        
        # Navigation
        self.show_navigation()
    
    def show_search_content(self):
        """Step 1: Search - Large, prominent"""
        
        # Large search section
        search_container = tk.Frame(self.main_content, bg='#ffffff')
        search_container.pack(fill='both', expand=True)
        
        # Centered search area
        center_frame = tk.Frame(search_container, bg='#ffffff')
        center_frame.pack(expand=True)
        
        tk.Label(center_frame, text="What component are you looking for?", 
                font=('Arial', 20, 'bold'), bg='#ffffff', fg='#212529').pack(pady=30)
        
        # Large search input
        search_frame = tk.Frame(center_frame, bg='#ffffff')
        search_frame.pack(pady=20)
        
        tk.Label(search_frame, text="Component:", font=('Arial', 14, 'bold'), 
                bg='#ffffff').pack(anchor='w')
        
        self.search_var = tk.StringVar(value="10k resistor")
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, 
                               font=('Arial', 16), width=50, relief='solid', bd=2)
        search_entry.pack(pady=10, ipady=8)
        
        # Search suggestions
        suggestions_frame = tk.Frame(center_frame, bg='#ffffff')
        suggestions_frame.pack(pady=20)
        
        tk.Label(suggestions_frame, text="💡 Popular searches:", 
                font=('Arial', 12, 'bold'), bg='#ffffff').pack()
        
        suggestions = ["10k resistor", "Arduino Uno", "ESP32", "100uF capacitor"]
        
        for i, suggestion in enumerate(suggestions):
            if i % 4 == 0:
                row_frame = tk.Frame(suggestions_frame, bg='#ffffff')
                row_frame.pack(pady=5)
            
            btn = tk.Button(row_frame, text=suggestion, 
                           command=lambda s=suggestion: self.use_suggestion(s),
                           bg='#e9ecef', fg='#495057', font=('Arial', 10),
                           relief='solid', bd=1, padx=15, pady=5)
            btn.pack(side='left', padx=5)
        
        # Search buttons
        button_frame = tk.Frame(center_frame, bg='#ffffff')
        button_frame.pack(pady=40)
        
        tk.Button(button_frame, text="🤖 AI-Powered Search", 
                 command=lambda: self.go_to_step(2),
                 bg='#007bff', fg='white', font=('Arial', 14, 'bold'),
                 padx=30, pady=15).pack(side='left', padx=10)
        
        tk.Button(button_frame, text="⚡ Quick Search", 
                 command=lambda: self.go_to_step(3),
                 bg='#28a745', fg='white', font=('Arial', 14, 'bold'),
                 padx=30, pady=15).pack(side='left', padx=10)
    
    def show_analysis_content(self):
        """Step 2: AI Analysis - Large, readable"""
        
        # Large AI analysis
        analysis_container = tk.Frame(self.main_content, bg='#f8f9fa', relief='solid', bd=1)
        analysis_container.pack(fill='both', expand=True, pady=20)
        
        tk.Label(analysis_container, text="🤖 Gemini AI Analysis Results", 
                font=('Arial', 18, 'bold'), bg='#f8f9fa', fg='#007bff').pack(pady=20)
        
        # Large analysis display - NO tiny boxes
        analysis_text = tk.Text(analysis_container, font=('Arial', 12), height=20,
                               bg='#ffffff', relief='flat', padx=30, pady=20)
        analysis_text.pack(fill='both', expand=True, padx=30, pady=20)
        
        analysis_content = """🤖 GEMINI AI ANALYSIS COMPLETE

COMPONENT IDENTIFICATION:
✅ Type: Electronic Resistor
✅ Value: 10,000 Ohms (10kΩ)
✅ Tolerance: ±5% (standard)
✅ Power Rating: 1/4W (0.25W)
✅ Package: Through-hole preferred

KEY SPECIFICATIONS:
• Resistance: 10kΩ ±5%
• Power: 1/4W maximum
• Temperature Coefficient: ±200 ppm/°C
• Voltage Rating: 250V
• Operating Temperature: -55°C to +155°C

RECOMMENDED SEARCH TERMS:
🎯 Primary: "10k ohm resistor 1/4w carbon film"
🎯 Alternative: "10000 ohm resistor through hole"
🎯 Specific: "10kΩ ±5% 0.25W resistor"

PACKAGE OPTIONS:
📦 Through-hole (recommended for prototyping)
📦 SMD 0805 (for PCB assembly)
📦 SMD 1206 (easier hand soldering)

TOP MANUFACTURERS:
🏭 Vishay (premium quality)
🏭 Yageo (cost effective)
🏭 Panasonic (automotive grade)
🏭 Bourns (precision options)

AI CONFIDENCE: 95% ✅"""
        
        analysis_text.insert('1.0', analysis_content)
        analysis_text.config(state='disabled')
    
    def show_results_content(self):
        """Step 3: Results - Large table"""
        
        results_container = tk.Frame(self.main_content, bg='#ffffff')
        results_container.pack(fill='both', expand=True)
        
        # Results header
        header_frame = tk.Frame(results_container, bg='#ffffff')
        header_frame.pack(fill='x', pady=20)
        
        tk.Label(header_frame, text="📊 Search Results with AI Quality Scoring", 
                font=('Arial', 18, 'bold'), bg='#ffffff').pack(side='left')
        
        quality_label = tk.Label(header_frame, text="Quality: A+ | Results: 8 | AI Enhanced: 6", 
                                font=('Arial', 12), bg='#ffffff', fg='#28a745')
        quality_label.pack(side='right')
        
        # LARGE results table
        table_frame = tk.Frame(results_container, bg='#ffffff')
        table_frame.pack(fill='both', expand=True, padx=20)
        
        columns = ('Rank', 'Supplier', 'Component', 'Price', 'Stock', 'Quality', 'Total')
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        widths = {'Rank': 60, 'Supplier': 150, 'Component': 400, 'Price': 80, 'Stock': 120, 'Quality': 80, 'Total': 100}
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=widths[col])
        
        # Sample results with color coding
        results = [
            ("1", "Robu Electronics", "10kΩ Carbon Film Resistor 1/4W ±5%", "₹2", "500+ units", "A+", "₹47"),
            ("2", "Probots", "10K Ohm Resistor Through Hole", "₹1.5", "200+ units", "A", "₹51.5"),
            ("3", "Evelta", "Resistor 10kΩ 0.25W Carbon Film", "₹2.5", "100+ units", "A+", "₹42.5"),
            ("4", "SunRom", "10000 Ohm Resistor 1/4 Watt", "₹1.8", "300+ units", "A", "₹56.8"),
        ]
        
        # Color coding
        tree.tag_configure('excellent', background='#d4edda')
        tree.tag_configure('good', background='#fff3cd')
        
        for result in results:
            quality = result[5]
            tag = 'excellent' if 'A+' in quality else 'good'
            tree.insert('', 'end', values=result, tags=(tag,))
        
        tree.pack(fill='both', expand=True)
    
    def show_details_content(self):
        """Step 4: Details - Large datasheet view"""
        
        details_container = tk.Frame(self.main_content, bg='#ffffff')
        details_container.pack(fill='both', expand=True)
        
        tk.Label(details_container, text="📄 Component Details & Datasheets", 
                font=('Arial', 18, 'bold'), bg='#ffffff').pack(pady=20)
        
        # Large content area
        content_frame = tk.Frame(details_container, bg='#ffffff')
        content_frame.pack(fill='both', expand=True, padx=20)
        
        # Left - datasheet list (30%)
        left_frame = tk.Frame(content_frame, bg='#f8f9fa', relief='solid', bd=1, width=300)
        left_frame.pack(side='left', fill='y', padx=(0,10))
        left_frame.pack_propagate(False)
        
        tk.Label(left_frame, text="📋 Available Datasheets", 
                font=('Arial', 14, 'bold'), bg='#f8f9fa').pack(pady=15)
        
        datasheets = ["📄 Vishay Carbon Film", "📄 Yageo General Purpose", "📄 Resistor Selection Guide"]
        for ds in datasheets:
            tk.Button(left_frame, text=ds, anchor='w', bg='#ffffff', 
                     font=('Arial', 11), padx=15, pady=8).pack(fill='x', padx=10, pady=2)
        
        # Right - LARGE datasheet viewer (70%)
        right_frame = tk.Frame(content_frame, bg='#ffffff', relief='solid', bd=1)
        right_frame.pack(side='right', fill='both', expand=True)
        
        # Large PDF display
        pdf_display = tk.Text(right_frame, font=('Arial', 12), bg='#ffffff',
                             relief='flat', padx=30, pady=30)
        pdf_display.pack(fill='both', expand=True)
        
        pdf_content = """📄 VISHAY CARBON FILM RESISTOR DATASHEET

PART NUMBER: CFR-25JB-52-10K
RESISTANCE: 10,000 Ohms (10kΩ)
TOLERANCE: ±5%
POWER RATING: 0.25W (1/4W)

SPECIFICATIONS:
• Temperature Coefficient: ±200 ppm/°C
• Operating Temperature: -55°C to +155°C
• Maximum Voltage: 250V
• Noise: <0.2µV/V
• Lead Diameter: 0.6mm
• Body Length: 6.3mm
• Body Diameter: 2.3mm

APPLICATIONS:
✓ General purpose applications
✓ Voltage dividers
✓ Pull-up/pull-down resistors
✓ Current limiting
✓ Timing circuits

PRICING (India):
1-99 pieces: ₹2.50 each
100-999 pieces: ₹1.80 each
1000+ pieces: ₹1.20 each

AVAILABILITY: ✅ In Stock - Ships within 24 hours"""
        
        pdf_display.insert('1.0', pdf_content)
        pdf_display.config(state='disabled')
    
    def show_decision_content(self):
        """Step 5: Decision - Clear summary"""
        
        decision_container = tk.Frame(self.main_content, bg='#ffffff')
        decision_container.pack(fill='both', expand=True)
        
        tk.Label(decision_container, text="✅ Final Decision & Export", 
                font=('Arial', 18, 'bold'), bg='#ffffff').pack(pady=20)
        
        # Large summary
        summary_frame = tk.Frame(decision_container, bg='#d4edda', relief='solid', bd=1)
        summary_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        summary_text = tk.Text(summary_frame, font=('Arial', 12), bg='#d4edda',
                              relief='flat', padx=30, pady=30)
        summary_text.pack(fill='both', expand=True)
        
        summary_content = """🎯 COMPONENT SOURCING SUMMARY

SELECTED COMPONENT: 10kΩ Carbon Film Resistor 1/4W ±5%

TOP RECOMMENDATION:
🥇 Robu Electronics - ₹2.00 each + ₹45 shipping = ₹47 total
   ✅ Highest quality score (A+)
   ✅ Best availability (500+ units)
   ✅ Fast delivery (24 hours)
   ✅ Verified supplier

ALTERNATIVE OPTIONS:
🥈 Evelta - ₹2.50 each + ₹40 shipping = ₹42.50 total
🥉 Probots - ₹1.50 each + ₹50 shipping = ₹51.50 total

AI ANALYSIS SUMMARY:
✅ Component correctly identified
✅ Specifications verified
✅ Price range validated (₹1-3 expected)
✅ Quality suppliers found
✅ Datasheets available

EXPORT OPTIONS:
📊 Detailed comparison spreadsheet
📋 Procurement report with specifications
📄 Supplier contact information
🔗 Direct purchase links"""
        
        summary_text.insert('1.0', summary_content)
        summary_text.config(state='disabled')
        
        # Export buttons
        export_frame = tk.Frame(decision_container, bg='#ffffff')
        export_frame.pack(fill='x', pady=20, padx=20)
        
        buttons = [("📊 Export Excel", '#007bff'), ("📋 Generate Report", '#28a745'), 
                  ("🛒 Purchase Links", '#ffc107'), ("🔄 New Search", '#6c757d')]
        
        for text, color in buttons:
            tk.Button(export_frame, text=text, bg=color, 
                     fg='white' if color != '#ffc107' else 'black',
                     font=('Arial', 12, 'bold'), padx=25, pady=12).pack(side='left', padx=10)
    
    def show_navigation(self):
        """Show navigation buttons"""
        nav_frame = tk.Frame(self.main_content, bg='#ffffff')
        nav_frame.pack(fill='x', pady=20)
        
        if self.current_step > 1:
            tk.Button(nav_frame, text="⬅️ Previous", 
                     command=lambda: self.go_to_step(self.current_step - 1),
                     bg='#6c757d', fg='white', font=('Arial', 12),
                     padx=20, pady=10).pack(side='left')
        
        if self.current_step < 5:
            tk.Button(nav_frame, text="Next ➡️", 
                     command=lambda: self.go_to_step(self.current_step + 1),
                     bg='#007bff', fg='white', font=('Arial', 12, 'bold'),
                     padx=20, pady=10).pack(side='right')
    
    def use_suggestion(self, suggestion):
        """Use search suggestion"""
        self.search_var.set(suggestion)
    
    def go_to_step(self, step):
        """Navigate to specific step"""
        self.current_step = step
        self.update_workflow_step(step)
        self.show_step_content()
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🎯 Fixed Text Only UI")
    print("=" * 25)
    print("✅ ALL 5 steps visible at all times")
    print("✅ Fixed text occlusion with grid layout")
    print("✅ Large content areas - no tiny boxes")
    print("✅ Professional workflow visibility")
    print("✅ Clear visual progression")
    
    app = FixedTextOnlyUI()
    app.run()
