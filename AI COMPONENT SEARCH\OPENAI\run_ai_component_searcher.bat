@echo off
title AI-Powered Component Search v2.0.0

echo ========================================
echo  AI-Powered Component Search v2.0.0
echo ========================================
echo.

echo Checking system requirements...

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found

REM Check if AI features are available
echo.
echo Checking AI features...
python -c "from ai_analyzer import get_ai_analyzer; print('✅ AI Analyzer available')" 2>nul
if errorlevel 1 (
    echo ⚠️ AI features may not be fully available
    echo   Run: pip install -r requirements.txt
    echo   For local AI: Install Ollama and run 'ollama pull qwen2:7b'
)

REM Check if datasheet features are available
python -c "from datasheet_manager import get_datasheet_manager; print('✅ Datasheet Manager available')" 2>nul
if errorlevel 1 (
    echo ⚠️ Datasheet features may not be fully available
    echo   Run: pip install PyPDF2 pdfplumber
)

echo.
echo Starting application...
echo.
echo 🤖 AI Features:
echo   - Component analysis and classification
echo   - Datasheet downloading and parsing
echo   - Intelligent search recommendations
echo.
echo 🔍 Search Modes:
echo   - AI-Enhanced Search (recommended)
echo   - Smart Search (with intelligence)
echo   - Quick Search (fast results)
echo.
echo 🇮🇳 Supplier Network:
echo   - 25+ Indian suppliers prioritized
echo   - International suppliers as fallback
echo   - Real-time stock and pricing
echo.

python component_searcher.py

if errorlevel 1 (
    echo.
    echo ❌ Application encountered an error
    echo.
    echo 🔧 Troubleshooting:
    echo   1. Run test_ai_features.py to check setup
    echo   2. Install missing dependencies: pip install -r requirements.txt
    echo   3. Check internet connectivity
    echo   4. For AI features: Install Ollama or configure API keys
    echo.
)

pause
