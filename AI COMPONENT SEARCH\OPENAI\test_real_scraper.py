#!/usr/bin/env python3
"""
Test script to test real web scraping with actual Indian electronics suppliers
"""

import json
from scrapers import get_scraper

def test_real_scraping():
    """Test actual web scraping with real components"""
    
    # Load suppliers
    with open('suppliers.json', 'r') as f:
        suppliers = json.load(f)
    
    # Test components that are commonly available
    test_components = [
        "10k resistor",
        "Arduino Uno",
        "LED",
        "100nF capacitor"
    ]
    
    # Test only a few reliable suppliers to avoid overwhelming their servers
    test_suppliers = [
        suppliers['indian_tier1'][0],  # Robu
        # suppliers['indian_tier1'][1],  # Evelta (commented out for now)
    ]
    
    print("🔍 Testing Real Web Scraping")
    print("=" * 60)
    print("⚠️  Note: This makes actual HTTP requests to supplier websites")
    print("   Please use responsibly and respect their terms of service")
    print("=" * 60)
    
    for component in test_components:
        print(f"\n🔎 Searching for: {component}")
        print("-" * 40)
        
        for supplier in test_suppliers:
            print(f"\n📍 Testing {supplier['name']} ({supplier['location']})")
            
            try:
                # Get scraper
                scraper = get_scraper(supplier)
                print(f"   Using: {scraper.__class__.__name__}")
                
                # Search for component
                print(f"   Searching...")
                results = scraper.search_component(component, "Any", "", 1)
                
                if results:
                    print(f"   ✅ Found {len(results)} results:")
                    for i, result in enumerate(results[:3], 1):  # Show first 3 results
                        print(f"      {i}. {result['component'][:50]}...")
                        print(f"         Price: ₹{result['price']}")
                        print(f"         Stock: {result['stock']}")
                        print(f"         Total: ₹{result['total']}")
                        if result['url'] != supplier['url']:
                            print(f"         URL: {result['url'][:60]}...")
                else:
                    print("   ❌ No results found")
                    
            except Exception as e:
                print(f"   ⚠️  Error: {str(e)}")
        
        print("\n" + "="*60)

def test_search_url_formats():
    """Test different search URL formats"""
    
    print("\n🔧 Testing Search URL Formats")
    print("=" * 60)
    
    # Load suppliers
    with open('suppliers.json', 'r') as f:
        suppliers = json.load(f)
    
    test_query = "arduino"
    
    for supplier in suppliers['indian_tier1'][:3]:  # Test first 3 suppliers
        print(f"\n📍 {supplier['name']}")
        print(f"   Base URL: {supplier['url']}")
        
        # Test different search URL patterns
        search_patterns = [
            f"{supplier['url']}/?s={test_query}&post_type=product",
            f"{supplier['url']}/search?q={test_query}",
            f"{supplier['url']}/search?search={test_query}",
            f"{supplier['url']}/search?keyword={test_query}",
        ]
        
        for pattern in search_patterns:
            print(f"   Testing: {pattern}")
            
            try:
                import requests
                response = requests.get(pattern, timeout=5)
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    # Check if it looks like a search results page
                    content = response.text.lower()
                    if 'product' in content and ('search' in content or 'result' in content):
                        print(f"   ✅ Looks like a valid search page")
                        break
                    else:
                        print(f"   ⚠️  Page loaded but doesn't look like search results")
                else:
                    print(f"   ❌ HTTP Error: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")

def demonstrate_scraper_development():
    """Show how to develop scrapers for new websites"""
    
    print("\n🛠️  Scraper Development Process")
    print("=" * 60)
    
    print("""
Step-by-step process to add a new supplier:

1. 📋 ANALYZE THE WEBSITE
   - Visit the supplier's website
   - Try searching for a component manually
   - Inspect the search results page (F12 in browser)
   - Note the URL pattern and HTML structure

2. 🔍 IDENTIFY KEY ELEMENTS
   - Product containers (div, li, article tags)
   - Product names (h2, h3, a tags with titles)
   - Prices (spans/divs with 'price', '₹', 'Rs' text)
   - Stock status (spans/divs with 'stock', 'availability')
   - Product links (a tags with href)

3. 🧪 TEST SEARCH URLS
   - Try different search URL patterns:
     * /search?q=QUERY
     * /?s=QUERY&post_type=product
     * /search?search=QUERY
     * /search?keyword=QUERY

4. 💻 CREATE SCRAPER CLASS
   - Extend BaseScraper or GenericScraper
   - Implement perform_search() method
   - Implement parse_results() method
   - Handle errors gracefully

5. ✅ TEST AND REFINE
   - Test with different components
   - Handle edge cases (no results, errors)
   - Add delays to be respectful
   - Update suppliers.json

Example for a new supplier:

class NewSupplierScraper(BaseScraper):
    def perform_search(self, search_query):
        search_url = f"{self.config['url']}/search?q={search_query}"
        response = self.session.get(search_url, timeout=10)
        return response.text
    
    def parse_results(self, html_content, quantity):
        soup = BeautifulSoup(html_content, 'html.parser')
        results = []
        
        # Find products using CSS selectors
        products = soup.select('.product-item')  # Adjust selector
        
        for product in products[:5]:
            name = product.select_one('h3').text.strip()
            price_text = product.select_one('.price').text
            price = self.extract_price(price_text)
            
            results.append({
                'supplier': self.config['name'],
                'component': name,
                'price': price,
                'stock': 'Check Website',
                'shipping': 50,
                'total': price + 50,
                'location': self.config['location'],
                'url': self.config['url']
            })
        
        return results
""")

if __name__ == "__main__":
    print("🇮🇳 Indian Electronics Component Searcher - Real Data Test")
    print("Testing with actual supplier websites")
    
    # Show development process
    demonstrate_scraper_development()
    
    # Test search URL formats
    test_search_url_formats()
    
    # Ask user before making actual requests
    print("\n" + "="*60)
    response = input("Do you want to test actual web scraping? (y/N): ").lower().strip()
    
    if response == 'y':
        test_real_scraping()
    else:
        print("Skipping actual web scraping test.")
        print("Run with 'y' to test real scraping when ready.")
    
    print("\n" + "="*60)
    print("✅ Test completed!")
    print("The scraper framework is ready for development.")
    print("Add specific scrapers for each supplier as needed.")
    print("="*60)
