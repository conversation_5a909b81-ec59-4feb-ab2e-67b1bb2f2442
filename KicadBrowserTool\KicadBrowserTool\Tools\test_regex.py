import re

# --- MODIFIED Regular Expression ---
# Allows hyphen, space, or underscore as separator(s)
project_folder_pattern = re.compile(r"^(P-[\w\d]+)[-\s_]+(.+)$")

# The folder name you want to test (copied from your example)
folder_name_to_test = "P-0275-PCB_DETECTRA-Lite v0-CARRIERBOARD-W-Rpi-Zero2-Lora"

print(f"Testing folder name: '{folder_name_to_test}'")
print(f"With MODIFIED regex: r\"^{project_folder_pattern.pattern}$\"")

match = project_folder_pattern.match(folder_name_to_test)

if match:
    print("\nResult: MATCH FOUND!")
    print(f"  Full match: '{match.group(0)}'")
    print(f"  Group 1 (SKU part): '{match.group(1)}'")
    print(f"  Group 2 (Description part): '{match.group(2)}'")
else:
    print("\nResult: NO MATCH.")

print("\n--- Explanation of the MODIFIED regex: ---")
print(r"(P-[\w\d]+)       <-- Matches 'P-' followed by one or more letters, numbers, or underscores.")
print(r"[-\s_]+          <-- Matches one or more characters that are either a hyphen, a whitespace, or an underscore.")
print(r"(.+)              <-- Matches the rest of the string as the description.")
print("---------------------------------------")

# Let's test a few other variations to be sure
print("\n--- Additional Test Cases ---")
test_names = [
    "P-1234 Project Alpha", # Space separated
    "P-5678-Project Beta",  # Hyphen separated
    "P-9012_Project Gamma", # Underscore separated
    "P-ABCD--Multiple Separators", # Multiple hyphens
    "P-0275-PCB_DETECTRA-Lite v0-CARRIERBOARD-W-Rpi-Zero2-Lora", # Your specific case
    "P-XYZNoSeparator",     # Should not match
    "Q-1234 Project Test"   # Should not match (doesn't start with P-)
]

for name in test_names:
    m = project_folder_pattern.match(name)
    if m:
        print(f"'{name}' -> MATCH. SKU: '{m.group(1)}', Desc: '{m.group(2)}'")
    else:
        print(f"'{name}' -> NO MATCH.")
print("-----------------------------")