# P:\KiCadProjectBrowserTool\project_browser\scanner.py
import os
import json
import time
import datetime
import traceback 
import re # For version extraction

from . import config
from .utils import format_size, log_scan_error

# ==============================================================================
# START: Version Extraction Logic (adapted from standalone script)
# ==============================================================================
KICAD_COMMON_REV_PATTERNS = [
    re.compile(r'\(\s*rev\s+"([^"]*)"\s*\)', re.IGNORECASE), 
    re.compile(r'\(\s*property\s+"(?:Revision|Version)"\s+"([^"]*)"\s*\)', re.IGNORECASE),
    re.compile(r'(?:Rev|Revision|Version)\s*[:\s]\s*([^\s")]+)', re.IGNORECASE), 
]

def _find_specific_kicad_files(project_folder_path):
    """Helper to find schematic and PCB files in a project folder."""
    sch_files = []
    pcb_files = []
    try:
        for filename in os.listdir(project_folder_path):
            full_path = os.path.join(project_folder_path, filename)
            if os.path.isfile(full_path):
                if filename.endswith(".kicad_sch"):
                    if '-bak' not in filename.lower() and '-cache' not in filename.lower():
                        sch_files.insert(0, full_path)
                    else:
                        sch_files.append(full_path)
                elif filename.endswith(".kicad_pcb"):
                    if '-bak' not in filename.lower():
                        pcb_files.insert(0, full_path)
                    else:
                        pcb_files.append(full_path)
    except Exception: # Catch any error during listing (permissions, etc.)
        pass # Silently ignore if we can't list files for version extraction for this project
    return sch_files, pcb_files

def _extract_version_from_content(content, patterns):
    """Helper to extract version from file content string."""
    for pattern in patterns:
        matches = pattern.finditer(content)
        for match in matches:
            if match.groups() and match.group(1) and match.group(1).strip():
                return match.group(1).strip() 
    return None

def get_kicad_project_file_versions(project_full_path):
    """
    Extracts schematic and PCB versions from files within a given project folder path.
    Returns a combined display string (e.g., "SCH/PCB-V1.1", "SCH-V1.0", "PCB-V1.2", or "N/A").
    """
    versions = {"schematic": None, "pcb": None} # Use None to distinguish from "N/A" initially
    sch_files, pcb_files = _find_specific_kicad_files(project_full_path)

    if sch_files:
        target_sch_file = sch_files[0]
        try:
            with open(target_sch_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                sch_ver = _extract_version_from_content(content, KICAD_COMMON_REV_PATTERNS)
                if sch_ver: versions["schematic"] = sch_ver
        except Exception: # Silently ignore if file reading or parsing fails for version
            pass
    
    if pcb_files:
        target_pcb_file = pcb_files[0]
        try:
            with open(target_pcb_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                pcb_ver = _extract_version_from_content(content, KICAD_COMMON_REV_PATTERNS)
                if pcb_ver: versions["pcb"] = pcb_ver
        except Exception:
            pass
            
    # Create combined display string
    sch_display = versions['schematic']
    pcb_display = versions['pcb']
    combined_parts = []

    if sch_display and pcb_display and sch_display == pcb_display:
        combined_parts.append(f"SCH/PCB-{sch_display}")
    else:
        if sch_display:
            combined_parts.append(f"SCH-{sch_display}")
        if pcb_display:
            combined_parts.append(f"PCB-{pcb_display}")
            
    return ", ".join(combined_parts) if combined_parts else "N/A"
# ==============================================================================
# END: Version Extraction Logic
# ==============================================================================

def get_folder_size_recursive(folder_path):
    # ... (this function remains the same as your last working version) ...
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(folder_path, onerror=log_scan_error):
            dirnames[:] = [
                d for d in dirnames 
                if d not in config.EXPLICITLY_SKIP_FOLDERS and not d.startswith('.')
            ]
            for f in filenames:
                fp = os.path.join(dirpath, f)
                if not os.path.islink(fp):
                    try: total_size += os.path.getsize(fp)
                    except OSError: pass
    except Exception as e_walk_size:
        print(f"  [SizeCalc Major Error] Error walking {folder_path} for size: {e_walk_size}")
        traceback.print_exc(limit=1)
    return total_size

def scan_projects_generator():
    scan_start_time = time.monotonic()
    initial_message = f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Starting full project scan in: {config.ROOT_DIR_TO_SCAN} (Scanner for Server v{config.SERVER_VERSION})"
    print(initial_message)
    yield {"type": "status", "message": "Scan initiated..."}
    yield {"type": "status", "message": f"Scanning: {config.ROOT_DIR_TO_SCAN}"}

    projects_list = []
    claimed_project_parent_paths = set()
    directories_scanned_count = 0

    try:
        for dirpath, dirnames, filenames in os.walk(config.ROOT_DIR_TO_SCAN, topdown=True, onerror=log_scan_error):
            current_normalized_dirpath = os.path.normpath(dirpath)
            directories_scanned_count += 1

            if directories_scanned_count % 50 == 0:
                yield {"type": "status", "message": f"Scanning: {current_normalized_dirpath} ({directories_scanned_count} dirs checked)"}
            
            is_within_claimed_project = False
            for claimed_path in claimed_project_parent_paths:
                if current_normalized_dirpath.startswith(claimed_path + os.sep) and current_normalized_dirpath != claimed_path:
                    is_within_claimed_project = True; break
            if is_within_claimed_project: dirnames[:] = []; continue

            original_dirnames_at_this_level = list(dirnames)
            dirnames[:] = [d for d in dirnames if d not in config.EXPLICITLY_SKIP_FOLDERS and not d.startswith('.')]

            for folder_name in original_dirnames_at_this_level:
                if folder_name in config.EXPLICITLY_SKIP_FOLDERS or folder_name.startswith('.'):
                    continue

                project_match = config.PROJECT_FOLDER_PATTERN.match(folder_name)
                if project_match:
                    project_full_path = os.path.join(current_normalized_dirpath, folder_name)
                    
                    is_already_nested_under_claimed = False
                    for claimed_path in claimed_project_parent_paths:
                        if project_full_path.startswith(claimed_path + os.sep) and project_full_path != claimed_path:
                            is_already_nested_under_claimed = True; break
                    if is_already_nested_under_claimed: continue

                    sku = project_match.group(1)
                    description = project_match.group(2)
                    relative_path_for_display = os.path.relpath(project_full_path, config.ROOT_DIR_TO_SCAN)
                    yield {"type": "found_project", "sku": sku, "description": description, "path_display": relative_path_for_display }
                    
                    try:
                        last_modified_timestamp = os.path.getmtime(project_full_path)
                        last_modified_date = time.strftime('%Y-%m-%d', time.localtime(last_modified_timestamp))
                        
                        file_size_bytes = get_folder_size_recursive(project_full_path)
                        file_size_str = format_size(file_size_bytes)
                        
                        # ============ CALL NEW VERSION EXTRACTION FUNCTION ============
                        version_info = get_kicad_project_file_versions(project_full_path)
                        # ============================================================
                        
                        projects_list.append({
                            'sku': sku, 'description': description, 'last_modified': last_modified_date,
                            'file_size': file_size_str, 'version': version_info, 'path': project_full_path,
                            'relative_path': relative_path_for_display
                        })
                        claimed_project_parent_paths.add(project_full_path)
                        if folder_name in dirnames: dirnames.remove(folder_name)
                    except FileNotFoundError: # ... (rest of error handling for project details) ...
                        msg = f"Project folder disappeared during detail scan: {relative_path_for_display}"
                        print(f"  [SCANNER WARNING] {msg}")
                        yield {"type": "error", "message": msg}
                    except PermissionError as pe:
                        msg = f"Permission denied while getting details for {relative_path_for_display}: {pe.strerror}"
                        print(f"  [SCANNER WARNING] {msg}")
                        yield {"type": "error", "message": msg}
                    except Exception as e_details:
                        msg = f"Error processing details for {relative_path_for_display}: {str(e_details)}"
                        print(f"  [SCANNER ERROR] {msg}")
                        traceback.print_exc(limit=2) 
                        yield {"type": "error", "message": msg}
    except Exception as e_main_walk: 
        msg = f"A critical error occurred during the main directory scan: {str(e_main_walk)}"
        print(f"[SCANNER CRITICAL ERROR] {msg}")
        traceback.print_exc() 
        yield {"type": "error", "message": msg}
        yield {"type": "status", "message": "Scan aborted due to critical error."}

    projects_list.sort(key=lambda p: p.get('sku', '').lower())
    scan_end_time = time.monotonic()
    scan_duration_seconds = scan_end_time - scan_start_time
    final_output_data = {
        "metadata": {
            "server_script_version": config.SERVER_VERSION, "ui_version_target": config.UI_VERSION,
            "scan_timestamp_utc": datetime.datetime.now(datetime.timezone.utc).isoformat(),
            "scan_duration_seconds": round(scan_duration_seconds, 2),
            "total_projects_found": len(projects_list), "root_scanned": config.ROOT_DIR_TO_SCAN,
            "directories_scanned": directories_scanned_count
        },
        "projects": projects_list
    }
    try:
        with open(config.INDEX_FILE_PATH, 'w') as f: json.dump(final_output_data, f, indent=2)
        completion_message = f"Scan complete. Found {len(projects_list)} projects in {final_output_data['metadata']['scan_duration_seconds']:.2f}s. Scanned {directories_scanned_count} directories. Index saved."
        print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {completion_message}")
    except IOError as e_io:
        error_message = f"Critical: Could not write index file {config.INDEX_FILE_PATH}: {e_io}"
        print(f"\n[SCANNER_ERROR] {error_message}")
        yield {"type": "error", "message": error_message}
        
    yield {"type": "status", "message": "Scan finished. Processing final data..."}
    yield {"type": "final_data", "payload": final_output_data}
    yield {"type": "status", "message": "Scan process fully completed."}

# Main execution block for standalone testing (if any)
if __name__ == '__main__':
    print("--- Running scanner.py directly for testing the generator ---")
    # This is usually not run directly in the deployed app, run.py is the entry point
    # For testing, ensure config.py is in the parent directory or adjust path
    # For example:
    # import sys
    # sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) # Add parent of project_browser
    # from project_browser import config # Now this should work
    
    print(f"Configured ROOT_DIR_TO_SCAN: {config.ROOT_DIR_TO_SCAN if 'config' in locals() else 'config not loaded for standalone'}")

    test_project_path_for_version = r"P:\P-0278-PCB_DETECTRA-Lite v1-CARRIERBOARD-W-ESP32-S3-LoRA" # Update if needed for direct test
    if os.path.isdir(test_project_path_for_version):
         print(f"\nTesting version extraction on: {test_project_path_for_version}")
         version_str = get_kicad_project_file_versions(test_project_path_for_version)
         print(f"Extracted version string: {version_str}\n")
    else:
        print(f"\nTest project path for version extraction not found: {test_project_path_for_version}\n")

    # for progress_update in scan_projects_generator():
    #     print(f"Update from generator: {progress_update}")
    #     if progress_update.get("type") == "final_data":
    #         print(f"  Total projects in final data: {len(progress_update['payload']['projects'])}")
    print("--- End of scanner.py standalone test ---")