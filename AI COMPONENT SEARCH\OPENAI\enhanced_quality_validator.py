#!/usr/bin/env python3
"""
Enhanced Quality Validator with Component Relevance Checking
Fixes the issue where irrelevant components pass quality validation
"""

import re
from typing import List, Dict, Any, Tuple
from data_quality_validator import SourcingDataValidator

class EnhancedQualityValidator(SourcingDataValidator):
    """Enhanced validator that includes component relevance checking"""

    def __init__(self):
        super().__init__()
        # Add relevance validation to the rules
        self.validation_rules['relevance'] = self.validate_component_relevance_wrapper
        # Override component validation to be less strict
        self.validation_rules['component_name'] = self.validate_component_name_enhanced

    def validate_single_result(self, result: Dict[str, Any]) -> tuple:
        """Enhanced validation with adjusted scoring"""
        # Get base quality score
        base_score = 100
        all_issues = []

        # Run all validation rules except relevance first
        for field, validator in self.validation_rules.items():
            if field != 'relevance':  # Skip relevance for now
                # Map component field correctly
                if field == 'component_name':
                    field_value = result.get('component', '')
                else:
                    field_value = result.get(field, '')

                field_score, field_issues = validator(field_value, result)
                base_score = min(base_score, field_score)
                all_issues.extend(field_issues)

        # Now run relevance check separately
        component_name = result.get('component', '')
        relevance_score, relevance_issues = self.validate_component_relevance_wrapper(component_name, result)

        # Combine scores: base quality (40%) + relevance (60%) - relevance is more important
        if base_score >= 60:  # Only if basic quality is acceptable
            final_score = int(base_score * 0.4 + relevance_score * 0.6)
            # If relevance is very low, reject regardless of quality
            if relevance_score < 30:
                final_score = min(final_score, 40)  # Force rejection
        else:
            final_score = base_score  # Poor quality overrides relevance

        all_issues.extend(relevance_issues)

        return final_score, all_issues

    def validate_component_name_enhanced(self, name: str, result: Dict) -> Tuple[int, List[str]]:
        """Enhanced component name validation that's less strict"""
        issues = []
        score = 100

        if not name or len(name) < 3:  # More lenient than original 5
            issues.append("Component name too short or missing")
            score = 0
        elif any(x in name.lower() for x in ['{{', 'template', 'error', 'product found']):
            issues.append("Component name contains template errors")
            score = 0
        elif name.lower() in ['check website', 'contact supplier', 'product found']:
            issues.append("Generic placeholder component name")
            score = 20
        elif len(name) > 150:  # More lenient than original 100
            issues.append("Component name too long")
            score = 70

        return score, issues

    def validate_component_relevance_wrapper(self, component_name: str, result: Dict) -> Tuple[int, List[str]]:
        """Wrapper for component relevance validation"""
        search_term = result.get('search_term', '')
        return self.validate_component_relevance(component_name, search_term, result)
    
    def validate_component_relevance(self, component_name: str, search_term: str, result: Dict) -> Tuple[int, List[str]]:
        """Validate if component matches the search term - THE KEY FIX"""
        issues = []
        
        if not search_term or not component_name:
            return 50, ["Cannot validate relevance - missing search term or component name"]
        
        component_lower = component_name.lower()
        search_lower = search_term.lower()
        
        # Extract key information
        search_keywords = self.extract_component_keywords(search_lower)
        component_keywords = self.extract_component_keywords(component_lower)
        
        search_type = self.identify_component_type(search_lower)
        component_type = self.identify_component_type(component_lower)
        
        search_value = self.extract_component_value(search_lower)
        component_value = self.extract_component_value(component_lower)
        
        # Calculate relevance score
        relevance_score = 0

        # 1. Keyword matching (40 points max)
        matching_keywords = set(search_keywords) & set(component_keywords)
        if matching_keywords:
            keyword_score = min(40, 40 * (len(matching_keywords) / max(len(search_keywords), 1)))
            relevance_score += keyword_score
        else:
            # Check for partial matches or synonyms
            partial_matches = 0
            for search_word in search_keywords:
                for comp_word in component_keywords:
                    if search_word in comp_word or comp_word in search_word:
                        partial_matches += 1
                        break
            if partial_matches > 0:
                relevance_score += 20  # Partial credit for partial matches
            else:
                issues.append(f"No matching keywords between '{search_term}' and '{component_name}'")
        
        # 2. Component type matching (30 points max)
        if search_type and component_type:
            if search_type == component_type:
                relevance_score += 30
            elif self.are_related_components(search_type, component_type):
                relevance_score += 15
                issues.append(f"Component type mismatch: searched for {search_type}, found {component_type}")
            else:
                issues.append(f"Irrelevant component type: searched for {search_type}, found {component_type}")
        
        # 3. Value matching (30 points max) - CRITICAL for resistors, capacitors
        if search_value and component_value:
            # Normalize both values for comparison
            search_norm = search_value.lower().replace(' ', '').replace('ohm', '').replace('ω', '')
            comp_norm = component_value.lower().replace(' ', '').replace('ohm', '').replace('ω', '')

            if search_norm == comp_norm or search_value == component_value:
                relevance_score += 30
            elif self.are_similar_values(search_value, component_value):
                relevance_score += 25  # Higher score for similar values
                issues.append(f"Value close match: searched for {search_value}, found {component_value}")
            else:
                issues.append(f"Wrong component value: searched for {search_value}, found {component_value}")
        elif search_value and not component_value:
            issues.append(f"Missing component value: searched for {search_value} but component has no clear value")
        
        # 4. Exact phrase matching bonus
        if any(keyword in component_lower for keyword in search_keywords if len(keyword) > 3):
            relevance_score += 10
        
        # 5. Penalty for clearly irrelevant items
        irrelevant_indicators = [
            'kit', 'bundle', 'assortment', 'mixed', 'variety pack',
            'book', 'manual', 'guide', 'tutorial',
            'tool', 'equipment', 'instrument', 'meter'
        ]

        # Add conditional irrelevant indicators based on search type
        if search_type not in ['connector', 'cable']:
            irrelevant_indicators.extend(['cable', 'wire', 'adapter'])
        if search_type not in ['battery', 'power']:
            irrelevant_indicators.extend(['battery', 'power supply'])
        if search_type not in ['case', 'enclosure']:
            irrelevant_indicators.extend(['case', 'enclosure', 'box'])
        
        for indicator in irrelevant_indicators:
            if indicator in component_lower and indicator not in search_lower:
                relevance_score -= 20
                issues.append(f"Irrelevant item detected: contains '{indicator}'")
        
        # Final score (0-100)
        final_score = max(0, min(100, int(relevance_score)))
        
        # Add summary issue if score is low
        if final_score < 50:
            issues.insert(0, f"LOW RELEVANCE ({final_score}%): '{component_name}' doesn't match '{search_term}'")
        
        return final_score, issues
    
    def extract_component_keywords(self, text: str) -> List[str]:
        """Extract meaningful keywords from component text"""
        # Remove common noise words
        noise_words = {
            'the', 'and', 'or', 'with', 'for', 'in', 'on', 'at', 'to', 'from', 
            'by', 'of', 'as', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'can', 'must', 'shall', 'original', 'genuine',
            'high', 'quality', 'premium', 'standard', 'basic', 'professional',
            'piece', 'pcs', 'pack', 'set'
        }
        
        # Split and clean
        words = re.sub(r'[^\w\s]', ' ', text).split()
        keywords = []
        
        for word in words:
            clean_word = word.lower().strip()
            if len(clean_word) >= 2 and clean_word not in noise_words:
                keywords.append(clean_word)
        
        return keywords
    
    def identify_component_type(self, text: str) -> str:
        """Identify the component type from text"""
        component_types = {
            'resistor': ['resistor', 'resistance', 'ohm', 'kohm', 'mohm', 'ω'],
            'capacitor': ['capacitor', 'cap', 'uf', 'pf', 'nf', 'mf', 'farad', 'µf', 'μf'],
            'inductor': ['inductor', 'coil', 'choke', 'henry', 'mh', 'uh'],
            'diode': ['diode', 'led', 'zener', 'schottky', 'rectifier'],
            'transistor': ['transistor', 'mosfet', 'bjt', 'fet', 'igbt'],
            'ic': ['ic', 'chip', 'microcontroller', 'processor', 'controller', 'mcu'],
            'arduino': ['arduino', 'uno', 'nano', 'mega', 'leonardo', 'pro'],
            'esp': ['esp32', 'esp8266', 'esp', 'wifi', 'bluetooth', 'nodemcu'],
            'sensor': ['sensor', 'temperature', 'humidity', 'pressure', 'motion', 'ultrasonic'],
            'display': ['display', 'lcd', 'oled', 'tft', 'screen', 'monitor'],
            'motor': ['motor', 'servo', 'stepper', 'dc', 'gear', 'brushless'],
            'connector': ['connector', 'header', 'socket', 'pin', 'terminal', 'jumper'],
            'switch': ['switch', 'button', 'toggle', 'push', 'tactile'],
            'relay': ['relay', 'contactor', 'ssr'],
            'crystal': ['crystal', 'oscillator', 'xtal', 'resonator'],
            'battery': ['battery', 'cell', 'lithium', 'nimh', 'alkaline'],
            'power': ['power', 'supply', 'adapter', 'charger', 'regulator'],
            'cable': ['cable', 'wire', 'jumper', 'dupont'],
            'breadboard': ['breadboard', 'protoboard', 'perfboard'],
            'module': ['module', 'breakout', 'board', 'shield']
        }
        
        for comp_type, keywords in component_types.items():
            if any(keyword in text for keyword in keywords):
                return comp_type
        
        return 'unknown'
    
    def are_related_components(self, type1: str, type2: str) -> bool:
        """Check if two component types are related"""
        related_groups = [
            {'resistor', 'capacitor', 'inductor'},  # Passive components
            {'diode', 'transistor', 'ic'},  # Semiconductors
            {'arduino', 'esp', 'ic', 'module'},  # Microcontrollers
            {'sensor', 'display', 'module'},  # I/O components
            {'switch', 'relay', 'connector'},  # Interface components
            {'motor', 'servo'},  # Actuators
            {'battery', 'power'},  # Power components
            {'cable', 'connector', 'jumper'}  # Connection components
        ]
        
        for group in related_groups:
            if type1 in group and type2 in group:
                return True
        
        return False
    
    def extract_component_value(self, text: str) -> str:
        """Extract component value (10k, 100uF, etc.)"""
        # Patterns for common component values
        patterns = [
            r'(\d+\.?\d*)\s*k\s*ohm',  # 10k ohm
            r'(\d+\.?\d*)\s*kohm',     # 10kohm  
            r'(\d+\.?\d*)\s*k(?!\w)',  # 10k (not followed by letter)
            r'(\d+\.?\d*)\s*ohm',      # 100 ohm
            r'(\d+\.?\d*)\s*ω',        # 10ω
            r'(\d+\.?\d*)\s*µf',       # 100µf
            r'(\d+\.?\d*)\s*uf',       # 100uf
            r'(\d+\.?\d*)\s*pf',       # 22pf
            r'(\d+\.?\d*)\s*nf',       # 1nf
            r'(\d+\.?\d*)\s*mf',       # 1mf
            r'(\d+\.?\d*)\s*mh',       # 10mh
            r'(\d+\.?\d*)\s*uh',       # 100uh
            r'(\d+\.?\d*)\s*v(?!\w)',  # 5v
            r'(\d+\.?\d*)\s*a(?!\w)',  # 1a
            r'(\d+\.?\d*)\s*mhz',      # 16mhz
            r'(\d+\.?\d*)\s*khz',      # 32khz
            r'(\d+\.?\d*)\s*w(?!\w)',  # 5w
            r'(\d+\.?\d*)\s*ma',       # 100ma
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(0).lower().replace(' ', '')
        
        return ''
    
    def are_similar_values(self, value1: str, value2: str) -> bool:
        """Check if two component values are similar"""
        # Normalize values first
        v1_norm = value1.lower().replace(' ', '').replace('ohm', '').replace('ω', '')
        v2_norm = value2.lower().replace(' ', '').replace('ohm', '').replace('ω', '')

        # Direct match after normalization
        if v1_norm == v2_norm:
            return True

        # Extract numeric parts
        num1 = re.search(r'(\d+\.?\d*)', v1_norm)
        num2 = re.search(r'(\d+\.?\d*)', v2_norm)

        if not num1 or not num2:
            return False

        try:
            n1 = float(num1.group(1))
            n2 = float(num2.group(1))

            # Check if values are within 20% of each other
            if min(n1, n2) == 0:
                return False
            ratio = max(n1, n2) / min(n1, n2)
            return ratio <= 1.2
        except:
            return False

def validate_search_results_enhanced(results: List[Dict[str, Any]], search_term: str = "") -> Dict[str, Any]:
    """Enhanced validation with component relevance checking"""
    # Add search term to each result for relevance checking
    for result in results:
        if 'search_term' not in result:
            result['search_term'] = search_term
    
    validator = EnhancedQualityValidator()
    return validator.validate_results(results)

if __name__ == "__main__":
    # Test with problematic data that should be filtered out
    test_results = [
        {
            'supplier': 'TestSupplier',
            'component': '10k Ohm Resistor 1/4W Carbon Film',  # RELEVANT
            'price': 2,
            'stock': 'In Stock (100 units)',
            'shipping': 45,
            'specifications': '1/4W, 5% tolerance, carbon film',
            'location': 'Mumbai, Maharashtra',
            'url': 'https://testsupplier.com/10k-resistor',
            'search_term': '10k resistor'
        },
        {
            'supplier': 'TestSupplier',
            'component': 'Arduino Uno R3 Development Board',  # IRRELEVANT for resistor search
            'price': 650,
            'stock': 'In Stock (15 units)',
            'shipping': 55,
            'specifications': '5V, USB, ATmega328P microcontroller',
            'location': 'Delhi, India',
            'url': 'https://testsupplier.com/arduino-uno',
            'search_term': '10k resistor'
        },
        {
            'supplier': 'TestSupplier',
            'component': 'LED Strip 5m RGB Waterproof',  # IRRELEVANT for resistor search
            'price': 450,
            'stock': 'In Stock (8 units)',
            'shipping': 60,
            'specifications': '12V, RGB, 5 meters, IP65 waterproof',
            'location': 'Bangalore, Karnataka',
            'url': 'https://testsupplier.com/led-strip',
            'search_term': '10k resistor'
        }
    ]
    
    print("🧪 Testing Enhanced Quality Validator")
    print("=" * 45)
    
    validation_result = validate_search_results_enhanced(test_results, "10k resistor")
    
    print(f"Original results: {len(test_results)}")
    print(f"Validated results: {len(validation_result['validated_results'])}")
    print(f"Rejection rate: {validation_result['quality_report']['rejection_rate']:.1f}%")

    # Show filtering effectiveness
    print(f"\n🎯 Filtering Results:")
    relevant_count = len([r for r in validation_result['validated_results']
                         if 'resistor' in r['component'].lower() or '10k' in r['component'].lower()])
    irrelevant_filtered = len(test_results) - len(validation_result['validated_results'])
    print(f"   Relevant components found: {relevant_count}")
    print(f"   Irrelevant components filtered: {irrelevant_filtered}")

    print("\nValidated Results:")
    for result in validation_result['validated_results']:
        print(f"  ✅ {result['component']} (Score: {result['quality_score']})")

    print("\nRejected Results:")
    for result in test_results:
        if result not in [r for r in validation_result['validated_results']]:
            print(f"  ❌ {result['component']} (Irrelevant to search)")

    print(f"\n🎯 Quality Grade: {validation_result['quality_report']['quality_grade']}")
    print("✅ Enhanced validator working - irrelevant items filtered out!")
