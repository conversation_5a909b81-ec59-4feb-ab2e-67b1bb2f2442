#!/usr/bin/env python3
"""
Test script to verify Ollama integration with DeerFlow
"""

import os
import sys
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_ollama_direct():
    """Test Ollama directly using requests"""
    print("Testing Ollama directly...")
    
    import requests
    import json
    
    try:
        url = "http://localhost:11434/v1/chat/completions"
        headers = {"Content-Type": "application/json"}
        data = {
            "model": "qwen2:7b",
            "messages": [
                {"role": "user", "content": "Hello! Please respond with 'Ollama direct API is working!' to confirm."}
            ],
            "max_tokens": 100
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            print(f"✅ Ollama Direct Response: {content}")
            return True
        else:
            print(f"❌ Ollama Direct Error: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Ollama Direct Error: {e}")
        return False

def test_deer_flow_config():
    """Test DeerFlow configuration loading"""
    print("\nTesting DeerFlow configuration loading...")
    
    try:
        from src.config.loader import load_yaml_config
        
        config = load_yaml_config("conf_ollama.yaml")
        basic_model = config.get("BASIC_MODEL", {})
        
        print(f"✅ Configuration loaded:")
        print(f"   Base URL: {basic_model.get('base_url')}")
        print(f"   Model: {basic_model.get('model')}")
        print(f"   API Key: {basic_model.get('api_key')}")
        
        if basic_model.get('base_url') == 'http://localhost:11434/v1':
            return True
        else:
            print(f"❌ Configuration not updated correctly")
            return False
            
    except Exception as e:
        print(f"❌ Configuration Error: {e}")
        return False

def test_deer_flow_llm():
    """Test DeerFlow LLM loading with Ollama"""
    print("\nTesting DeerFlow LLM loading...")
    
    try:
        from src.llms.llm import get_llm_by_type
        
        llm = get_llm_by_type("basic")
        print(f"✅ LLM created: {type(llm)}")
        
        # Test a simple invoke
        response = llm.invoke("Hello! Please respond with 'DeerFlow LLM with Ollama is working!' to confirm.")
        print(f"✅ DeerFlow LLM Response: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ DeerFlow LLM Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing Ollama Integration with DeerFlow\n")
    
    # Test 1: Direct Ollama API
    ollama_works = test_ollama_direct()
    
    # Test 2: DeerFlow Config
    config_works = test_deer_flow_config()
    
    # Test 3: DeerFlow LLM
    deer_flow_works = test_deer_flow_llm()
    
    print(f"\n📊 Test Results:")
    print(f"   Ollama Direct: {'✅ PASS' if ollama_works else '❌ FAIL'}")
    print(f"   Config Load:   {'✅ PASS' if config_works else '❌ FAIL'}")
    print(f"   DeerFlow LLM:  {'✅ PASS' if deer_flow_works else '❌ FAIL'}")
    
    if ollama_works and config_works and deer_flow_works:
        print(f"\n🎉 All tests passed! Ollama is ready to use with DeerFlow!")
    else:
        print(f"\n⚠️  Some tests failed. Check the errors above.")
