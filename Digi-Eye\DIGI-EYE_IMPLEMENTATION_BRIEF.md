# Project Brief: "Digi-Eye" Implementation

## 1. Project Objective
Create a Windows desktop application using Python that controls a 7-segment display via a USB serial port and simultaneously uses a USB camera with OpenCV to visually recognize and verify the displayed number in real-time.

## 2. Core Components & Technologies

### NUC Application (Python)
- **Language**: Python 3.9+
- **UI Framework**: `customtkinter` for a modern look and feel.
- **Computer Vision**: `opencv-python` for camera handling and image processing.
- **Serial Communication**: `pyserial` for communicating with the RP2040.
- **Concurrency**: Use Python's `threading` module to run the UI, camera processing, and serial communication in separate threads to ensure a responsive interface.

### RP2040 Firmware (C/C++)
- **Platform**: Raspberry Pi Pico SDK (C/C++).
- **USB Stack**: Use the built-in `tinyusb` library to implement a USB CDC device (Virtual COM Port).
- **Functionality**:
    - Listen for simple newline-terminated commands on the virtual serial port.
    - Implement a command parser for commands like `S{value}\n` (set value), `R\n` (random), etc.
    - Write hardware abstraction functions to control the GPIOs connected to the 7-segment display driver.

## 3. Implementation Plan

### Step 1: RP2040 Firmware
1.  Initialize a Pico SDK project.
2.  Enable USB CDC and verify it appears as a COM port on Windows.
3.  Implement the serial command parser.
4.  Write placeholder functions for display driving. Test by printing parsed commands back to the serial console.

### Step 2: NUC Application - UI & Core Structure
1.  Set up a Python virtual environment with `customtkinter`, `opencv-python`, `pyserial`, `numpy`.
2.  Design the main UI window with placeholders for the camera feed, control buttons, and status labels.
3.  Implement the multi-threaded architecture:
    - A `main.py` to launch the UI.
    - A `CameraThread` that continuously grabs frames and puts them in a queue.
    - A `SerialThread` to handle sending/receiving data.

### Step 3: NUC Application - Vision & OCR
1.  In the `CameraThread`, implement the deterministic OCR logic:
2.  **Isolate Display**: Use a one-time calibration or a simple ROI selector to define the display area. Apply a perspective transform to get a flat, stable view.
3.  **Pre-process**: Convert to grayscale and apply a binary threshold.
4.  **Segment Digits**: Divide the ROI into sub-images for each digit.
5.  **Recognize Segments**: For each digit, define 7 sub-ROIs for the 7 segments. Check the pixel sum in each to determine if it's on/off.
6.  **Lookup Value**: Use a dictionary to map the 7-segment on/off pattern to a character ('0'-'9', '-', '.').
7.  Emit a custom signal or update a shared state variable with the recognized string.

### Step 4: Integration
1.  Connect the UI buttons to the `SerialThread` to send commands.
2.  Connect the `CameraThread`'s output to the UI labels to display the recognized value.
3.  Add logic to automatically find and connect to the correct COM port for the RP2040.
4.  Refine the UI, adding status indicators and error handling.

## 4. Serial Protocol Definition
- `S<value>`: Set a specific value. e.g., `S12.34`, `S-0.5`
- `R`: Display a random number.
- `U<ms>`: Count up every `<ms>` milliseconds.
- `D<ms>`: Count down every `<ms>` milliseconds.
- `P`: Pause/Stop counting.
- **Terminator**: All commands must end with a newline character `\n`.