#!/usr/bin/env python3
"""
Test Web-based AI Providers
Test the web automation for AI chatbots like DeepSeek, ChatGPT, etc.
"""

import sys
import os
import traceback

def test_web_automation_imports():
    """Test if web automation dependencies are available"""
    print("🔍 Testing web automation imports...")
    
    try:
        import selenium
        print(f"  ✅ selenium {selenium.__version__}")
    except ImportError as e:
        print(f"  ❌ selenium - {e}")
        return False
    
    try:
        import undetected_chromedriver as uc
        print(f"  ✅ undetected-chromedriver")
    except ImportError as e:
        print(f"  ❌ undetected-chromedriver - {e}")
        print("     Install with: pip install undetected-chromedriver")
        return False
    
    try:
        from web_ai_provider import get_web_ai_automator
        print("  ✅ web_ai_provider - Web AI automation")
    except ImportError as e:
        print(f"  ❌ web_ai_provider - {e}")
        return False
    
    return True

def test_browser_setup():
    """Test browser setup"""
    print("\n🌐 Testing browser setup...")
    
    try:
        from web_ai_provider import get_web_ai_automator
        
        automator = get_web_ai_automator()
        print("  ✅ Web AI automator initialized")
        
        # Test browser setup (without actually opening)
        print("  🔧 Testing browser configuration...")
        
        # Check Chrome availability
        try:
            import undetected_chromedriver as uc
            print("  ✅ Chrome driver available")
        except Exception as e:
            print(f"  ❌ Chrome driver issue: {e}")
            return False
        
        print("  ✅ Browser setup ready")
        return True
        
    except Exception as e:
        print(f"  ❌ Browser setup failed: {e}")
        traceback.print_exc()
        return False

def test_provider_configuration():
    """Test provider configuration"""
    print("\n⚙️ Testing provider configuration...")
    
    try:
        from web_ai_provider import get_web_ai_automator
        
        automator = get_web_ai_automator()
        providers = automator.get_available_providers()
        
        print(f"  📋 Available providers: {len(providers)}")
        for provider in providers:
            provider_info = automator.providers[provider]
            print(f"    • {provider_info.name}")
            print(f"      URL: {provider_info.url}")
            print(f"      Login required: {provider_info.login_required}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Provider configuration test failed: {e}")
        traceback.print_exc()
        return False

def test_ai_analyzer_integration():
    """Test AI analyzer integration with web providers"""
    print("\n🤖 Testing AI analyzer integration...")
    
    try:
        from ai_analyzer import get_ai_analyzer
        
        analyzer = get_ai_analyzer()
        print("  ✅ AI analyzer initialized")
        
        print(f"  📊 Active provider: {analyzer.active_provider}")
        print(f"  🔧 Available providers: {list(analyzer.providers.keys())}")
        
        # Test provider connections (without actually connecting)
        print("\n  🔗 Testing provider configurations:")
        for provider_name in analyzer.providers:
            provider_config = analyzer.providers[provider_name]
            enabled = provider_config.get("enabled", False)
            status = "✅ Enabled" if enabled else "⚪ Disabled"
            print(f"    {provider_name}: {status}")
            
            if enabled and provider_name.endswith("_web"):
                print(f"      Name: {provider_config.get('name', 'Unknown')}")
                print(f"      Description: {provider_config.get('description', 'No description')}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ AI analyzer integration test failed: {e}")
        traceback.print_exc()
        return False

def test_configuration_dialog():
    """Test web AI configuration dialog"""
    print("\n🖥️ Testing configuration dialog...")
    
    try:
        from web_ai_config_dialog import WebAIConfigDialog
        print("  ✅ Web AI configuration dialog available")
        
        # Test configuration loading
        dialog_class = WebAIConfigDialog
        print("  ✅ Dialog class can be imported")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ Configuration dialog import failed: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Configuration dialog test failed: {e}")
        traceback.print_exc()
        return False

def test_component_analysis_prompt():
    """Test component analysis prompt generation"""
    print("\n📝 Testing component analysis prompts...")
    
    try:
        from ai_analyzer import get_ai_analyzer
        
        analyzer = get_ai_analyzer()
        
        # Test prompt generation
        test_component = "arduino uno"
        prompt = analyzer._build_component_analysis_prompt(test_component)
        
        print(f"  ✅ Generated prompt for '{test_component}'")
        print(f"  📏 Prompt length: {len(prompt)} characters")
        
        # Check if prompt contains expected elements
        expected_elements = ["JSON", "component_type", "manufacturer", "specifications"]
        for element in expected_elements:
            if element in prompt:
                print(f"    ✅ Contains '{element}'")
            else:
                print(f"    ⚠️ Missing '{element}'")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Prompt generation test failed: {e}")
        traceback.print_exc()
        return False

def show_usage_instructions():
    """Show usage instructions for web AI"""
    print("\n" + "=" * 60)
    print("🚀 Web-based AI Setup Instructions")
    print("=" * 60)
    
    print("\n📋 QUICK START:")
    print("1. Run the main application: python component_searcher.py")
    print("2. Go to Tools → AI Configuration")
    print("3. Enable your preferred providers:")
    print("   • DeepSeek Chat (Free, no login)")
    print("   • Perplexity AI (Free, no login)")
    print("   • You.com Chat (Free, no login)")
    print("   • ChatGPT Free (requires OpenAI account)")
    print("   • Claude Free (requires Anthropic account)")
    
    print("\n🔧 BROWSER REQUIREMENTS:")
    print("• Chrome browser must be installed")
    print("• Internet connection required")
    print("• Allow browser automation when prompted")
    
    print("\n🤖 AI SEARCH USAGE:")
    print("1. Enter component name (e.g., 'arduino uno')")
    print("2. Click '🤖 AI Search' button")
    print("3. AI will analyze and enhance your search")
    print("4. Get intelligent results with quality scoring")
    
    print("\n⚙️ CONFIGURATION:")
    print("• Free providers work immediately")
    print("• Login providers need account credentials")
    print("• Browser can run visible or in background")
    print("• Default provider: DeepSeek (recommended)")
    
    print("\n🔍 TROUBLESHOOTING:")
    print("• If browser fails: Update Chrome browser")
    print("• If login fails: Check credentials in config")
    print("• If slow: Enable headless mode in settings")
    print("• If blocked: Try different provider")

def main():
    """Run all web AI tests"""
    print("🌐 Web-based AI Provider Test")
    print("=" * 40)
    print("Testing web automation for AI chatbots")
    print("=" * 40)
    
    tests = [
        ("Web Automation Imports", test_web_automation_imports),
        ("Browser Setup", test_browser_setup),
        ("Provider Configuration", test_provider_configuration),
        ("AI Analyzer Integration", test_ai_analyzer_integration),
        ("Configuration Dialog", test_configuration_dialog),
        ("Component Analysis Prompts", test_component_analysis_prompt)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 Web AI Test Summary:")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Web-based AI is ready to use.")
        show_usage_instructions()
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        print("\n🔧 Common solutions:")
        print("   - Install dependencies: pip install selenium undetected-chromedriver")
        print("   - Update Chrome browser to latest version")
        print("   - Check internet connectivity")
        print("   - Run as administrator if needed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
