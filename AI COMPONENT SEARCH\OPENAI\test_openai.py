#!/usr/bin/env python3
"""
Test script to verify OpenAI API key and integration with DeerFlow
"""

import os
from langchain_openai import ChatOpenAI

def test_openai_direct():
    """Test OpenAI directly using langchain-openai"""
    print("Testing OpenAI API key directly...")
    
    # Set up OpenAI client
    openai_llm = ChatOpenAI(
        model="gpt-4o-mini",  # Using mini for cost efficiency
        api_key="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",
        temperature=0.1
    )
    
    try:
        response = openai_llm.invoke("Hello! Please respond with 'OpenAI is working!' to confirm the connection.")
        print(f"✅ OpenAI Response: {response.content}")
        return True
    except Exception as e:
        print(f"❌ OpenAI Error: {e}")
        if "insufficient_quota" in str(e):
            print("💡 This means you need to add API credits to your OpenAI account.")
            print("   ChatGPT Plus subscription is separate from API credits.")
            print("   Go to: https://platform.openai.com/settings/organization/billing")
        elif "invalid" in str(e).lower():
            print("💡 API key format issue - let me check the key format...")
        return False

def test_deer_flow_llm():
    """Test DeerFlow's LLM loading with OpenAI"""
    print("\nTesting DeerFlow LLM loading with OpenAI...")
    
    try:
        # Set environment variable for OpenAI
        os.environ["OPENAI_API_KEY"] = "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"
        
        from src.llms.llm import get_llm_by_type
        
        llm = get_llm_by_type("basic")
        response = llm.invoke("Hello! Please respond with 'DeerFlow with OpenAI is working!' to confirm.")
        print(f"✅ DeerFlow LLM Response: {response.content}")
        return True
    except Exception as e:
        print(f"❌ DeerFlow LLM Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing OpenAI Integration with DeerFlow\n")
    
    # Test 1: Direct OpenAI
    openai_works = test_openai_direct()
    
    # Test 2: DeerFlow LLM
    deer_flow_works = test_deer_flow_llm()
    
    print(f"\n📊 Test Results:")
    print(f"   OpenAI Direct: {'✅ PASS' if openai_works else '❌ FAIL'}")
    print(f"   DeerFlow LLM:  {'✅ PASS' if deer_flow_works else '❌ FAIL'}")
    
    if openai_works and deer_flow_works:
        print(f"\n🎉 All tests passed! OpenAI is ready to use with DeerFlow!")
    elif openai_works:
        print(f"\n⚠️  OpenAI works directly but DeerFlow integration needs fixing.")
    else:
        print(f"\n❌ OpenAI connection failed. Check your API key and network.")
