import os
import json
import time
import re
import platform
import subprocess
from flask import Flask, jsonify, render_template, request, send_from_directory
from flask_cors import CORS

# --- Configuration ---
# The server.py script is inside KiCadProjectBrowserTool.
# We want to scan the PARENT directory of where this script is.
# So, if server.py is in P:\KiCadProjectBrowserTool\, ROOT_DIR_TO_SCAN will be P:\
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
ROOT_DIR_TO_SCAN = os.path.dirname(SCRIPT_DIR) # Parent directory of the script's location

# Name of the JSON file to cache project data
INDEX_FILE_NAME = 'project_index.json'
INDEX_FILE_PATH = os.path.join(SCRIPT_DIR, INDEX_FILE_NAME) # Cache file inside KiCadProjectBrowserTool

# Regex for project folders (same as the successful one from discovery)
PROJECT_FOLDER_PATTERN = re.compile(r"^(P-[\w\d]+)[-\s_]+(.+)$")

# Folders to explicitly skip during scanning. Add more if needed.
# These are relative to the directory being scanned at any given moment.
EXPLICITLY_SKIP_FOLDERS = {
    "KiCadProjectBrowserTool",  # Always skip the tool's own folder
    ".git",
    "node_modules",
    "$RECYCLE.BIN",
    "System Volume Information",
    # Add other common large non-project folders you know exist on P:\
    # e.g., "Windows", "Program Files", "Software_Installers", "Video_Archive"
}
# --- End Configuration ---

app = Flask(__name__, template_folder='templates') # Assumes templates/index.html
CORS(app) # Allows cross-origin requests, useful for development

def format_size(size_bytes):
    if size_bytes == 0:
        return "0B"
    size_name = ("B", "KB", "MB", "GB", "TB")
    i = int(min(len(size_name) - 1, int(abs(size_bytes)).bit_length() // 10))
    p = 1024 ** i
    s = round(size_bytes / p, 1) # Show one decimal place
    return f"{s}{size_name[i]}"

def get_folder_size(folder_path):
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(folder_path, onerror=lambda e: print(f"  [SizeCalc Error] Cannot access {e.filename} in {folder_path}")):
            # Skip delving into explicitly skipped subfolders even for size calculation
            # to avoid permission errors or large unwanted directories.
            dirnames[:] = [d for d in dirnames if d not in EXPLICITLY_SKIP_FOLDERS and not d.startswith('.')]
            for f in filenames:
                fp = os.path.join(dirpath, f)
                if not os.path.islink(fp):
                    try:
                        total_size += os.path.getsize(fp)
                    except OSError as e:
                        print(f"  [SizeCalc Error] Cannot get size of {fp}: {e}")
                        continue
    except Exception as e:
        print(f"  [SizeCalc Error] General error walking {folder_path} for size: {e}")
    return total_size

def log_scan_error(os_error):
    print(f"  [Scan Error] Could not access: {os_error.filename} - {os_error.strerror}. Skipping.")

def scan_projects_and_save():
    print(f"Starting full project scan in: {ROOT_DIR_TO_SCAN}")
    print(f"Using regex: r\"{PROJECT_FOLDER_PATTERN.pattern}\"")

    projects = []
    # Keep track of paths that have already yielded a top-level project
    # to implement the "only top-most P-XXXX project" rule.
    claimed_project_parent_paths = set()

    for dirpath, dirnames, filenames in os.walk(ROOT_DIR_TO_SCAN, topdown=True, onerror=log_scan_error):
        current_normalized_dirpath = os.path.normpath(dirpath)
        
        # Check if this path is already part of an identified project higher up
        # Example: if P:\FoundProject was identified, skip P:\FoundProject\SubDir
        is_within_claimed_project = False
        for claimed_path in claimed_project_parent_paths:
            if current_normalized_dirpath.startswith(claimed_path + os.sep) and current_normalized_dirpath != claimed_path:
                is_within_claimed_project = True
                break
        
        if is_within_claimed_project:
            dirnames[:] = [] # Don't descend further into already claimed project areas for *new* projects
            continue

        # Feedback for user
        print(f"Scanning dir: {current_normalized_dirpath}")

        original_dirnames = list(dirnames) # Iterate over a copy for matching
        # Modify dirnames in place to control os.walk's traversal
        dirnames[:] = [d for d in dirnames if d not in EXPLICITLY_SKIP_FOLDERS and not d.startswith('.')]

        for folder_name in original_dirnames:
            if folder_name in EXPLICITLY_SKIP_FOLDERS or folder_name.startswith('.'):
                continue

            project_match = PROJECT_FOLDER_PATTERN.match(folder_name)
            if project_match:
                project_full_path = os.path.join(current_normalized_dirpath, folder_name)
                
                # Check if this project's parent path is already part of another claimed project
                # This handles cases where a P-XXXX might be sibling to another P-XXXX's subfolder
                # but the logic above should mostly cover it.
                is_nested_claim = False
                for claimed_path in claimed_project_parent_paths:
                     if project_full_path.startswith(claimed_path + os.sep) and project_full_path != claimed_path:
                        is_nested_claim = True
                        break
                if is_nested_claim:
                    print(f"  Skipping nested P-folder: {project_full_path} (already under a primary project)")
                    continue

                print(f"  Found potential project: {folder_name}")
                sku = project_match.group(1)
                description = project_match.group(2)
                
                try:
                    last_modified_timestamp = os.path.getmtime(project_full_path)
                    last_modified_date = time.strftime('%Y-%m-%d', time.localtime(last_modified_timestamp))
                    
                    print(f"    Calculating size for: {project_full_path}...")
                    file_size_bytes = get_folder_size(project_full_path)
                    file_size_str = format_size(file_size_bytes)
                    print(f"    ...size: {file_size_str}")
                    
                    version = "N/A" # Placeholder for version
                    # Later, we can implement KiCad file parsing here
                    # For now, you could optionally look for version.txt if you want
                    # version_file_path = os.path.join(project_full_path, 'version.txt')
                    # if os.path.exists(version_file_path):
                    #     with open(version_file_path, 'r') as vf:
                    #         version = vf.read().strip()
                    
                    projects.append({
                        'sku': sku,
                        'description': description,
                        'last_modified': last_modified_date,
                        'file_size': file_size_str,
                        'version': version,
                        'path': project_full_path, # Full path for opening the folder
                        'relative_path': os.path.relpath(project_full_path, ROOT_DIR_TO_SCAN) # For display/reference
                    })
                    
                    # Add this project's path to claimed_project_parent_paths
                    # so that P-XXXX folders *inside* this one are not listed as new top-level projects.
                    claimed_project_parent_paths.add(project_full_path)
                    
                    # We found a project, so remove it from dirnames for THIS os.walk iteration
                    # to prevent os.walk from trying to find P-projects *inside* this P-project
                    # *as new top-level candidates*. It has already been claimed.
                    if folder_name in dirnames:
                        dirnames.remove(folder_name)

                except Exception as e:
                    print(f"  [ERROR] Processing project {project_full_path}: {e}")
    
    projects.sort(key=lambda p: p.get('sku', '').lower()) # Sort by SKU
    
    try:
        with open(INDEX_FILE_PATH, 'w') as f:
            json.dump(projects, f, indent=2)
        print(f"\nScan complete. Found {len(projects)} projects. Index saved to {INDEX_FILE_PATH}")
    except IOError as e:
        print(f"\n[ERROR] Could not write index file {INDEX_FILE_PATH}: {e}")
        
    return projects

# --- Flask App Routes ---
@app.route('/')
def index_page():
    # This will render KiCadProjectBrowserTool/templates/index.html
    return render_template('index.html')

@app.route('/api/projects', methods=['GET'])
def get_projects_api():
    if not os.path.exists(INDEX_FILE_PATH):
        print(f"{INDEX_FILE_NAME} not found. Triggering initial scan...")
        scan_projects_and_save() # Perform initial scan if index doesn't exist
    
    try:
        with open(INDEX_FILE_PATH, 'r') as f:
            projects_data = json.load(f)
        return jsonify(projects_data)
    except FileNotFoundError:
        print(f"[Warning] {INDEX_FILE_NAME} not found after attempting scan. Returning empty list.")
        return jsonify([])
    except json.JSONDecodeError:
        print(f"[Warning] {INDEX_FILE_NAME} is corrupted. Triggering re-scan...")
        projects_data = scan_projects_and_save()
        return jsonify(projects_data)

@app.route('/api/scan', methods=['POST'])
def trigger_scan_api():
    print("Manual scan triggered via API.")
    projects_data = scan_projects_and_save()
    return jsonify({"message": "Scan complete", "project_count": len(projects_data)})

@app.route('/api/open_folder', methods=['POST'])
def open_folder_api():
    data = request.get_json()
    folder_path = data.get('path')
    if not folder_path or not os.path.isabs(folder_path) or not os.path.isdir(folder_path):
        # Ensure path is absolute and exists
        return jsonify({"success": False, "message": f"Invalid or non-existent path: {folder_path}"}), 400

    print(f"Attempting to open folder: {folder_path}")
    try:
        if platform.system() == "Windows":
            # os.startfile is generally safer and more reliable on Windows
            os.startfile(folder_path)
        elif platform.system() == "Darwin": # macOS
            subprocess.run(['open', folder_path], check=True)
        else: # Linux and other Unix-like
            subprocess.run(['xdg-open', folder_path], check=True)
        return jsonify({"success": True, "message": f"Opened {folder_path}"})
    except Exception as e:
        print(f"  [ERROR] Opening folder {folder_path}: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

if __name__ == '__main__':
    print("Starting KiCad Project Browser Server...")
    print(f"Root directory to scan for projects: {ROOT_DIR_TO_SCAN}")
    print(f"Project index file will be: {INDEX_FILE_PATH}")
    print(f"Open http://127.0.0.1:5000 in your browser.")
    
    # Optional: Perform a scan on startup if index doesn't exist.
    # The /api/projects route also handles this.
    if not os.path.exists(INDEX_FILE_PATH):
         print("No project index found. Performing initial scan on startup...")
         scan_projects_and_save()

    app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
    # use_reloader=False is important if scan_on_startup is enabled,
    # otherwise Flask's reloader might trigger the scan twice.