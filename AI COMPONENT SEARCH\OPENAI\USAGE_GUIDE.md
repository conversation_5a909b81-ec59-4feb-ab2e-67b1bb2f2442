# KiCad Version Extractor - Usage Guide

## Quick Start

### First Time Setup
1. **Install Dependencies**: Double-click `install_dependencies.bat` (Windows) or run `pip install -r requirements.txt`
2. **Launch Application**: Double-click `run_kicad_extractor.bat` or `kicad_version_extractor.py`

### Using the GUI

#### Single File Processing
1. **Browse for File**: Click "Browse" button and select a KiCad file
2. **Or Drag & Drop**: Simply drag a KiCad file onto the window
3. **Or Use Recent Files**: Select from the "Recent" dropdown
4. **Extract Version**: Click "Extract Version" button

#### Batch Processing
1. **Select Multiple Files**: Click "Browse Multiple Files"
2. **Select Files**: Hold Ctrl/Cmd to select multiple files in the dialog
3. **Process**: Files will be processed automatically
4. **View Results**: All results appear in the table below

#### Managing Results
- **View Results**: All processed files appear in the results table
- **Export to CSV**: Click "Export to CSV" to save results
- **Clear Results**: Click "Clear Results" to start fresh

## File Types Supported

### Modern KiCad Files (v5+)
- **.kicad_pcb** - PCB layout files
- **.kicad_pro** - Project files  
- **.kicad_sch** - Schematic files

### Legacy KiCad Files (v4 and earlier)
- **.pro** - Legacy project files
- **.sch** - Legacy schematic files
- **.brd** - Legacy PCB files

## Understanding Results

### Result Columns
- **File Name**: Name of the processed file
- **Version**: Extracted version information
- **Type**: Type of KiCad file detected
- **Status**: Success or error message

### Version Information Types
- **Numeric Version** (e.g., "20221018"): KiCad 6+ format version
- **Date Format** (e.g., "22/05/2016 07:44:19"): Legacy update timestamp
- **Generator Info**: Tool that created the file

### File Types Detected
- **PCB Project**: PCB layout files
- **KiCad 6+ Format**: Modern KiCad files
- **Legacy Format**: Older KiCad files
- **Project File**: KiCad project configuration
- **Unknown KiCad File**: Recognized as KiCad but type unclear

## Command Line Usage

### Basic Usage
```bash
python kicad_version_extractor.py path/to/file.kicad_pcb
```

### Examples
```bash
# Process a PCB file
python kicad_version_extractor.py my_board.kicad_pcb

# Process a project file
python kicad_version_extractor.py my_project.kicad_pro

# Process a legacy file
python kicad_version_extractor.py old_project.pro
```

## Advanced Features

### Drag & Drop
- Drag single files onto the window for quick processing
- Drag multiple files for batch processing
- Works with files from Windows Explorer, macOS Finder, or Linux file managers

### Recent Files
- Last 10 processed files are remembered
- Access via dropdown menu
- Persists between application sessions

### Export Options
The CSV export includes:
- File name and full path
- Version information
- File type
- Processing status
- Timestamp of processing

### Configuration
Settings are saved in `kicad_extractor_config.json`:
- Recent files list
- Application preferences

## Troubleshooting

### Common Issues

**Application won't start:**
- Ensure Python 3.6+ is installed
- Run `install_dependencies.bat` to install required packages
- Check that tkinter is available (usually included with Python)

**"tkinterdnd2 not found" error:**
- Run: `pip install tkinterdnd2`
- Application will work without drag & drop if this fails

**Files not recognized:**
- Ensure files have correct extensions (.kicad_pcb, .kicad_pro, etc.)
- Check that files aren't corrupted
- Try opening files in KiCad to verify they're valid

**Version not found:**
- Some very old or corrupted files may not contain version info
- Check the "Status" column for specific error messages
- Try opening the file in a text editor to verify format

**Permission errors:**
- Ensure you have read access to the files
- Try copying files to a different location
- Run application as administrator if necessary (Windows)

### Performance Tips

**For Large Batch Operations:**
- Process files in smaller batches (50-100 files at a time)
- Close other applications to free up memory
- Use SSD storage for faster file access

**For Network Files:**
- Copy files locally before processing for better performance
- Network drives may cause timeout issues

## Integration with Other Tools

### Automation Scripts
The command-line interface allows integration with:
- Batch scripts (.bat, .sh)
- Build systems (Make, CMake)
- CI/CD pipelines
- File management tools

### Example Batch Script
```batch
@echo off
echo Processing all KiCad files in current directory...
for %%f in (*.kicad_pcb *.kicad_pro *.kicad_sch) do (
    echo Processing %%f
    python kicad_version_extractor.py "%%f"
)
pause
```

## Tips and Best Practices

### File Organization
- Keep KiCad files organized in project folders
- Use consistent naming conventions
- Backup files before processing large batches

### Version Management
- Document version information in project files
- Use the CSV export for project documentation
- Track version changes over time

### Workflow Integration
- Use recent files for frequently accessed projects
- Export results for project reports
- Integrate with version control systems

## Support and Updates

### Getting Help
- Check this usage guide first
- Review error messages in the Status column
- Verify file formats and permissions

### Feature Requests
- The application is designed to be extensible
- Additional file formats can be added
- Export formats can be expanded

### Known Limitations
- Very large files (>100MB) may process slowly
- Some exotic KiCad variants may not be recognized
- Network file access may be slower than local files
