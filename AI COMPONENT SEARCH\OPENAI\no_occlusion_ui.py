#!/usr/bin/env python3
"""
NO OCCLUSION UI - Guaranteed No Text Overlap
Simple, clean layout with proper spacing
"""

import tkinter as tk
from tkinter import ttk

class NoOcclusionUI:
    """UI with guaranteed no text occlusion"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("✅ NO OCCLUSION UI - Perfect Text Layout")
        self.root.geometry("1400x900")
        self.root.configure(bg='#ffffff')
        
        self.current_step = 1
        self.setup_ui()
    
    def setup_ui(self):
        """Setup UI with NO text occlusion"""
        
        # Simple, clean workflow steps
        self.setup_clean_workflow()
        
        # Main content
        self.main_content = tk.Frame(self.root, bg='#ffffff')
        self.main_content.pack(fill='both', expand=True, padx=30, pady=20)
        
        # Show demo
        self.show_step_content()
    
    def setup_clean_workflow(self):
        """Clean workflow with NO occlusion - guaranteed"""
        
        # Workflow container - plenty of space
        workflow_frame = tk.Frame(self.root, bg='#f8f9fa', height=140)
        workflow_frame.pack(fill='x', padx=30, pady=20)
        workflow_frame.pack_propagate(False)
        
        # Title
        title_frame = tk.Frame(workflow_frame, bg='#f8f9fa')
        title_frame.pack(fill='x', pady=10)
        
        tk.Label(title_frame, text="📋 Component Search Process", 
                font=('Arial', 16, 'bold'), bg='#f8f9fa', fg='#495057').pack()
        
        # Steps container - WIDE spacing
        steps_frame = tk.Frame(workflow_frame, bg='#f8f9fa')
        steps_frame.pack(expand=True, fill='x', pady=15)
        
        # Simple step data - SHORT text only
        steps = [
            ("1", "Search"),
            ("2", "Analyze"), 
            ("3", "Results"),
            ("4", "Details"),
            ("5", "Export")
        ]
        
        self.step_widgets = []
        
        # Create steps with LOTS of space between them
        for i, (num, title) in enumerate(steps):
            
            # Step container - WIDE
            step_container = tk.Frame(steps_frame, bg='#f8f9fa', width=200)
            step_container.pack(side='left', expand=True, padx=20)
            step_container.pack_propagate(False)
            
            # Step circle - larger
            circle_bg = '#007bff' if i+1 == self.current_step else '#28a745' if i+1 < self.current_step else '#e9ecef'
            text_color = 'white' if i+1 <= self.current_step else '#6c757d'
            
            circle = tk.Label(step_container, text=num, 
                             font=('Arial', 18, 'bold'),
                             bg=circle_bg, fg=text_color,
                             width=4, height=2, relief='solid', bd=0)
            circle.pack(pady=5)
            
            # Step title - SINGLE line, SHORT text
            title_color = '#007bff' if i+1 == self.current_step else '#28a745' if i+1 < self.current_step else '#6c757d'
            title_label = tk.Label(step_container, text=title, 
                                  font=('Arial', 12, 'bold'),
                                  bg='#f8f9fa', fg=title_color)
            title_label.pack(pady=5)
            
            self.step_widgets.append((circle, title_label))
            
            # Arrow between steps - SPACED OUT
            if i < len(steps) - 1:
                arrow_frame = tk.Frame(steps_frame, bg='#f8f9fa', width=50)
                arrow_frame.pack(side='left', pady=30)
                arrow_frame.pack_propagate(False)
                
                tk.Label(arrow_frame, text="→", font=('Arial', 24, 'bold'), 
                        bg='#f8f9fa', fg='#dee2e6').pack()
    
    def update_workflow_step(self, step):
        """Update workflow step indicator"""
        self.current_step = step
        
        for i, (circle, title_label) in enumerate(self.step_widgets):
            if i + 1 == step:
                # Current step - blue
                circle.config(bg='#007bff', fg='white')
                title_label.config(fg='#007bff')
            elif i + 1 < step:
                # Completed step - green
                circle.config(bg='#28a745', fg='white')
                title_label.config(fg='#28a745')
            else:
                # Future step - gray
                circle.config(bg='#e9ecef', fg='#6c757d')
                title_label.config(fg='#6c757d')
    
    def show_step_content(self):
        """Show content for current step"""
        
        # Clear content
        for widget in self.main_content.winfo_children():
            widget.destroy()
        
        # Step indicator
        step_info = tk.Frame(self.main_content, bg='#ffffff')
        step_info.pack(fill='x', pady=20)
        
        step_names = ["Search", "AI Analysis", "Results", "Details", "Export"]
        current_name = step_names[self.current_step - 1]
        
        tk.Label(step_info, text=f"Step {self.current_step}: {current_name}", 
                font=('Arial', 24, 'bold'), bg='#ffffff', fg='#007bff').pack(side='left')
        
        # Progress indicator
        progress_text = f"{self.current_step}/5 Complete"
        tk.Label(step_info, text=progress_text, 
                font=('Arial', 14), bg='#ffffff', fg='#6c757d').pack(side='right')
        
        # Content based on step
        if self.current_step == 1:
            self.show_search_content()
        elif self.current_step == 2:
            self.show_analysis_content()
        elif self.current_step == 3:
            self.show_results_content()
        elif self.current_step == 4:
            self.show_details_content()
        elif self.current_step == 5:
            self.show_export_content()
        
        # Navigation buttons
        self.show_navigation()
    
    def show_search_content(self):
        """Step 1: Search"""
        
        content_frame = tk.Frame(self.main_content, bg='#f8f9fa', relief='solid', bd=1)
        content_frame.pack(fill='both', expand=True, pady=20)
        
        # Large search area
        search_area = tk.Frame(content_frame, bg='#f8f9fa')
        search_area.pack(expand=True, pady=50)
        
        tk.Label(search_area, text="🔍 Enter Component Name", 
                font=('Arial', 20, 'bold'), bg='#f8f9fa').pack(pady=20)
        
        # Large search input
        self.search_var = tk.StringVar(value="10k resistor")
        search_entry = tk.Entry(search_area, textvariable=self.search_var, 
                               font=('Arial', 16), width=50, relief='solid', bd=2)
        search_entry.pack(pady=20, ipady=12)
        
        # Search type buttons
        button_frame = tk.Frame(search_area, bg='#f8f9fa')
        button_frame.pack(pady=30)
        
        tk.Button(button_frame, text="🤖 AI Search", 
                 command=lambda: self.go_to_step(2),
                 bg='#007bff', fg='white', font=('Arial', 14, 'bold'),
                 padx=40, pady=15).pack(side='left', padx=15)
        
        tk.Button(button_frame, text="⚡ Quick Search", 
                 command=lambda: self.go_to_step(3),
                 bg='#28a745', fg='white', font=('Arial', 14, 'bold'),
                 padx=40, pady=15).pack(side='left', padx=15)
    
    def show_analysis_content(self):
        """Step 2: AI Analysis"""
        
        content_frame = tk.Frame(self.main_content, bg='#f8f9fa', relief='solid', bd=1)
        content_frame.pack(fill='both', expand=True, pady=20)
        
        # Analysis display
        tk.Label(content_frame, text="🤖 AI Analysis Results", 
                font=('Arial', 18, 'bold'), bg='#f8f9fa', fg='#007bff').pack(pady=20)
        
        # Large text area for analysis
        analysis_text = tk.Text(content_frame, font=('Arial', 12), height=15,
                               bg='#ffffff', relief='flat', padx=30, pady=20)
        analysis_text.pack(fill='both', expand=True, padx=30, pady=20)
        
        analysis_content = """🤖 GEMINI AI ANALYSIS

COMPONENT: 10kΩ Resistor
TYPE: Carbon Film Resistor  
POWER: 1/4W (0.25W)
TOLERANCE: ±5%

SPECIFICATIONS:
• Resistance: 10,000 Ohms
• Power Rating: 0.25W
• Temperature Range: -55°C to +155°C
• Package: Through-hole

RECOMMENDED SEARCH:
"10k ohm resistor 1/4w carbon film"

TOP MANUFACTURERS:
• Vishay (premium)
• Yageo (cost effective)
• Panasonic (automotive)

CONFIDENCE: 95% ✅"""
        
        analysis_text.insert('1.0', analysis_content)
        analysis_text.config(state='disabled')
    
    def show_results_content(self):
        """Step 3: Results"""
        
        content_frame = tk.Frame(self.main_content, bg='#f8f9fa', relief='solid', bd=1)
        content_frame.pack(fill='both', expand=True, pady=20)
        
        tk.Label(content_frame, text="📊 Search Results", 
                font=('Arial', 18, 'bold'), bg='#f8f9fa', fg='#007bff').pack(pady=20)
        
        # Results table
        table_frame = tk.Frame(content_frame, bg='#f8f9fa')
        table_frame.pack(fill='both', expand=True, padx=30, pady=20)
        
        columns = ('Rank', 'Supplier', 'Component', 'Price', 'Quality')
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=10)
        
        # Configure columns
        widths = {'Rank': 60, 'Supplier': 150, 'Component': 300, 'Price': 80, 'Quality': 80}
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=widths[col])
        
        # Sample data
        results = [
            ("1", "Robu Electronics", "10kΩ Carbon Film Resistor 1/4W", "₹2.00", "A+"),
            ("2", "Probots", "10K Ohm Resistor Through Hole", "₹1.50", "A"),
            ("3", "Evelta", "Resistor 10kΩ 0.25W Carbon Film", "₹2.50", "A+"),
            ("4", "SunRom", "10000 Ohm Resistor 1/4 Watt", "₹1.80", "A"),
        ]
        
        for result in results:
            tree.insert('', 'end', values=result)
        
        tree.pack(fill='both', expand=True)
    
    def show_details_content(self):
        """Step 4: Details"""
        
        content_frame = tk.Frame(self.main_content, bg='#f8f9fa', relief='solid', bd=1)
        content_frame.pack(fill='both', expand=True, pady=20)
        
        tk.Label(content_frame, text="📄 Component Details", 
                font=('Arial', 18, 'bold'), bg='#f8f9fa', fg='#007bff').pack(pady=20)
        
        # Details display
        details_text = tk.Text(content_frame, font=('Arial', 12), height=15,
                              bg='#ffffff', relief='flat', padx=30, pady=20)
        details_text.pack(fill='both', expand=True, padx=30, pady=20)
        
        details_content = """📄 COMPONENT DATASHEET

VISHAY CARBON FILM RESISTOR
Part: CFR-25JB-52-10K

SPECIFICATIONS:
• Resistance: 10kΩ ±5%
• Power: 0.25W maximum
• Temperature: -55°C to +155°C
• Voltage: 250V maximum
• Size: 6.3mm x 2.3mm

APPLICATIONS:
✓ Pull-up resistors
✓ Voltage dividers  
✓ Current limiting
✓ General circuits

PRICING:
• 1-99 pcs: ₹2.50 each
• 100+ pcs: ₹1.80 each
• 1000+ pcs: ₹1.20 each

AVAILABILITY: ✅ In Stock"""
        
        details_text.insert('1.0', details_content)
        details_text.config(state='disabled')
    
    def show_export_content(self):
        """Step 5: Export"""
        
        content_frame = tk.Frame(self.main_content, bg='#d4edda', relief='solid', bd=1)
        content_frame.pack(fill='both', expand=True, pady=20)
        
        tk.Label(content_frame, text="✅ Ready for Export", 
                font=('Arial', 18, 'bold'), bg='#d4edda', fg='#155724').pack(pady=20)
        
        # Export summary
        summary_text = tk.Text(content_frame, font=('Arial', 12), height=12,
                              bg='#d4edda', relief='flat', padx=30, pady=20)
        summary_text.pack(fill='both', expand=True, padx=30, pady=20)
        
        summary_content = """🎯 SOURCING SUMMARY

SELECTED: 10kΩ Carbon Film Resistor

BEST OPTION:
🥇 Robu Electronics
   Price: ₹2.00 each
   Quality: A+ rating
   Stock: Available
   Shipping: ₹45

TOTAL COST: ₹47.00

STATUS:
✅ Component verified
✅ Supplier confirmed  
✅ Price validated
✅ Ready to order

CONFIDENCE: 95%"""
        
        summary_text.insert('1.0', summary_content)
        summary_text.config(state='disabled')
        
        # Export buttons
        export_frame = tk.Frame(content_frame, bg='#d4edda')
        export_frame.pack(fill='x', padx=30, pady=20)
        
        tk.Button(export_frame, text="📊 Export Excel", 
                 bg='#007bff', fg='white', font=('Arial', 12, 'bold'),
                 padx=25, pady=12).pack(side='left', padx=10)
        
        tk.Button(export_frame, text="🛒 Order Now", 
                 bg='#28a745', fg='white', font=('Arial', 12, 'bold'),
                 padx=25, pady=12).pack(side='left', padx=10)
    
    def show_navigation(self):
        """Show navigation buttons"""
        
        nav_frame = tk.Frame(self.main_content, bg='#ffffff')
        nav_frame.pack(fill='x', pady=20)
        
        # Previous button
        if self.current_step > 1:
            tk.Button(nav_frame, text=f"⬅️ Previous", 
                     command=lambda: self.go_to_step(self.current_step - 1),
                     bg='#6c757d', fg='white', font=('Arial', 12),
                     padx=20, pady=10).pack(side='left')
        
        # Next button  
        if self.current_step < 5:
            tk.Button(nav_frame, text=f"Next ➡️", 
                     command=lambda: self.go_to_step(self.current_step + 1),
                     bg='#007bff', fg='white', font=('Arial', 12, 'bold'),
                     padx=20, pady=10).pack(side='right')
        
        # New search button
        if self.current_step == 5:
            tk.Button(nav_frame, text="🔄 New Search", 
                     command=lambda: self.go_to_step(1),
                     bg='#28a745', fg='white', font=('Arial', 12, 'bold'),
                     padx=20, pady=10).pack(side='right')
    
    def go_to_step(self, step):
        """Navigate to specific step"""
        self.current_step = step
        self.update_workflow_step(step)
        self.show_step_content()
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

if __name__ == "__main__":
    print("✅ NO OCCLUSION UI")
    print("=" * 20)
    print("✅ Guaranteed no text overlap")
    print("✅ Simple, clean layout")
    print("✅ Wide spacing between elements")
    print("✅ Short, clear text only")
    print("✅ Professional appearance")
    
    app = NoOcclusionUI()
    app.run()
