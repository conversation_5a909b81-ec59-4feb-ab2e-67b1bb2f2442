#!/usr/bin/env python3
"""
Demo Web AI Simple
Demonstrates the simple web AI functionality for component analysis
"""

import sys
import time

def demo_simple_web_ai():
    """Demo the simple web AI functionality"""
    print("🌐 Simple Web AI Demo")
    print("=" * 30)
    print("This demo shows web-based AI component analysis")
    print("using standard Selenium (compatible with Python 3.12+)")
    print()
    
    try:
        from simple_web_ai import get_simple_web_ai, analyze_component_with_web_ai
        
        ai = get_simple_web_ai()
        print(f"✅ Simple Web AI initialized")
        print(f"📋 Available providers: {ai.get_available_providers()}")
        
        # Test component analysis
        test_components = [
            "arduino uno",
            "10k resistor",
            "LM358 op amp"
        ]
        
        print(f"\n🔍 Testing component analysis...")
        
        for component in test_components:
            print(f"\n📋 Analyzing: '{component}'")
            print("   🔄 Sending query to web AI...")
            
            try:
                result = analyze_component_with_web_ai(component)
                
                if result["success"]:
                    print(f"   ✅ Analysis successful!")
                    print(f"   🤖 Provider: {result['provider']}")
                    print(f"   📊 Confidence: {result['confidence']}")
                    print(f"   📝 Analysis:")
                    
                    # Show first few lines of analysis
                    analysis_lines = result["analysis"].split('\n')[:5]
                    for line in analysis_lines:
                        if line.strip():
                            print(f"      {line.strip()}")
                    
                    if len(result["analysis"].split('\n')) > 5:
                        print("      ...")
                else:
                    print(f"   ❌ Analysis failed: {result['error']}")
                    
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
            
            print("   ⏱️ Waiting before next query...")
            time.sleep(2)  # Be nice to the AI services
        
        # Close browser
        ai.close()
        print(f"\n🎉 Demo completed successfully!")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure selenium is installed: pip install selenium")
        return False
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return False

def demo_ai_analyzer_integration():
    """Demo AI analyzer with web AI integration"""
    print("\n\n🤖 AI Analyzer Integration Demo")
    print("=" * 40)
    
    try:
        from ai_analyzer import get_ai_analyzer
        
        analyzer = get_ai_analyzer()
        print(f"✅ AI Analyzer initialized")
        print(f"📊 Active provider: {analyzer.active_provider}")
        
        # Update configuration to use web providers
        print(f"\n⚙️ Configuring for web-based AI...")
        
        # Add web providers to configuration
        analyzer.config["providers"]["perplexity_simple"] = {
            "enabled": True,
            "name": "Perplexity AI (Simple)",
            "description": "Web-based AI via simple automation"
        }
        
        analyzer.config["providers"]["you_chat_simple"] = {
            "enabled": True,
            "name": "You.com Chat (Simple)",
            "description": "Web-based AI via simple automation"
        }
        
        # Set default to web provider
        analyzer.config["default_provider"] = "perplexity_simple"
        analyzer.active_provider = "perplexity_simple"
        
        print(f"✅ Configuration updated")
        print(f"📊 New active provider: {analyzer.active_provider}")
        
        # Test component analysis
        test_query = "arduino uno r3"
        print(f"\n🔍 Testing component analysis with: '{test_query}'")
        
        try:
            analysis = analyzer.analyze_component_query(test_query)
            
            print(f"✅ Analysis completed!")
            print(f"📋 Component Type: {analysis.component_type}")
            print(f"🏭 Manufacturer: {analysis.manufacturer or 'Unknown'}")
            print(f"📦 Package Options: {', '.join(analysis.package_options[:3])}")
            print(f"📊 Confidence: {analysis.confidence_score:.2f}")
            print(f"📝 Notes: {analysis.analysis_notes[:100]}...")
            
            if analysis.alternatives:
                print(f"🔄 Alternatives: {', '.join(analysis.alternatives[:3])}")
            
            return True
            
        except Exception as e:
            print(f"❌ Component analysis failed: {e}")
            return False
        
    except Exception as e:
        print(f"❌ AI Analyzer integration failed: {e}")
        return False

def show_web_ai_benefits():
    """Show benefits of web-based AI approach"""
    print("\n\n🌟 Web-based AI Benefits")
    print("=" * 30)
    
    benefits = [
        "🆓 Completely free - no API keys required",
        "🚀 No local installation - works immediately",
        "🔄 Always up-to-date - uses latest AI models",
        "💻 Low resource usage - no local GPU needed",
        "🌐 Multiple providers - fallback options available",
        "🔧 Easy setup - just needs Chrome browser",
        "📱 Cross-platform - works on Windows, Mac, Linux",
        "🎯 Specialized models - different AI for different tasks"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")
    
    print(f"\n📋 Supported Providers:")
    providers = [
        ("Perplexity AI", "Research-focused, great for technical queries"),
        ("You.com Chat", "General purpose, good for component analysis"),
        ("ChatGPT Free", "Advanced reasoning (requires account)"),
        ("Claude Free", "Excellent for technical analysis (requires account)"),
        ("DeepSeek", "Fast and efficient (when available)")
    ]
    
    for name, description in providers:
        print(f"  • {name}: {description}")

def main():
    """Run the web AI demo"""
    print("🚀 Web-based AI Component Analysis Demo")
    print("=" * 50)
    print("Demonstrating web automation for AI-powered component search")
    print("=" * 50)
    
    # Check if user wants to run browser demo
    print("\n⚠️ BROWSER DEMO WARNING:")
    print("This demo will open a Chrome browser and interact with AI websites.")
    print("Make sure you have Chrome installed and a stable internet connection.")
    print()
    
    response = input("Do you want to run the browser demo? (y/n): ").lower().strip()
    
    if response == 'y' or response == 'yes':
        print("\n🌐 Starting browser-based demo...")
        
        # Run demos
        demo1_success = demo_simple_web_ai()
        demo2_success = demo_ai_analyzer_integration()
        
        print("\n" + "=" * 50)
        print("📊 Demo Results:")
        print("=" * 50)
        
        if demo1_success:
            print("✅ Simple Web AI Demo: SUCCESS")
        else:
            print("❌ Simple Web AI Demo: FAILED")
        
        if demo2_success:
            print("✅ AI Analyzer Integration: SUCCESS")
        else:
            print("❌ AI Analyzer Integration: FAILED")
        
        if demo1_success or demo2_success:
            print("\n🎉 Web-based AI is working!")
            print("\n🚀 To use in the main application:")
            print("1. Run: python component_searcher.py")
            print("2. Click '🤖 AI Search' button")
            print("3. Enter component name (e.g., 'arduino uno')")
            print("4. Get AI-powered analysis and search results")
        else:
            print("\n⚠️ Web AI demos failed. Check your setup:")
            print("• Install Chrome browser")
            print("• Install selenium: pip install selenium")
            print("• Check internet connectivity")
            print("• Try running as administrator")
    else:
        print("\n📚 Skipping browser demo. Showing benefits instead...")
    
    # Always show benefits
    show_web_ai_benefits()
    
    print(f"\n💡 Key Advantage:")
    print(f"Web-based AI eliminates the need for:")
    print(f"• Local AI installation (Ollama)")
    print(f"• API key management")
    print(f"• GPU requirements")
    print(f"• Model downloads")
    print(f"")
    print(f"Just install Chrome and you're ready to go!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
    finally:
        print("\nPress Enter to exit...")
        input()
