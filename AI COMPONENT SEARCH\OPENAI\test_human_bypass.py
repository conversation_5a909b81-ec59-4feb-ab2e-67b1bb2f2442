#!/usr/bin/env python3
"""
Test Human Bypass for Web AI
Simple test to see if we can actually access AI websites and handle human verification
"""

import time
import sys
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class HumanBypassTester:
    """Test if we can bypass human detection on AI websites"""
    
    def __init__(self):
        self.driver = None
        self.human_intervention_needed = False
    
    def setup_human_like_browser(self):
        """Setup browser to look as human as possible"""
        try:
            options = Options()
            
            # Make browser look human
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # Add realistic user agent
            options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            # Disable automation indicators
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--no-sandbox")
            
            # Start browser
            self.driver = webdriver.Chrome(options=options)
            
            # Remove webdriver property
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Set realistic window size
            self.driver.set_window_size(1366, 768)
            
            print("✅ Browser setup complete - attempting to look human")
            return True
            
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            return False
    
    def test_deepseek_access(self):
        """Test if we can access DeepSeek without being blocked"""
        print("\n🧪 Testing DeepSeek Chat access...")
        
        try:
            url = "https://chat.deepseek.com/"
            print(f"📍 Navigating to: {url}")
            
            self.driver.get(url)
            time.sleep(3)  # Wait for page load
            
            # Check if we're blocked
            page_title = self.driver.title.lower()
            page_source = self.driver.page_source.lower()
            
            print(f"📄 Page title: {self.driver.title}")
            
            # Look for blocking indicators
            blocking_indicators = [
                "access denied", "blocked", "captcha", "verification", 
                "cloudflare", "bot detection", "security check"
            ]
            
            is_blocked = any(indicator in page_title or indicator in page_source for indicator in blocking_indicators)
            
            if is_blocked:
                print("❌ Appears to be blocked or requires verification")
                return False
            
            # Look for chat interface
            try:
                # Wait for input field to appear
                input_field = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "textarea"))
                )
                print("✅ Found chat input field")
                
                # Try to interact
                print("🤖 Attempting to send test message...")
                input_field.click()
                time.sleep(1)
                
                # Type very slowly like a human
                test_message = "Hello"
                for char in test_message:
                    input_field.send_keys(char)
                    time.sleep(0.2)  # Human-like typing speed
                
                print("✅ Successfully typed message")
                
                # Check if we can send (look for send button)
                try:
                    send_button = self.driver.find_element(By.XPATH, "//button[contains(@type, 'submit') or contains(text(), 'Send') or contains(@aria-label, 'Send')]")
                    print("✅ Found send button")
                    return True
                except NoSuchElementException:
                    print("⚠️ No send button found, might need Enter key")
                    return True  # Still consider success if we can type
                
            except TimeoutException:
                print("❌ No chat interface found - might be blocked")
                return False
                
        except Exception as e:
            print(f"❌ DeepSeek test failed: {e}")
            return False
    
    def test_perplexity_access(self):
        """Test if we can access Perplexity without being blocked"""
        print("\n🧪 Testing Perplexity AI access...")
        
        try:
            url = "https://www.perplexity.ai/"
            print(f"📍 Navigating to: {url}")
            
            self.driver.get(url)
            time.sleep(3)
            
            print(f"📄 Page title: {self.driver.title}")
            
            # Look for input field
            try:
                input_field = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "textarea"))
                )
                print("✅ Found input field")
                
                # Try to interact
                input_field.click()
                time.sleep(1)
                
                # Type slowly
                test_message = "test"
                for char in test_message:
                    input_field.send_keys(char)
                    time.sleep(0.3)
                
                print("✅ Successfully typed in Perplexity")
                return True
                
            except TimeoutException:
                print("❌ No input field found in Perplexity")
                return False
                
        except Exception as e:
            print(f"❌ Perplexity test failed: {e}")
            return False
    
    def test_you_chat_access(self):
        """Test if we can access You.com Chat"""
        print("\n🧪 Testing You.com Chat access...")
        
        try:
            url = "https://you.com/"
            print(f"📍 Navigating to: {url}")
            
            self.driver.get(url)
            time.sleep(3)
            
            print(f"📄 Page title: {self.driver.title}")
            
            # Look for search/chat input
            try:
                input_field = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='text'], textarea"))
                )
                print("✅ Found input field")
                
                input_field.click()
                time.sleep(1)
                
                # Type slowly
                test_message = "hello"
                for char in test_message:
                    input_field.send_keys(char)
                    time.sleep(0.25)
                
                print("✅ Successfully typed in You.com")
                return True
                
            except TimeoutException:
                print("❌ No input field found in You.com")
                return False
                
        except Exception as e:
            print(f"❌ You.com test failed: {e}")
            return False
    
    def wait_for_human_intervention(self, message):
        """Wait for human to handle verification"""
        print(f"\n🚨 HUMAN INTERVENTION NEEDED")
        print(f"📋 {message}")
        print(f"🖱️ Please handle any verification in the browser window")
        print(f"⌨️ Then press Enter here to continue...")
        
        input("Press Enter when ready to continue...")
        print("✅ Continuing after human intervention...")
    
    def interactive_test(self):
        """Interactive test with human assistance"""
        print("\n🤝 Interactive Test Mode")
        print("This will open browsers and let you see what happens")
        print("You can manually handle any human verification")
        
        # Test each site with human assistance
        sites_to_test = [
            ("DeepSeek", "https://chat.deepseek.com/", self.test_deepseek_access),
            ("Perplexity", "https://www.perplexity.ai/", self.test_perplexity_access),
            ("You.com", "https://you.com/", self.test_you_chat_access)
        ]
        
        results = {}
        
        for site_name, url, test_func in sites_to_test:
            print(f"\n{'='*50}")
            print(f"Testing {site_name}")
            print(f"{'='*50}")
            
            try:
                # Navigate to site
                self.driver.get(url)
                time.sleep(2)
                
                # Check if human intervention needed
                page_source = self.driver.page_source.lower()
                if any(word in page_source for word in ["captcha", "verification", "cloudflare", "blocked"]):
                    self.wait_for_human_intervention(f"Please complete verification for {site_name}")
                
                # Run the test
                success = test_func()
                results[site_name] = success
                
                if success:
                    print(f"✅ {site_name}: SUCCESS - Can access and interact")
                else:
                    print(f"❌ {site_name}: FAILED - Cannot access or interact")
                
                # Ask user for confirmation
                user_input = input(f"\nDid {site_name} work correctly? (y/n): ").lower().strip()
                if user_input == 'y':
                    results[site_name] = True
                    print(f"✅ User confirmed {site_name} works")
                else:
                    results[site_name] = False
                    print(f"❌ User confirmed {site_name} doesn't work")
                
            except Exception as e:
                print(f"❌ {site_name} test crashed: {e}")
                results[site_name] = False
        
        return results
    
    def close(self):
        """Close browser"""
        if self.driver:
            self.driver.quit()

def main():
    """Main test function"""
    print("🧪 Human Bypass Test for Web AI")
    print("=" * 40)
    print("Testing if we can actually access AI websites without being blocked")
    print()
    
    tester = HumanBypassTester()
    
    try:
        # Setup browser
        if not tester.setup_human_like_browser():
            print("❌ Failed to setup browser")
            return False
        
        print("\n🤖 AUTOMATED TESTS")
        print("=" * 20)
        
        # Run automated tests first
        deepseek_result = tester.test_deepseek_access()
        perplexity_result = tester.test_perplexity_access()
        you_result = tester.test_you_chat_access()
        
        print(f"\n📊 AUTOMATED TEST RESULTS:")
        print(f"DeepSeek: {'✅ SUCCESS' if deepseek_result else '❌ FAILED'}")
        print(f"Perplexity: {'✅ SUCCESS' if perplexity_result else '❌ FAILED'}")
        print(f"You.com: {'✅ SUCCESS' if you_result else '❌ FAILED'}")
        
        # If any failed, try interactive mode
        if not all([deepseek_result, perplexity_result, you_result]):
            print(f"\n⚠️ Some automated tests failed")
            print(f"🤝 Trying interactive mode with human assistance...")
            
            interactive_results = tester.interactive_test()
            
            print(f"\n📊 FINAL RESULTS:")
            for site, result in interactive_results.items():
                print(f"{site}: {'✅ SUCCESS' if result else '❌ FAILED'}")
            
            # Check if any work
            working_sites = [site for site, result in interactive_results.items() if result]
            
            if working_sites:
                print(f"\n🎉 SUCCESS! Working sites: {', '.join(working_sites)}")
                print(f"✅ We can proceed with web AI implementation")
                return True
            else:
                print(f"\n❌ FAILURE! No sites are accessible")
                print(f"🚫 Web AI approach won't work - all sites block automation")
                return False
        else:
            print(f"\n🎉 ALL AUTOMATED TESTS PASSED!")
            print(f"✅ Web AI should work without human intervention")
            return True
    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    finally:
        tester.close()

if __name__ == "__main__":
    try:
        success = main()
        
        if success:
            print(f"\n🚀 NEXT STEPS:")
            print(f"✅ Web AI is feasible")
            print(f"📝 I can now implement the full application")
            print(f"🤖 The AI component search will work")
        else:
            print(f"\n🛑 CONCLUSION:")
            print(f"❌ Web AI is not feasible with current approach")
            print(f"🔄 Need alternative strategy (API keys, local AI, etc.)")
            print(f"💡 Consider paid API services instead")
        
    except KeyboardInterrupt:
        print(f"\n⏹️ Test interrupted by user")
    
    input(f"\nPress Enter to exit...")
