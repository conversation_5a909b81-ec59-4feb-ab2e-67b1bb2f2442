#!/usr/bin/env python3
"""
ComponentAI Admin UI Launcher
Launch the admin web interface for ComponentAI
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from admin.web_server import ComponentAIWebServer

def main():
    """Launch the ComponentAI Admin UI"""
    print("🚀 Starting ComponentAI Admin UI...")
    print("=" * 50)
    
    try:
        # Initialize web server
        server = ComponentAIWebServer()
        
        print("✅ ComponentAI Admin UI started successfully!")
        print("🌐 Access the admin interface at: http://localhost:8080")
        print("📋 Available features:")
        print("   • Component Management")
        print("   • AI Research Lab") 
        print("   • Analytics Dashboard")
        print("   • System Information")
        print("=" * 50)
        print("Press Ctrl+C to stop the server")
        
        # Start server
        server.run()
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
