import os
import re
import time # For a small delay if output is too fast

def find_project_folders(root_dir_to_scan):
    # --- UPDATED Regular Expression ---
    project_folder_pattern = re.compile(r"^(P-[\w\d]+)[-\s_]+(.+)$")
    
    # --- Configuration for folders to EXPLICITLY SKIP ---
    EXPLICITLY_SKIP_FOLDERS = {"KiCadProjectBrowserTool", ".git", "node_modules", 
                               "$RECYCLE.BIN", "System Volume Information",
                               # Add any other specific top-level or common folders you want to always skip
                               }
    # --- End Configuration ---

    print(f"Starting scan for KiCad project folders (P-XXXX Description) in: {os.path.abspath(root_dir_to_scan)}\n")
    print(f"Using regex: r\"^{project_folder_pattern.pattern}$\"\n") # Show the regex being used
    
    found_projects = []
    processed_paths = set() 

    for dirpath, dirnames, filenames in os.walk(root_dir_to_scan, topdown=True, onerror=log_error):
        current_normalized_dirpath = os.path.normpath(dirpath)

        if current_normalized_dirpath in processed_paths:
            dirnames[:] = [] 
            continue
        processed_paths.add(current_normalized_dirpath)

        print(f"Scanning in: {current_normalized_dirpath}")
        # time.sleep(0.01) # Uncomment for a tiny delay

        original_dirnames = list(dirnames) 
        dirnames[:] = [d for d in dirnames if d not in EXPLICITLY_SKIP_FOLDERS and not d.startswith('.')] # Also skip hidden folders generally

        for current_folder_name in original_dirnames:
            if current_folder_name in EXPLICITLY_SKIP_FOLDERS or current_folder_name.startswith('.'):
                continue

            if project_folder_pattern.match(current_folder_name):
                project_folder_full_path = os.path.join(current_normalized_dirpath, current_folder_name)
                relative_project_path = os.path.relpath(project_folder_full_path, root_dir_to_scan)
                
                if relative_project_path not in found_projects:
                    print(f"  MATCH FOUND: {relative_project_path}")
                    found_projects.append(relative_project_path)
                
    print("\n----------------------------------------------")
    print("Scan complete.")
    
    if not found_projects:
        print("No project folders matching the 'P-XXXX Description' pattern were found.")
    else:
        found_projects.sort()
        print("\nFinal list of found project folders (relative paths):")
        for project_path in found_projects:
            print(project_path)
        print(f"\n----------------------------------------------")
        print(f"Found {len(found_projects)} project folder(s).")

    print("\nNote: The paths shown are relative to the directory scanned.")

def log_error(os_error):
    print(f"  [ERROR] Could not access: {os_error.filename} - {os_error.strerror}. Skipping.")

if __name__ == "__main__":
    master_folder_to_scan = "P:\\"
    find_project_folders(master_folder_to_scan)