#!/usr/bin/env python3
"""
Tabbed Workflow UI - Clickable Tabs + Next/Previous Buttons
Best of both worlds: direct tab access AND linear navigation
"""

import tkinter as tk
from tkinter import ttk

class TabbedWorkflowUI:
    """Workflow UI with clickable tabs AND next/previous buttons"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("📑 Tabbed Workflow UI - Clickable Steps")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#ffffff')
        self.root.state('zoomed')
        
        self.current_step = 1
        self.setup_ui()
    
    def setup_ui(self):
        """Setup tabbed workflow UI"""
        
        # Custom tab-like workflow header
        self.setup_clickable_workflow_tabs()
        
        # Main content area
        self.main_content = tk.Frame(self.root, bg='#ffffff')
        self.main_content.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Show current step content
        self.show_step_content()
    
    def setup_clickable_workflow_tabs(self):
        """Create clickable workflow tabs"""
        
        # Tab container
        tab_container = tk.Frame(self.root, bg='#f8f9fa', height=100)
        tab_container.pack(fill='x', padx=20, pady=10)
        tab_container.pack_propagate(False)
        
        # Title
        title_frame = tk.Frame(tab_container, bg='#f8f9fa')
        title_frame.pack(fill='x', pady=5)
        
        tk.Label(title_frame, text="📑 Component Search Workflow - Click Any Step", 
                font=('Arial', 14, 'bold'), bg='#f8f9fa', fg='#495057').pack()
        
        # Tabs frame
        tabs_frame = tk.Frame(tab_container, bg='#f8f9fa')
        tabs_frame.pack(expand=True, fill='x', pady=10)
        
        # Tab definitions
        self.tabs = [
            {"num": "1", "title": "🔍 Search", "desc": "Enter component", "step": 1},
            {"num": "2", "title": "🤖 AI Analysis", "desc": "Review insights", "step": 2},
            {"num": "3", "title": "📊 Results", "desc": "Compare suppliers", "step": 3},
            {"num": "4", "title": "📄 Details", "desc": "View datasheets", "step": 4},
            {"num": "5", "title": "✅ Decision", "desc": "Select & export", "step": 5}
        ]
        
        self.tab_widgets = []
        
        # Create enhanced clickable tabs
        for i, tab in enumerate(self.tabs):

            # Enhanced tab frame - rounded corners effect with padding
            tab_outer = tk.Frame(tabs_frame, bg='#f8f9fa')
            tab_outer.pack(side='left', expand=True, fill='both', padx=8, pady=5)

            tab_frame = tk.Frame(tab_outer, bg='#ffffff', relief='raised', bd=1, cursor='hand2')
            tab_frame.pack(fill='both', expand=True, padx=2, pady=2)

            # Make entire tab clickable
            tab_frame.bind('<Button-1>', lambda e, step=tab['step']: self.go_to_step(step))

            # Enhanced number circle - larger and more prominent
            circle_container = tk.Frame(tab_frame, bg='#ffffff')
            circle_container.pack(pady=12)
            circle_container.bind('<Button-1>', lambda e, step=tab['step']: self.go_to_step(step))

            # Create circular number with better styling
            circle = tk.Label(circle_container, text=tab['num'],
                             font=('Arial', 16, 'bold'),
                             bg='#e9ecef', fg='#6c757d',
                             width=4, height=2, relief='flat', bd=0)
            circle.pack()
            circle.bind('<Button-1>', lambda e, step=tab['step']: self.go_to_step(step))

            # Add subtle shadow effect
            shadow = tk.Frame(circle_container, bg='#dee2e6', height=2)
            shadow.pack(fill='x', padx=8)
            shadow.bind('<Button-1>', lambda e, step=tab['step']: self.go_to_step(step))

            # Enhanced tab title with better spacing
            title_label = tk.Label(tab_frame, text=tab['title'],
                                  font=('Arial', 11, 'bold'),
                                  bg='#ffffff', fg='#6c757d')
            title_label.pack(pady=(8, 4))
            title_label.bind('<Button-1>', lambda e, step=tab['step']: self.go_to_step(step))

            # Enhanced tab description
            desc_label = tk.Label(tab_frame, text=tab['desc'],
                                 font=('Arial', 9),
                                 bg='#ffffff', fg='#6c757d')
            desc_label.pack(pady=(0, 12))
            desc_label.bind('<Button-1>', lambda e, step=tab['step']: self.go_to_step(step))
            
            self.tab_widgets.append({
                'outer': tab_outer,
                'frame': tab_frame,
                'circle': circle,
                'shadow': shadow,
                'title': title_label,
                'desc': desc_label
            })
            
            # Arrow between tabs
            if i < len(self.tabs) - 1:
                arrow_frame = tk.Frame(tabs_frame, bg='#f8f9fa', width=30)
                arrow_frame.pack(side='left', fill='y', pady=20)
                arrow_frame.pack_propagate(False)
                
                tk.Label(arrow_frame, text="→", font=('Arial', 16), 
                        bg='#f8f9fa', fg='#dee2e6').pack(expand=True)
        
        # Update initial tab appearance
        self.update_tab_appearance()
    
    def update_tab_appearance(self):
        """Update enhanced tab appearance based on current step"""

        for i, tab_widget in enumerate(self.tab_widgets):
            step_num = i + 1

            if step_num == self.current_step:
                # Current step - ACTIVE TAB (Blue theme)
                tab_widget['outer'].config(bg='#007bff')
                tab_widget['frame'].config(bg='#007bff', relief='raised', bd=2)
                tab_widget['circle'].config(bg='#ffffff', fg='#007bff', font=('Arial', 18, 'bold'))
                tab_widget['shadow'].config(bg='#0056b3')
                tab_widget['title'].config(bg='#007bff', fg='#ffffff', font=('Arial', 12, 'bold'))
                tab_widget['desc'].config(bg='#007bff', fg='#e3f2fd')

            elif step_num < self.current_step:
                # Completed step - GREEN TAB (Success theme)
                tab_widget['outer'].config(bg='#28a745')
                tab_widget['frame'].config(bg='#28a745', relief='raised', bd=2)
                tab_widget['circle'].config(bg='#ffffff', fg='#28a745', font=('Arial', 16, 'bold'))
                tab_widget['shadow'].config(bg='#1e7e34')
                tab_widget['title'].config(bg='#28a745', fg='#ffffff', font=('Arial', 11, 'bold'))
                tab_widget['desc'].config(bg='#28a745', fg='#d4edda')

            else:
                # Future step - ELEGANT GRAY TAB (but clickable)
                tab_widget['outer'].config(bg='#f8f9fa')
                tab_widget['frame'].config(bg='#ffffff', relief='raised', bd=1)
                tab_widget['circle'].config(bg='#e9ecef', fg='#6c757d', font=('Arial', 16, 'bold'))
                tab_widget['shadow'].config(bg='#dee2e6')
                tab_widget['title'].config(bg='#ffffff', fg='#6c757d', font=('Arial', 11, 'bold'))
                tab_widget['desc'].config(bg='#ffffff', fg='#6c757d')
    
    def show_step_content(self):
        """Show content for current step"""
        
        # Clear content
        for widget in self.main_content.winfo_children():
            widget.destroy()
        
        # Step header with navigation info
        header_frame = tk.Frame(self.main_content, bg='#ffffff')
        header_frame.pack(fill='x', pady=20)
        
        step_names = ["🔍 Component Search", "🤖 AI Analysis", "📊 Search Results", "📄 Component Details", "✅ Final Decision"]
        current_name = step_names[self.current_step - 1]
        
        tk.Label(header_frame, text=current_name, 
                font=('Arial', 24, 'bold'), bg='#ffffff', fg='#007bff').pack(side='left')
        
        # Navigation hint
        nav_hint = "💡 Click any step above to jump directly, or use Next/Previous buttons"
        tk.Label(header_frame, text=nav_hint, 
                font=('Arial', 10), bg='#ffffff', fg='#6c757d').pack(side='right')
        
        # Content based on step
        if self.current_step == 1:
            self.show_search_content()
        elif self.current_step == 2:
            self.show_analysis_content()
        elif self.current_step == 3:
            self.show_results_content()
        elif self.current_step == 4:
            self.show_details_content()
        elif self.current_step == 5:
            self.show_decision_content()
        
        # Navigation buttons (ALWAYS show both options)
        self.show_navigation_buttons()
    
    def show_search_content(self):
        """Step 1: Search"""
        
        search_container = tk.Frame(self.main_content, bg='#f8f9fa', relief='solid', bd=1)
        search_container.pack(fill='both', expand=True, pady=20)
        
        # Large search area
        center_frame = tk.Frame(search_container, bg='#f8f9fa')
        center_frame.pack(expand=True, pady=50)
        
        tk.Label(center_frame, text="What component are you looking for?", 
                font=('Arial', 20, 'bold'), bg='#f8f9fa').pack(pady=30)
        
        # Search input
        search_frame = tk.Frame(center_frame, bg='#f8f9fa')
        search_frame.pack(pady=20)
        
        self.search_var = tk.StringVar(value="10k resistor")
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, 
                               font=('Arial', 16), width=50, relief='solid', bd=2)
        search_entry.pack(pady=10, ipady=8)
        
        # Quick suggestions
        suggestions_frame = tk.Frame(center_frame, bg='#f8f9fa')
        suggestions_frame.pack(pady=20)
        
        tk.Label(suggestions_frame, text="💡 Quick suggestions:", 
                font=('Arial', 12, 'bold'), bg='#f8f9fa').pack()
        
        suggestions = ["10k resistor", "Arduino Uno", "ESP32", "100uF capacitor"]
        
        button_row = tk.Frame(suggestions_frame, bg='#f8f9fa')
        button_row.pack(pady=10)
        
        for suggestion in suggestions:
            btn = tk.Button(button_row, text=suggestion, 
                           command=lambda s=suggestion: self.use_suggestion(s),
                           bg='#ffffff', fg='#495057', font=('Arial', 10),
                           relief='solid', bd=1, padx=15, pady=5)
            btn.pack(side='left', padx=5)
        
        # Search type buttons
        button_frame = tk.Frame(center_frame, bg='#f8f9fa')
        button_frame.pack(pady=30)
        
        tk.Button(button_frame, text="🤖 AI-Powered Search", 
                 command=lambda: self.go_to_step(2),
                 bg='#007bff', fg='white', font=('Arial', 14, 'bold'),
                 padx=30, pady=15).pack(side='left', padx=10)
        
        tk.Button(button_frame, text="⚡ Quick Search", 
                 command=lambda: self.go_to_step(3),
                 bg='#28a745', fg='white', font=('Arial', 14, 'bold'),
                 padx=30, pady=15).pack(side='left', padx=10)
    
    def show_analysis_content(self):
        """Step 2: AI Analysis"""
        
        analysis_container = tk.Frame(self.main_content, bg='#f8f9fa', relief='solid', bd=1)
        analysis_container.pack(fill='both', expand=True, pady=20)
        
        tk.Label(analysis_container, text="🤖 Gemini AI Analysis in Progress", 
                font=('Arial', 18, 'bold'), bg='#f8f9fa', fg='#007bff').pack(pady=20)
        
        # Large analysis display
        analysis_text = tk.Text(analysis_container, font=('Arial', 12), height=18,
                               bg='#ffffff', relief='flat', padx=30, pady=20)
        analysis_text.pack(fill='both', expand=True, padx=30, pady=20)
        
        analysis_content = """🤖 GEMINI AI ANALYSIS COMPLETE

COMPONENT IDENTIFICATION:
✅ Type: Electronic Resistor
✅ Value: 10,000 Ohms (10kΩ)
✅ Tolerance: ±5% (standard)
✅ Power Rating: 1/4W (0.25W)
✅ Package: Through-hole preferred

DETAILED SPECIFICATIONS:
• Resistance: 10kΩ ±5%
• Power: 1/4W maximum
• Temperature Coefficient: ±200 ppm/°C
• Voltage Rating: 250V
• Operating Temperature: -55°C to +155°C

OPTIMIZED SEARCH TERMS:
🎯 Primary: "10k ohm resistor 1/4w carbon film"
🎯 Alternative: "10000 ohm resistor through hole"
🎯 Specific: "10kΩ ±5% 0.25W resistor"

PACKAGE RECOMMENDATIONS:
📦 Through-hole (best for prototyping)
📦 SMD 0805 (for PCB assembly)
📦 SMD 1206 (easier hand soldering)

TOP MANUFACTURERS:
🏭 Vishay (premium quality, automotive grade)
🏭 Yageo (cost effective, reliable)
🏭 Panasonic (high precision, low noise)
🏭 Bourns (specialty applications)

PRICE EXPECTATIONS:
💰 Individual: ₹1-5 per piece
💰 Bulk (100+): ₹0.50-2 per piece
💰 Premium: ₹2-8 per piece

AI CONFIDENCE: 95% ✅
READY FOR SEARCH: ✅"""
        
        analysis_text.insert('1.0', analysis_content)
        analysis_text.config(state='disabled')
    
    def show_results_content(self):
        """Step 3: Results"""
        
        results_container = tk.Frame(self.main_content, bg='#ffffff')
        results_container.pack(fill='both', expand=True)
        
        # Results header
        header_frame = tk.Frame(results_container, bg='#ffffff')
        header_frame.pack(fill='x', pady=20)
        
        tk.Label(header_frame, text="📊 Search Results with AI Quality Scoring", 
                font=('Arial', 18, 'bold'), bg='#ffffff').pack(side='left')
        
        quality_label = tk.Label(header_frame, text="Quality: A+ | Results: 8 | AI Enhanced: 6", 
                                font=('Arial', 12), bg='#ffffff', fg='#28a745')
        quality_label.pack(side='right')
        
        # Large results table
        table_frame = tk.Frame(results_container, bg='#ffffff')
        table_frame.pack(fill='both', expand=True, padx=20)
        
        columns = ('Rank', 'Supplier', 'Component', 'Price', 'Stock', 'AI Score', 'Total')
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        widths = {'Rank': 60, 'Supplier': 150, 'Component': 350, 'Price': 80, 'Stock': 120, 'AI Score': 80, 'Total': 100}
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=widths[col])
        
        # Sample results with AI scoring
        results = [
            ("1", "Robu Electronics", "10kΩ Carbon Film Resistor 1/4W ±5%", "₹2.00", "500+ units", "95%", "₹47"),
            ("2", "Probots", "10K Ohm Resistor Through Hole", "₹1.50", "200+ units", "88%", "₹51.5"),
            ("3", "Evelta", "Resistor 10kΩ 0.25W Carbon Film", "₹2.50", "100+ units", "92%", "₹42.5"),
            ("4", "SunRom", "10000 Ohm Resistor 1/4 Watt", "₹1.80", "300+ units", "85%", "₹56.8"),
            ("5", "Electronix", "10K Carbon Film Resistor", "₹1.20", "150+ units", "78%", "₹51.2"),
        ]
        
        # Color coding based on AI score
        tree.tag_configure('excellent', background='#d4edda')  # 90%+
        tree.tag_configure('good', background='#fff3cd')      # 80-89%
        tree.tag_configure('average', background='#f8f9fa')   # <80%
        
        for result in results:
            ai_score = int(result[5].replace('%', ''))
            if ai_score >= 90:
                tag = 'excellent'
            elif ai_score >= 80:
                tag = 'good'
            else:
                tag = 'average'
            tree.insert('', 'end', values=result, tags=(tag,))
        
        tree.pack(fill='both', expand=True)
    
    def show_details_content(self):
        """Step 4: Details"""
        
        details_container = tk.Frame(self.main_content, bg='#ffffff')
        details_container.pack(fill='both', expand=True)
        
        tk.Label(details_container, text="📄 Component Details & Datasheets", 
                font=('Arial', 18, 'bold'), bg='#ffffff').pack(pady=20)
        
        # Content layout
        content_frame = tk.Frame(details_container, bg='#ffffff')
        content_frame.pack(fill='both', expand=True, padx=20)
        
        # Left panel - datasheet list
        left_frame = tk.Frame(content_frame, bg='#f8f9fa', relief='solid', bd=1, width=300)
        left_frame.pack(side='left', fill='y', padx=(0,10))
        left_frame.pack_propagate(False)
        
        tk.Label(left_frame, text="📋 Available Datasheets", 
                font=('Arial', 14, 'bold'), bg='#f8f9fa').pack(pady=15)
        
        datasheets = [
            "📄 Vishay Carbon Film Resistor",
            "📄 Yageo General Purpose Resistor", 
            "📄 Panasonic Resistor Selection Guide",
            "📄 Resistor Color Code Chart",
            "📄 Power Rating Guidelines"
        ]
        
        for ds in datasheets:
            btn = tk.Button(left_frame, text=ds, anchor='w',
                           bg='#ffffff', relief='solid', bd=1,
                           font=('Arial', 10), padx=15, pady=8)
            btn.pack(fill='x', padx=10, pady=2)
        
        # Right panel - datasheet viewer
        right_frame = tk.Frame(content_frame, bg='#ffffff', relief='solid', bd=1)
        right_frame.pack(side='right', fill='both', expand=True)
        
        # Large datasheet display
        datasheet_text = tk.Text(right_frame, font=('Arial', 11), bg='#ffffff',
                                relief='flat', padx=30, pady=20)
        datasheet_text.pack(fill='both', expand=True)
        
        datasheet_content = """📄 VISHAY CARBON FILM RESISTOR DATASHEET

PART NUMBER: CFR-25JB-52-10K
RESISTANCE: 10,000 Ohms (10kΩ)
TOLERANCE: ±5%
POWER RATING: 0.25W (1/4W)

DETAILED SPECIFICATIONS:
• Temperature Coefficient: ±200 ppm/°C
• Operating Temperature: -55°C to +155°C
• Maximum Voltage: 250V
• Noise: <0.2µV/V
• Lead Diameter: 0.6mm
• Body Length: 6.3mm
• Body Diameter: 2.3mm
• Lead Spacing: 0.4 inch (10.16mm)

APPLICATIONS:
✓ General purpose applications
✓ Voltage dividers for signal conditioning
✓ Pull-up/pull-down resistors in digital circuits
✓ Current limiting for LEDs
✓ Timing circuits and RC networks
✓ Biasing circuits in amplifiers

ORDERING INFORMATION:
CFR-25JB-52-10K (Tape & Reel)
CFR-25JB-52-10KTR (Cut Tape)
Minimum Order: 100 pieces

PRICING (India Market):
1-99 pieces: ₹2.50 each
100-999 pieces: ₹1.80 each
1000-4999 pieces: ₹1.20 each
5000+ pieces: ₹0.95 each

AVAILABILITY & SHIPPING:
✅ In Stock - Ships within 24 hours
✅ Local distributor: Robu Electronics, Mumbai
✅ Express delivery available
✅ Bulk discounts for 1000+ pieces

QUALITY CERTIFICATIONS:
• RoHS Compliant
• AEC-Q200 Qualified
• ISO 9001:2015 Certified
• Lead-free construction"""
        
        datasheet_text.insert('1.0', datasheet_content)
        datasheet_text.config(state='disabled')
    
    def show_decision_content(self):
        """Step 5: Decision"""
        
        decision_container = tk.Frame(self.main_content, bg='#ffffff')
        decision_container.pack(fill='both', expand=True)
        
        tk.Label(decision_container, text="✅ Final Decision & Export Options", 
                font=('Arial', 18, 'bold'), bg='#ffffff').pack(pady=20)
        
        # Decision summary
        summary_frame = tk.Frame(decision_container, bg='#d4edda', relief='solid', bd=1)
        summary_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        summary_text = tk.Text(summary_frame, font=('Arial', 12), bg='#d4edda',
                              relief='flat', padx=30, pady=30)
        summary_text.pack(fill='both', expand=True)
        
        summary_content = """🎯 COMPONENT SOURCING DECISION SUMMARY

SELECTED COMPONENT: 10kΩ Carbon Film Resistor 1/4W ±5%

🥇 TOP RECOMMENDATION:
Supplier: Robu Electronics
Price: ₹2.00 each
Shipping: ₹45
Total Cost: ₹47.00
Quality Score: 95% (A+)
Stock: 500+ units available
Delivery: 24 hours (Mumbai)
Confidence: Very High ✅

🥈 ALTERNATIVE OPTIONS:
Evelta: ₹2.50 + ₹40 shipping = ₹42.50 (A+ quality)
Probots: ₹1.50 + ₹50 shipping = ₹51.50 (A quality)
SunRom: ₹1.80 + ₹55 shipping = ₹56.80 (A quality)

📊 AI ANALYSIS SUMMARY:
✅ Component correctly identified and verified
✅ Specifications match requirements perfectly
✅ Price range validated (₹1-3 expected, ₹2 actual)
✅ Quality suppliers identified and verified
✅ Datasheets reviewed and specifications confirmed
✅ Stock availability confirmed across multiple suppliers

📋 PROCUREMENT READINESS:
✅ Technical specifications verified
✅ Supplier reliability confirmed
✅ Pricing competitive and reasonable
✅ Delivery timeline acceptable
✅ Quality certifications in place

🚀 READY FOR PROCUREMENT!

EXPORT & ACTION OPTIONS:
📊 Detailed comparison spreadsheet with all suppliers
📋 Professional procurement report with specifications
📄 Supplier contact information and direct links
🔗 Direct purchase links for immediate ordering
📧 Email summary for team sharing"""
        
        summary_text.insert('1.0', summary_content)
        summary_text.config(state='disabled')
        
        # Export action buttons
        export_frame = tk.Frame(decision_container, bg='#ffffff')
        export_frame.pack(fill='x', pady=20, padx=20)
        
        export_buttons = [
            ("📊 Export to Excel", '#007bff'),
            ("📋 Generate Report", '#28a745'),
            ("🛒 Open Purchase Links", '#ffc107'),
            ("📧 Email Summary", '#17a2b8'),
            ("🔄 Start New Search", '#6c757d')
        ]
        
        for text, color in export_buttons:
            tk.Button(export_frame, text=text, 
                     bg=color, fg='white' if color != '#ffc107' else 'black',
                     font=('Arial', 11, 'bold'), padx=20, pady=10).pack(side='left', padx=8)
    
    def show_navigation_buttons(self):
        """Show next/previous navigation buttons"""
        
        nav_frame = tk.Frame(self.main_content, bg='#ffffff', relief='solid', bd=1)
        nav_frame.pack(fill='x', pady=20)
        
        # Navigation info
        nav_info_frame = tk.Frame(nav_frame, bg='#f8f9fa')
        nav_info_frame.pack(fill='x', pady=10)
        
        tk.Label(nav_info_frame, text="🧭 Navigation Options:", 
                font=('Arial', 12, 'bold'), bg='#f8f9fa').pack(side='left', padx=20)
        
        tk.Label(nav_info_frame, text="Click any step tab above OR use buttons below", 
                font=('Arial', 10), bg='#f8f9fa', fg='#6c757d').pack(side='left', padx=10)
        
        # Button frame
        button_frame = tk.Frame(nav_frame, bg='#ffffff')
        button_frame.pack(fill='x', pady=15, padx=20)
        
        # Previous button
        if self.current_step > 1:
            prev_step_name = self.tabs[self.current_step - 2]['title']
            tk.Button(button_frame, text=f"⬅️ Previous: {prev_step_name}", 
                     command=lambda: self.go_to_step(self.current_step - 1),
                     bg='#6c757d', fg='white', font=('Arial', 12),
                     padx=20, pady=12).pack(side='left')
        
        # Step indicator
        step_indicator = tk.Label(button_frame, text=f"Step {self.current_step} of 5", 
                                 font=('Arial', 12, 'bold'), bg='#ffffff', fg='#007bff')
        step_indicator.pack(side='left', expand=True)
        
        # Next button
        if self.current_step < 5:
            next_step_name = self.tabs[self.current_step]['title']
            tk.Button(button_frame, text=f"Next: {next_step_name} ➡️", 
                     command=lambda: self.go_to_step(self.current_step + 1),
                     bg='#007bff', fg='white', font=('Arial', 12, 'bold'),
                     padx=20, pady=12).pack(side='right')
        else:
            # Final step - new search button
            tk.Button(button_frame, text="🔄 Start New Search", 
                     command=lambda: self.go_to_step(1),
                     bg='#28a745', fg='white', font=('Arial', 12, 'bold'),
                     padx=20, pady=12).pack(side='right')
    
    def use_suggestion(self, suggestion):
        """Use search suggestion"""
        self.search_var.set(suggestion)
    
    def go_to_step(self, step):
        """Navigate to specific step (from tab click OR button click)"""
        self.current_step = step
        self.update_tab_appearance()
        self.show_step_content()
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

if __name__ == "__main__":
    print("📑 Tabbed Workflow UI")
    print("=" * 25)
    print("✅ Clickable workflow tabs - jump to any step")
    print("✅ Next/Previous buttons - linear navigation")
    print("✅ Visual tab states - current, completed, future")
    print("✅ Large content areas - no cramped spaces")
    print("✅ Professional tab-like appearance")
    print("✅ Best of both navigation worlds!")
    
    app = TabbedWorkflowUI()
    app.run()
