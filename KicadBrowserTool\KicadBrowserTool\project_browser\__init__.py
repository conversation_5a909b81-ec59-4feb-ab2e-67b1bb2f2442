# P:\KiCadProjectBrowserTool\project_browser\__init__.py
import os
from flask import Flask
from flask_cors import CORS
from .config import TOOL_ROOT_DIR # To locate templates and static folder

# ==========================================================================
# MODIFIED Flask App Initialization
# ==========================================================================
app = Flask(
    __name__,
    template_folder=os.path.join(TOOL_ROOT_DIR, 'templates'),
    # Explicitly define the static folder relative to TOOL_ROOT_DIR
    # TOOL_ROOT_DIR is P:\KiCadProjectBrowserTool
    static_folder=os.path.join(TOOL_ROOT_DIR, 'static'),
    # static_url_path defines the URL prefix for static files.
    # If you use '/static', then your custom route for '/static/...' in routes.py
    # might still be overridden by Flask's default handling if not careful.
    # Let's keep '/static' for now and see if our explicit route is used.
    # If our explicit route is still not hit, we might rename this to something else
    # like '/assets' and update index.html and routes.py accordingly.
    static_url_path='/static'
)
# ==========================================================================
# END OF MODIFIED Flask App Initialization
# ==========================================================================

CORS(app)

# Import routes after the app is created
from . import routes

print(f"Flask app initialized in project_browser package.")
print(f"  Template folder: {app.template_folder}")
print(f"  Static folder: {app.static_folder}")
print(f"  Static URL path: {app.static_url_path}")