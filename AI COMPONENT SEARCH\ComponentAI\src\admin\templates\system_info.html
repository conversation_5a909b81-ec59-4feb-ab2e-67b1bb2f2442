{% extends "base.html" %}

{% block title %}System Information - ComponentAI Admin{% endblock %}
{% block page_title %}System Information{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h6 class="m-0"><i class="bi bi-info-circle"></i> Version Information</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <th>ComponentAI Version:</th>
                        <td><span class="badge bg-success">{{ info.componentai_version }}</span></td>
                    </tr>
                    <tr>
                        <th>Build Date:</th>
                        <td>{{ info.build_date }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="m-0"><i class="bi bi-puzzle"></i> Component Versions</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    {% for component, version in info.components.items() %}
                    <tr>
                        <th>{{ component.replace('_', ' ').title() }}:</th>
                        <td><span class="badge bg-success">{{ version }}</span></td>
                    </tr>
                    {% endfor %}
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h6 class="m-0"><i class="bi bi-heart-pulse"></i> System Health Check</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="p-3">
                            <i class="bi bi-server display-4 text-success"></i>
                            <h6 class="mt-2">MCP Server</h6>
                            <span class="badge bg-success">Operational</span>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="p-3">
                            <i class="bi bi-globe display-4 text-success"></i>
                            <h6 class="mt-2">Admin UI</h6>
                            <span class="badge bg-success">Running</span>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="p-3">
                            <i class="bi bi-database display-4 text-success"></i>
                            <h6 class="mt-2">Database</h6>
                            <span class="badge bg-success">Connected</span>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="p-3">
                            <i class="bi bi-robot display-4 text-success"></i>
                            <h6 class="mt-2">AI Integration</h6>
                            <span class="badge bg-success">Active</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="m-0"><i class="bi bi-clock-history"></i> Changelog</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6><span class="badge bg-primary">v1.0.0</span> - December 8, 2024</h6>
                    <ul>
                        <li>✅ Initial release of ComponentAI</li>
                        <li>✅ MCP Server integration with Claude Desktop</li>
                        <li>✅ Admin UI with component management</li>
                        <li>✅ AI Research Lab with transparent process</li>
                        <li>✅ System information and version tracking</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
