#!/usr/bin/env python3
"""
Workflow-Driven UI Design
Intuitive step-by-step layout that guides users naturally
"""

import tkinter as tk
from tkinter import ttk
import threading

class WorkflowDrivenUI:
    """Intuitive workflow-driven interface with proper space utilization"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 Workflow-Driven Component Search")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#ffffff')
        self.root.state('zoomed')  # Maximize on Windows
        
        self.current_step = 1
        self.search_results = []
        self.ai_analysis = None
        
        self.setup_workflow_ui()
    
    def setup_workflow_ui(self):
        """Setup intuitive workflow-driven interface"""
        
        # Top workflow indicator
        self.setup_workflow_steps()
        
        # Main content area - single large panel that changes based on step
        self.main_content = tk.Frame(self.root, bg='#ffffff')
        self.main_content.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Start with step 1
        self.show_step_1_search()
    
    def setup_workflow_steps(self):
        """Visual workflow steps indicator with proper text spacing"""

        workflow_frame = tk.Frame(self.root, bg='#f8f9fa', height=100)  # Increased height
        workflow_frame.pack(fill='x', padx=20, pady=10)
        workflow_frame.pack_propagate(False)

        # Workflow steps container
        steps_container = tk.Frame(workflow_frame, bg='#f8f9fa')
        steps_container.pack(expand=True, fill='both')

        # Calculate proper spacing for 5 steps - responsive design
        self.root.update_idletasks()  # Ensure window is rendered
        window_width = max(self.root.winfo_width(), 1200)  # Minimum width
        available_width = window_width - 100  # Account for padding
        step_width = max(available_width // 5, 180)  # Minimum step width

        self.step_labels = []
        steps = [
            ("1", "🔍 Search", "Enter component"),
            ("2", "🤖 AI Analysis", "Review insights"),
            ("3", "📊 Results", "Compare options"),
            ("4", "📄 Details", "View specs"),
            ("5", "✅ Decision", "Select & export")
        ]

        for i, (num, title, desc) in enumerate(steps):
            # Fixed-width step frame to prevent overlap
            step_frame = tk.Frame(steps_container, bg='#f8f9fa', width=step_width)
            step_frame.pack(side='left', fill='y')
            step_frame.pack_propagate(False)

            # Step circle - centered
            circle_container = tk.Frame(step_frame, bg='#f8f9fa')
            circle_container.pack(pady=10)

            circle_frame = tk.Frame(circle_container,
                                   bg='#e9ecef' if i+1 != self.current_step else '#007bff',
                                   width=50, height=50)
            circle_frame.pack()
            circle_frame.pack_propagate(False)

            step_label = tk.Label(circle_frame, text=num,
                                 font=('Arial', 16, 'bold'),
                                 fg='#6c757d' if i+1 != self.current_step else 'white',
                                 bg='#e9ecef' if i+1 != self.current_step else '#007bff')
            step_label.pack(expand=True)

            # Step title and description - centered with proper spacing
            title_label = tk.Label(step_frame, text=title, font=('Arial', 11, 'bold'),
                                  bg='#f8f9fa', fg='#007bff' if i+1 == self.current_step else '#6c757d')
            title_label.pack(pady=(5,2))

            desc_label = tk.Label(step_frame, text=desc, font=('Arial', 9),
                                 bg='#f8f9fa', fg='#6c757d', wraplength=step_width-20)
            desc_label.pack()

            self.step_labels.append((circle_frame, step_label))

            # Arrow between steps - positioned properly
            if i < len(steps) - 1:
                arrow_frame = tk.Frame(steps_container, bg='#f8f9fa', width=30)
                arrow_frame.pack(side='left', fill='y')
                arrow_frame.pack_propagate(False)

                tk.Label(arrow_frame, text="→", font=('Arial', 20),
                        bg='#f8f9fa', fg='#dee2e6').pack(expand=True)
    
    def clear_main_content(self):
        """Clear main content area"""
        for widget in self.main_content.winfo_children():
            widget.destroy()
    
    def update_workflow_step(self, step):
        """Update visual workflow indicator"""
        self.current_step = step
        
        for i, (circle_frame, step_label) in enumerate(self.step_labels):
            if i + 1 == step:
                circle_frame.config(bg='#007bff')
                step_label.config(bg='#007bff', fg='white')
            elif i + 1 < step:
                circle_frame.config(bg='#28a745')  # Completed steps
                step_label.config(bg='#28a745', fg='white')
            else:
                circle_frame.config(bg='#e9ecef')  # Future steps
                step_label.config(bg='#e9ecef', fg='#6c757d')
    
    def show_step_1_search(self):
        """Step 1: Component Search - Large, prominent, intuitive"""
        self.clear_main_content()
        self.update_workflow_step(1)
        
        # Large search section - takes up most of the screen
        search_container = tk.Frame(self.main_content, bg='#ffffff')
        search_container.pack(fill='both', expand=True)
        
        # Centered search area
        center_frame = tk.Frame(search_container, bg='#ffffff')
        center_frame.pack(expand=True)
        
        # Large title
        tk.Label(center_frame, text="🔍 What component are you looking for?", 
                font=('Arial', 24, 'bold'), bg='#ffffff', fg='#212529').pack(pady=30)
        
        # Large search input
        search_frame = tk.Frame(center_frame, bg='#ffffff')
        search_frame.pack(pady=20)
        
        tk.Label(search_frame, text="Component:", font=('Arial', 14, 'bold'), 
                bg='#ffffff').pack(anchor='w')
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, 
                               font=('Arial', 16), width=50, relief='solid', bd=2)
        search_entry.pack(pady=10, ipady=8)
        search_entry.focus()
        
        # Search suggestions
        suggestions_frame = tk.Frame(center_frame, bg='#ffffff')
        suggestions_frame.pack(pady=20)
        
        tk.Label(suggestions_frame, text="💡 Popular searches:", 
                font=('Arial', 12, 'bold'), bg='#ffffff').pack()
        
        suggestions = ["10k resistor", "Arduino Uno", "ESP32", "100uF capacitor", 
                      "LM358 op-amp", "BC547 transistor", "16MHz crystal"]
        
        for i, suggestion in enumerate(suggestions):
            if i % 4 == 0:
                row_frame = tk.Frame(suggestions_frame, bg='#ffffff')
                row_frame.pack(pady=5)
            
            btn = tk.Button(row_frame, text=suggestion, 
                           command=lambda s=suggestion: self.use_suggestion(s),
                           bg='#e9ecef', fg='#495057', font=('Arial', 10),
                           relief='solid', bd=1, padx=15, pady=5)
            btn.pack(side='left', padx=5)
        
        # Large search buttons
        button_frame = tk.Frame(center_frame, bg='#ffffff')
        button_frame.pack(pady=40)
        
        tk.Button(button_frame, text="🤖 AI-Powered Search", 
                 command=self.start_ai_search,
                 bg='#007bff', fg='white', font=('Arial', 14, 'bold'),
                 padx=30, pady=15, relief='solid', bd=0).pack(side='left', padx=10)
        
        tk.Button(button_frame, text="⚡ Quick Search", 
                 command=self.start_quick_search,
                 bg='#28a745', fg='white', font=('Arial', 14, 'bold'),
                 padx=30, pady=15, relief='solid', bd=0).pack(side='left', padx=10)
        
        # Bind Enter key
        search_entry.bind('<Return>', lambda e: self.start_ai_search())
    
    def use_suggestion(self, suggestion):
        """Use a search suggestion"""
        self.search_var.set(suggestion)
    
    def start_ai_search(self):
        """Start AI-powered search"""
        search_term = self.search_var.get().strip()
        if not search_term:
            return
        
        # Show loading and move to step 2
        self.show_step_2_ai_analysis(search_term)
    
    def start_quick_search(self):
        """Start quick search (skip AI analysis)"""
        search_term = self.search_var.get().strip()
        if not search_term:
            return
        
        # Skip to step 3
        self.show_step_3_results(search_term, skip_ai=True)
    
    def show_step_2_ai_analysis(self, search_term):
        """Step 2: AI Analysis - Large, readable, actionable"""
        self.clear_main_content()
        self.update_workflow_step(2)
        
        # Large AI analysis section
        ai_container = tk.Frame(self.main_content, bg='#ffffff')
        ai_container.pack(fill='both', expand=True)
        
        # Header
        header_frame = tk.Frame(ai_container, bg='#ffffff')
        header_frame.pack(fill='x', pady=20)
        
        tk.Label(header_frame, text=f"🤖 AI Analysis: '{search_term}'", 
                font=('Arial', 20, 'bold'), bg='#ffffff').pack(side='left')
        
        # Progress indicator
        self.ai_progress = ttk.Progressbar(header_frame, mode='indeterminate')
        self.ai_progress.pack(side='right', padx=20)
        self.ai_progress.start()
        
        # Large content area - no tiny boxes!
        content_frame = tk.Frame(ai_container, bg='#ffffff')
        content_frame.pack(fill='both', expand=True, padx=20)
        
        # Left side - AI insights (60% width)
        left_frame = tk.Frame(content_frame, bg='#f8f9fa', relief='solid', bd=1)
        left_frame.pack(side='left', fill='both', expand=True, padx=(0,10))
        
        # AI analysis content - LARGE and readable
        tk.Label(left_frame, text="🔍 Component Analysis", 
                font=('Arial', 16, 'bold'), bg='#f8f9fa', fg='#007bff').pack(pady=15)
        
        # Large analysis display
        analysis_text = tk.Text(left_frame, font=('Arial', 12), height=20, 
                               wrap=tk.WORD, bg='#ffffff', relief='flat', padx=20, pady=20)
        analysis_text.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Right side - Actions (40% width)
        right_frame = tk.Frame(content_frame, bg='#ffffff', width=400)
        right_frame.pack(side='right', fill='y')
        right_frame.pack_propagate(False)
        
        # Action buttons - LARGE and clear
        tk.Label(right_frame, text="🎯 Next Steps", 
                font=('Arial', 16, 'bold'), bg='#ffffff').pack(pady=15)
        
        action_buttons = [
            ("✅ Use AI Recommendations", self.use_ai_recommendations, '#007bff'),
            ("🔧 Modify Search", self.modify_search, '#6c757d'),
            ("⚡ Skip to Results", lambda: self.show_step_3_results(search_term), '#28a745')
        ]
        
        for text, command, color in action_buttons:
            tk.Button(right_frame, text=text, command=command,
                     bg=color, fg='white', font=('Arial', 12, 'bold'),
                     padx=20, pady=15, relief='solid', bd=0).pack(fill='x', pady=10, padx=20)
        
        # Simulate AI analysis
        self.simulate_ai_analysis(analysis_text, search_term)
    
    def simulate_ai_analysis(self, text_widget, search_term):
        """Simulate AI analysis with realistic content"""
        
        def update_analysis():
            analysis_content = f"""🤖 Gemini AI Analysis Complete

COMPONENT IDENTIFICATION:
✅ Type: Electronic Resistor
✅ Value: 10,000 Ohms (10kΩ)
✅ Tolerance: ±5% (standard)
✅ Power Rating: 1/4W (0.25W)
✅ Package: Through-hole preferred

KEY SPECIFICATIONS:
• Resistance: 10kΩ ±5%
• Power: 1/4W maximum
• Temperature Coefficient: ±200 ppm/°C
• Voltage Rating: 250V
• Operating Temperature: -55°C to +155°C

RECOMMENDED SEARCH TERMS:
🎯 Primary: "10k ohm resistor 1/4w carbon film"
🎯 Alternative: "10000 ohm resistor through hole"
🎯 Specific: "10kΩ ±5% 0.25W resistor"

PACKAGE OPTIONS:
📦 Through-hole (recommended for prototyping)
📦 SMD 0805 (for PCB assembly)
📦 SMD 1206 (easier hand soldering)

TOP MANUFACTURERS:
🏭 Vishay (premium quality)
🏭 Yageo (cost effective)
🏭 Panasonic (automotive grade)
🏭 Bourns (precision options)

TYPICAL APPLICATIONS:
• Pull-up resistors in digital circuits
• Voltage dividers for signal conditioning
• Current limiting for LEDs
• Biasing circuits in amplifiers
• General purpose applications

PRICE EXPECTATIONS:
💰 Individual: ₹1-5 per piece
💰 Bulk (100+): ₹0.50-2 per piece
💰 Premium brands: ₹2-8 per piece

AI CONFIDENCE: 95% ✅
"""
            
            text_widget.delete('1.0', tk.END)
            text_widget.insert('1.0', analysis_content)
            self.ai_progress.stop()
            self.ai_progress.pack_forget()
        
        # Simulate processing time
        self.root.after(2000, update_analysis)
    
    def use_ai_recommendations(self):
        """Use AI recommendations and proceed to results"""
        self.show_step_3_results("10k ohm resistor 1/4w carbon film")
    
    def modify_search(self):
        """Go back to modify search"""
        self.show_step_1_search()
    
    def show_step_3_results(self, search_term, skip_ai=False):
        """Step 3: Results - Large table, no scrolling, clear quality indicators"""
        self.clear_main_content()
        self.update_workflow_step(3)
        
        # Large results section
        results_container = tk.Frame(self.main_content, bg='#ffffff')
        results_container.pack(fill='both', expand=True)
        
        # Header with clear info
        header_frame = tk.Frame(results_container, bg='#ffffff')
        header_frame.pack(fill='x', pady=20)
        
        tk.Label(header_frame, text=f"📊 Search Results: '{search_term}'", 
                font=('Arial', 18, 'bold'), bg='#ffffff').pack(side='left')
        
        quality_label = tk.Label(header_frame, text="Quality: A+ | Results: 8 | AI Enhanced: 6", 
                                font=('Arial', 12), bg='#ffffff', fg='#28a745')
        quality_label.pack(side='right')
        
        # LARGE results table - no tiny scrolling boxes!
        table_frame = tk.Frame(results_container, bg='#ffffff')
        table_frame.pack(fill='both', expand=True, padx=20)
        
        # Create large, readable results table
        columns = ('Rank', 'Supplier', 'Component', 'Price', 'Stock', 'Quality', 'Total')
        
        self.results_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Configure columns with proper widths
        column_widths = {'Rank': 60, 'Supplier': 150, 'Component': 400, 
                        'Price': 80, 'Stock': 120, 'Quality': 80, 'Total': 100}
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=column_widths[col])
        
        # Style the treeview
        style = ttk.Style()
        style.configure("Treeview", font=('Arial', 11), rowheight=30)
        style.configure("Treeview.Heading", font=('Arial', 12, 'bold'))
        
        # Add sample results
        sample_results = [
            ("1", "Robu Electronics", "10kΩ Carbon Film Resistor 1/4W ±5%", "₹2", "500+ units", "A+", "₹47"),
            ("2", "Probots", "10K Ohm Resistor Through Hole", "₹1.5", "200+ units", "A", "₹51.5"),
            ("3", "Evelta", "Resistor 10kΩ 0.25W Carbon Film", "₹2.5", "100+ units", "A+", "₹42.5"),
            ("4", "SunRom", "10000 Ohm Resistor 1/4 Watt", "₹1.8", "300+ units", "A", "₹56.8"),
            ("5", "Electronix", "10K Carbon Film Resistor", "₹1.2", "150+ units", "B+", "₹51.2"),
        ]
        
        # Color coding
        self.results_tree.tag_configure('excellent', background='#d4edda')
        self.results_tree.tag_configure('good', background='#fff3cd')
        self.results_tree.tag_configure('average', background='#f8f9fa')
        
        for result in sample_results:
            quality = result[5]
            tag = 'excellent' if 'A' in quality else 'good' if 'B' in quality else 'average'
            self.results_tree.insert('', 'end', values=result, tags=(tag,))
        
        self.results_tree.pack(fill='both', expand=True)
        
        # Large action buttons
        action_frame = tk.Frame(results_container, bg='#ffffff')
        action_frame.pack(fill='x', pady=20, padx=20)
        
        tk.Button(action_frame, text="📄 View Datasheets", 
                 command=self.show_step_4_details,
                 bg='#007bff', fg='white', font=('Arial', 12, 'bold'),
                 padx=20, pady=10).pack(side='left', padx=10)
        
        tk.Button(action_frame, text="🔍 Refine Search", 
                 command=self.show_step_1_search,
                 bg='#6c757d', fg='white', font=('Arial', 12, 'bold'),
                 padx=20, pady=10).pack(side='left', padx=10)
        
        tk.Button(action_frame, text="✅ Select & Export", 
                 command=self.show_step_5_decision,
                 bg='#28a745', fg='white', font=('Arial', 12, 'bold'),
                 padx=20, pady=10).pack(side='right', padx=10)
    
    def show_step_4_details(self):
        """Step 4: Datasheet Details - Large, readable PDFs"""
        self.clear_main_content()
        self.update_workflow_step(4)
        
        # Large datasheet section
        details_container = tk.Frame(self.main_content, bg='#ffffff')
        details_container.pack(fill='both', expand=True)
        
        tk.Label(details_container, text="📄 Component Datasheets & Specifications", 
                font=('Arial', 18, 'bold'), bg='#ffffff').pack(pady=20)
        
        # Large content area
        content_frame = tk.Frame(details_container, bg='#ffffff')
        content_frame.pack(fill='both', expand=True, padx=20)
        
        # Left - datasheet list (30%)
        left_frame = tk.Frame(content_frame, bg='#f8f9fa', relief='solid', bd=1, width=300)
        left_frame.pack(side='left', fill='y', padx=(0,10))
        left_frame.pack_propagate(False)
        
        tk.Label(left_frame, text="📋 Available Datasheets", 
                font=('Arial', 14, 'bold'), bg='#f8f9fa').pack(pady=15)
        
        datasheets = [
            "📄 Vishay Carbon Film Resistor",
            "📄 Yageo General Purpose Resistor", 
            "📄 Panasonic Resistor Selection Guide",
            "📄 Resistor Color Code Chart",
            "📄 Power Rating Guidelines"
        ]
        
        for ds in datasheets:
            tk.Button(left_frame, text=ds, anchor='w',
                     bg='#ffffff', relief='solid', bd=1,
                     font=('Arial', 11), padx=15, pady=8).pack(fill='x', padx=10, pady=2)
        
        # Right - large datasheet viewer (70%)
        right_frame = tk.Frame(content_frame, bg='#ffffff', relief='solid', bd=1)
        right_frame.pack(side='right', fill='both', expand=True)
        
        # Large PDF display area
        pdf_display = tk.Text(right_frame, font=('Arial', 12), bg='#ffffff',
                             relief='flat', padx=30, pady=30)
        pdf_display.pack(fill='both', expand=True)
        
        pdf_content = """📄 VISHAY CARBON FILM RESISTOR DATASHEET

PART NUMBER: CFR-25JB-52-10K
RESISTANCE: 10,000 Ohms (10kΩ)
TOLERANCE: ±5%
POWER RATING: 0.25W (1/4W)

SPECIFICATIONS:
• Temperature Coefficient: ±200 ppm/°C
• Operating Temperature: -55°C to +155°C
• Maximum Voltage: 250V
• Noise: <0.2µV/V
• Lead Diameter: 0.6mm
• Body Length: 6.3mm
• Body Diameter: 2.3mm

APPLICATIONS:
✓ General purpose applications
✓ Voltage dividers
✓ Pull-up/pull-down resistors
✓ Current limiting
✓ Timing circuits

ORDERING INFORMATION:
CFR-25JB-52-10K (Tape & Reel)
CFR-25JB-52-10KTR (Cut Tape)

PRICING (India):
1-99 pieces: ₹2.50 each
100-999 pieces: ₹1.80 each
1000+ pieces: ₹1.20 each

AVAILABILITY:
✅ In Stock - Ships within 24 hours
✅ Local distributor: Robu Electronics
✅ Lead time: Same day delivery in Mumbai"""
        
        pdf_display.insert('1.0', pdf_content)
        pdf_display.config(state='disabled')
        
        # Action buttons
        action_frame = tk.Frame(details_container, bg='#ffffff')
        action_frame.pack(fill='x', pady=20, padx=20)
        
        tk.Button(action_frame, text="⬅️ Back to Results", 
                 command=lambda: self.show_step_3_results("10k resistor"),
                 bg='#6c757d', fg='white', font=('Arial', 12, 'bold'),
                 padx=20, pady=10).pack(side='left')
        
        tk.Button(action_frame, text="✅ Make Decision", 
                 command=self.show_step_5_decision,
                 bg='#28a745', fg='white', font=('Arial', 12, 'bold'),
                 padx=20, pady=10).pack(side='right')
    
    def show_step_5_decision(self):
        """Step 5: Final Decision - Clear summary and export"""
        self.clear_main_content()
        self.update_workflow_step(5)
        
        # Decision summary
        decision_container = tk.Frame(self.main_content, bg='#ffffff')
        decision_container.pack(fill='both', expand=True)
        
        tk.Label(decision_container, text="✅ Final Decision & Export", 
                font=('Arial', 18, 'bold'), bg='#ffffff').pack(pady=20)
        
        # Large summary
        summary_frame = tk.Frame(decision_container, bg='#f8f9fa', relief='solid', bd=1)
        summary_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        summary_text = tk.Text(summary_frame, font=('Arial', 12), bg='#f8f9fa',
                              relief='flat', padx=30, pady=30)
        summary_text.pack(fill='both', expand=True)
        
        summary_content = """🎯 COMPONENT SOURCING SUMMARY

SELECTED COMPONENT: 10kΩ Carbon Film Resistor 1/4W ±5%

TOP RECOMMENDATION:
🥇 Robu Electronics - ₹2.00 each + ₹45 shipping = ₹47 total
   ✅ Highest quality score (A+)
   ✅ Best availability (500+ units)
   ✅ Fast delivery (24 hours)
   ✅ Verified supplier

ALTERNATIVE OPTIONS:
🥈 Evelta - ₹2.50 each + ₹40 shipping = ₹42.50 total
🥉 Probots - ₹1.50 each + ₹50 shipping = ₹51.50 total

AI ANALYSIS SUMMARY:
✅ Component correctly identified
✅ Specifications verified
✅ Price range validated (₹1-3 expected)
✅ Quality suppliers found
✅ Datasheets available

EXPORT OPTIONS:
📊 Detailed comparison spreadsheet
📋 Procurement report with specifications
📄 Supplier contact information
🔗 Direct purchase links"""
        
        summary_text.insert('1.0', summary_content)
        summary_text.config(state='disabled')
        
        # Large export buttons
        export_frame = tk.Frame(decision_container, bg='#ffffff')
        export_frame.pack(fill='x', pady=20, padx=20)
        
        export_buttons = [
            ("📊 Export to Excel", '#007bff'),
            ("📋 Generate Report", '#28a745'),
            ("🛒 Open Purchase Links", '#ffc107'),
            ("🔄 Start New Search", '#6c757d')
        ]
        
        for text, color in export_buttons:
            tk.Button(export_frame, text=text, 
                     bg=color, fg='white' if color != '#ffc107' else 'black',
                     font=('Arial', 12, 'bold'), padx=25, pady=12).pack(side='left', padx=10)
    
    def run(self):
        """Run the workflow-driven UI"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🎯 Workflow-Driven UI Demo")
    print("=" * 30)
    print("This shows an intuitive step-by-step interface:")
    print("1. Clear workflow steps at the top")
    print("2. Large, prominent content areas")
    print("3. No wasted space or tiny scrolling boxes")
    print("4. Intuitive next steps always visible")
    print("5. Professional space utilization")
    
    app = WorkflowDrivenUI()
    app.run()
