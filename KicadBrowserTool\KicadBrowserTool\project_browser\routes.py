# P:\KiCadProjectBrowserTool\project_browser\routes.py
import os
import platform
import subprocess
import json
import datetime
import time 
from flask import jsonify, render_template, request, send_from_directory, Response, stream_with_context

from . import app
from . import config 
from .scanner import scan_projects_generator

@app.route('/')
def index_page_route():
    print(f"--- [{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Serving / (index.html) ---")
    return render_template('index.html')

@app.route('/static/<path:filename>')
def static_files_route(filename):
    static_dir_abs_path = os.path.abspath(os.path.join(config.TOOL_ROOT_DIR, 'static'))
    return send_from_directory(static_dir_abs_path, filename)

@app.route('/api/scan_progress_stream')
def scan_progress_stream_route():
    print(f"--- [{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] SSE stream requested: /api/scan_progress_stream ---")
    def event_stream():
        for progress_update in scan_projects_generator():
            json_data = json.dumps(progress_update)
            yield f"data: {json_data}\n\n"
            time.sleep(0.01) 
        print(f"--- [{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] SSE stream /api/scan_progress_stream finished ---")
    return Response(stream_with_context(event_stream()), mimetype='text/event-stream')

@app.route('/api/projects', methods=['GET'])
def get_projects_api_route():
    print(f"--- [{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] API request: /api/projects (GET) ---")
    if not os.path.exists(config.INDEX_FILE_PATH):
        print(f"  [API /projects] {config.INDEX_FILE_NAME} not found. Client should initiate scan.")
        return jsonify({
            "metadata": {
                "server_script_version": config.SERVER_VERSION,
                "ui_version_target": config.UI_VERSION, 
                "error": "Project index not found. Please refresh/scan to generate it.",
                "scan_timestamp_utc": None,
                "total_projects_found": 0,
                "data_exists": False 
            }, 
            "projects": []
        }), 200 
    try:
        with open(config.INDEX_FILE_PATH, 'r') as f:
            projects_data_with_metadata = json.load(f)
        if "metadata" not in projects_data_with_metadata: projects_data_with_metadata["metadata"] = {}
        projects_data_with_metadata["metadata"]["data_exists"] = True
        return jsonify(projects_data_with_metadata)
    except json.JSONDecodeError:
        print(f"  [API /projects WARNING] {config.INDEX_FILE_NAME} is corrupted. Returning error. Client should initiate scan.")
        return jsonify({
            "metadata": {
                "server_script_version": config.SERVER_VERSION,
                "ui_version_target": config.UI_VERSION,
                "error": "Project index corrupted. Please refresh/scan to regenerate it.",
                "scan_timestamp_utc": None,
                "total_projects_found": 0,
                "data_exists": False 
            },
            "projects": []
        }), 200 
    except Exception as e: 
        print(f"  [API /projects ERROR] Could not read {config.INDEX_FILE_NAME}: {e}")
        return jsonify({
            "metadata": {
                "server_script_version": config.SERVER_VERSION,
                "ui_version_target": config.UI_VERSION,
                "error": f"Error reading project index: {e}. Please refresh/scan.",
                "scan_timestamp_utc": None,
                "total_projects_found": 0,
                "data_exists": False
            },
            "projects": []
        }), 200


@app.route('/api/scan', methods=['POST'])
def trigger_scan_api_route():
    # This route ensures the index is updated by consuming the generator.
    # Client ideally uses SSE for progress.
    # ==================================================================
    # REMOVED THE BAD PRINT LINE
    # ==================================================================
    print(f"--- [{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Manual scan trigger requested: /api/scan (POST) ---")
    
    final_data = None
    for data_chunk in scan_projects_generator(): # This consumes the generator and forces scan & save
        if data_chunk.get("type") == "final_data":
            final_data = data_chunk.get("payload")
            # No break here, let it finish all its yields if any SSE client is also attached
            # Though for this POST route, we only care about the final result for the response.
    
    if final_data:
        return jsonify({
            "message": "Scan initiated and completed (index updated).", 
            "project_count": final_data.get("metadata", {}).get("total_projects_found", 0),
            "duration_seconds": final_data.get("metadata", {}).get("scan_duration_seconds", 0)
        }), 200
    else:
        # This might happen if the generator had a critical error before yielding final_data
        return jsonify({"message": "Scan process was triggered, but encountered an issue before completion.", "error": "Unexpected issue during scan for POST /api/scan"}), 500


@app.route('/api/open_folder', methods=['POST'])
def open_folder_api_route():
    data = request.get_json()
    if not data: return jsonify({"success": False, "message": "No JSON data received."}), 400
    folder_path = data.get('path')
    if not folder_path: return jsonify({"success": False, "message": "Missing 'path' in request data."}), 400
    if not os.path.isabs(folder_path): return jsonify({"success": False, "message": f"Path must be absolute: {folder_path}"}), 400
    if not os.path.isdir(folder_path): return jsonify({"success": False, "message": f"Path is not a valid directory: {folder_path}"}), 400
    print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] API request to open folder: {folder_path}")
    try:
        if platform.system() == "Windows": os.startfile(os.path.normpath(folder_path))
        elif platform.system() == "Darwin": subprocess.run(['open', os.path.normpath(folder_path)], check=True)
        else: subprocess.run(['xdg-open', os.path.normpath(folder_path)], check=True)
        return jsonify({"success": True, "message": f"Opened {folder_path}"})
    except Exception as e:
        print(f"  [API_ERROR] Error opening folder {folder_path}: {e}")
        return jsonify({"success": False, "message": str(e)}), 500