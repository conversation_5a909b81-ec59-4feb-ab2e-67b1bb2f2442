#!/usr/bin/env python3
"""
Demo of Enhanced UI - Show what the improved interface will look like
"""

import tkinter as tk
from tkinter import ttk
from enhanced_ai_ui import GeminiResultsPanel, EnhancedResultsDisplay, DatasheetViewer

class EnhancedUIDemo:
    """Demo of the enhanced UI with proper AI integration"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🤖 Enhanced AI Component Search - UI Demo")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f8f9fa')
        
        self.setup_ui()
        self.show_demo_data()
    
    def setup_ui(self):
        """Setup the enhanced UI"""
        
        # Header
        header_frame = tk.Frame(self.root, bg='#4285f4', height=80)
        header_frame.pack(fill='x', padx=5, pady=5)
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="🤖 Enhanced AI Component Search v2.0", 
                font=('Arial', 16, 'bold'), fg='white', bg='#4285f4').pack(expand=True)
        
        # Main content area
        main_frame = tk.Frame(self.root, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Left panel - Search and AI Analysis
        left_panel = tk.Frame(main_frame, bg='#f8f9fa', width=500)
        left_panel.pack(side='left', fill='y', padx=(0,5))
        left_panel.pack_propagate(False)
        
        # Search section
        search_frame = tk.LabelFrame(left_panel, text="🔍 Smart Component Search", 
                                    font=('Arial', 12, 'bold'), bg='#f8f9fa')
        search_frame.pack(fill='x', pady=(0,10))
        
        # Search input
        tk.Label(search_frame, text="Component:", font=('Arial', 10, 'bold'), 
                bg='#f8f9fa').pack(anchor='w', padx=10, pady=5)
        
        search_var = tk.StringVar(value="10k resistor")
        search_entry = tk.Entry(search_frame, textvariable=search_var, 
                               font=('Arial', 12), width=40)
        search_entry.pack(fill='x', padx=10, pady=5)
        
        # Search buttons
        button_frame = tk.Frame(search_frame, bg='#f8f9fa')
        button_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(button_frame, text="🤖 Gemini AI Search", 
                 command=self.demo_ai_search, bg='#4285f4', fg='white',
                 font=('Arial', 11, 'bold')).pack(fill='x', pady=2)
        
        tk.Button(button_frame, text="🔍 Smart Search", 
                 command=self.demo_smart_search, bg='#28a745', fg='white',
                 font=('Arial', 11)).pack(fill='x', pady=2)
        
        tk.Button(button_frame, text="⚡ Quick Search", 
                 command=self.demo_quick_search, bg='#ffc107', fg='black',
                 font=('Arial', 11)).pack(fill='x', pady=2)
        
        # AI Analysis Panel
        self.ai_panel = GeminiResultsPanel(left_panel)
        
        # Right panel - Results and Datasheets
        right_panel = tk.Frame(main_frame, bg='#f8f9fa')
        right_panel.pack(side='right', fill='both', expand=True)
        
        # Results section
        results_frame = tk.LabelFrame(right_panel, text="🔍 Search Results with AI Scoring", 
                                     font=('Arial', 12, 'bold'), bg='#f8f9fa')
        results_frame.pack(fill='both', expand=True, pady=(0,10))
        
        # Enhanced results display
        self.results_display = EnhancedResultsDisplay(results_frame)
        
        # Datasheet section
        datasheet_frame = tk.LabelFrame(right_panel, text="📄 Datasheet Quick Access", 
                                       font=('Arial', 12, 'bold'), bg='#f8f9fa', height=150)
        datasheet_frame.pack(fill='x')
        datasheet_frame.pack_propagate(False)
        
        # Datasheet buttons
        ds_button_frame = tk.Frame(datasheet_frame, bg='#f8f9fa')
        ds_button_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(ds_button_frame, text="📄 Open Datasheet Manager", 
                 command=self.open_datasheet_manager, bg='#007bff', fg='white',
                 font=('Arial', 11)).pack(side='left', padx=5)
        
        tk.Button(ds_button_frame, text="🔍 Search Datasheets", 
                 command=self.search_datasheets, bg='#17a2b8', fg='white',
                 font=('Arial', 11)).pack(side='left', padx=5)
        
        tk.Button(ds_button_frame, text="📋 AI Extract Info", 
                 command=self.ai_extract_info, bg='#ffc107', fg='black',
                 font=('Arial', 11)).pack(side='left', padx=5)
        
        # Status bar
        status_frame = tk.Frame(self.root, bg='#e9ecef', height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(status_frame, text="✅ Enhanced UI Demo Ready - All AI features properly integrated", 
                                    font=('Arial', 10), bg='#e9ecef', fg='#495057')
        self.status_label.pack(expand=True)
    
    def show_demo_data(self):
        """Show demo AI analysis and results"""
        
        # Create mock AI analysis
        class MockAnalysis:
            def __init__(self):
                self.component_type = "resistor"
                self.manufacturer = "Vishay"
                self.part_number = "10kΩ Carbon Film Resistor 1/4W"
                self.confidence_score = 0.95
                self.specifications = {
                    "Resistance": "10kΩ ±5%",
                    "Power Rating": "1/4W (0.25W)",
                    "Tolerance": "±5%",
                    "Temperature Coefficient": "±200 ppm/°C",
                    "Package": "Through-hole",
                    "Lead Spacing": "0.4 inch (10.16mm)"
                }
                self.package_options = ["Through-hole", "SMD 0805", "SMD 1206", "SMD 0603"]
                self.applications = [
                    "Pull-up resistors in digital circuits",
                    "Voltage dividers for signal conditioning", 
                    "Current limiting for LEDs",
                    "Biasing circuits in amplifiers"
                ]
                self.alternatives = [
                    "10.1kΩ Precision Resistor (1%)",
                    "9.9kΩ Standard Resistor",
                    "10kΩ Metal Film Resistor",
                    "10kΩ Wirewound Resistor (high power)"
                ]
        
        mock_manufacturers = [
            {"name": "Vishay", "reputation": "Excellent", "availability": "High", 
             "notes": "Industry standard, reliable quality"},
            {"name": "Yageo", "reputation": "Good", "availability": "High", 
             "notes": "Cost effective, good for volume"},
            {"name": "Panasonic", "reputation": "Excellent", "availability": "Medium", 
             "notes": "Premium quality, automotive grade"},
            {"name": "Bourns", "reputation": "Good", "availability": "Medium", 
             "notes": "Specialized applications"},
            {"name": "KOA Speer", "reputation": "Good", "availability": "Medium", 
             "notes": "High precision options"}
        ]
        
        # Show AI analysis
        self.ai_panel.show_analysis(MockAnalysis(), mock_manufacturers)
        
        # Show demo results with AI scoring
        demo_results = [
            {
                'supplier': 'Robu Electronics',
                'component': '10kΩ Carbon Film Resistor 1/4W ±5%',
                'price': 2,
                'stock': 'In Stock (500 units)',
                'shipping': 45,
                'quality_score': 95,
                'ai_enhanced': True,
                'ai_confidence': 0.95
            },
            {
                'supplier': 'Probots',
                'component': '10K Ohm Resistor Through Hole',
                'price': 1.5,
                'stock': 'In Stock (200 units)',
                'shipping': 50,
                'quality_score': 88,
                'ai_enhanced': True,
                'ai_confidence': 0.88
            },
            {
                'supplier': 'Evelta',
                'component': 'Resistor 10kΩ 0.25W Carbon Film',
                'price': 2.5,
                'stock': 'In Stock (100 units)',
                'shipping': 40,
                'quality_score': 92,
                'ai_enhanced': True,
                'ai_confidence': 0.92
            },
            {
                'supplier': 'SunRom',
                'component': '10000 Ohm Resistor 1/4 Watt',
                'price': 1.8,
                'stock': 'In Stock (300 units)',
                'shipping': 55,
                'quality_score': 85,
                'ai_enhanced': True,
                'ai_confidence': 0.85
            },
            {
                'supplier': 'Generic Supplier',
                'component': 'Arduino Uno R3 Board',  # This should be filtered out
                'price': 650,
                'stock': 'In Stock (10 units)',
                'shipping': 60,
                'quality_score': 25,  # Low score - irrelevant
                'ai_enhanced': False,
                'ai_confidence': 0.0
            }
        ]
        
        # Add results to display
        for result in demo_results:
            if result['quality_score'] >= 60:  # Only show quality results
                self.results_display.add_result(result)
        
        # Update quality summary
        quality_results = [r for r in demo_results if r['quality_score'] >= 60]
        ai_enhanced_count = sum(1 for r in quality_results if r.get('ai_enhanced', False))
        self.results_display.update_quality_summary(len(quality_results), "A+", ai_enhanced_count)
    
    def demo_ai_search(self):
        """Demo AI search functionality"""
        self.status_label.config(text="🤖 Gemini AI analyzing component... (Demo: This would trigger full AI analysis)")
    
    def demo_smart_search(self):
        """Demo smart search functionality"""
        self.status_label.config(text="🔍 Smart search with intelligent questions... (Demo: This would show parameter dialog)")
    
    def demo_quick_search(self):
        """Demo quick search functionality"""
        self.status_label.config(text="⚡ Quick search across top suppliers... (Demo: This would search without AI)")
    
    def open_datasheet_manager(self):
        """Open datasheet manager"""
        try:
            DatasheetViewer(self.root)
            self.status_label.config(text="📄 Datasheet Manager opened - Professional PDF viewer with search capabilities")
        except Exception as e:
            self.status_label.config(text=f"📄 Datasheet Manager demo: {str(e)}")
    
    def search_datasheets(self):
        """Demo datasheet search"""
        self.status_label.config(text="🔍 Searching datasheets for '10k resistor'... (Demo: This would search PDF contents)")
    
    def ai_extract_info(self):
        """Demo AI info extraction"""
        self.status_label.config(text="📋 AI extracting key specifications from datasheets... (Demo: This would use AI to parse PDFs)")
    
    def run(self):
        """Run the demo"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🎨 Enhanced UI Demo")
    print("=" * 25)
    print("This demo shows what the enhanced UI will look like:")
    print("• Large, readable AI analysis panels")
    print("• Clickable AI parameters and recommendations")
    print("• Color-coded results with AI scoring")
    print("• Professional datasheet management")
    print("• Integrated AI insights throughout the interface")
    print("\nStarting demo...")
    
    demo = EnhancedUIDemo()
    demo.run()
