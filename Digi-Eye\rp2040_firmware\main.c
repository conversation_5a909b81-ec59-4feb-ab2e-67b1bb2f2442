#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>

#include "pico/stdlib.h"
#include "hardware/gpio.h"
#include "tusb.h"

/*
================================================================================
 IMPORTANT: HARDWARE CONFIGURATION
================================================================================
This firmware is a complete template but requires you to configure the GPIO
pins below to match your specific hardware wiring.

This code assumes:
1.  A 4-digit, 7-segment display.
2.  A 'common cathode' type display. If you have a 'common anode' display,
    you will need to invert the logic for putting pins high/low (e.g.,
    gpio_put(..., 1) becomes gpio_put(..., 0) and vice-versa for segments
    and digit selection).
3.  The display is driven by multiplexing, where only one digit is lit at
    any single moment, but they are cycled fast enough for the human eye
    to see them all lit at once (Persistence of Vision).
================================================================================
*/

// --- GPIO PIN DEFINITIONS (USER MUST CONFIGURE) ---

// Assign GPIO pins for the 7 segments (a, b, c, d, e, f, g) and the decimal point (dp)
const uint SEGMENT_PINS[] = {6, 7, 8, 9, 10, 11, 12, 13}; // Segments a-g, then dp
const uint NUM_SEGMENT_PINS = 8;

// Assign GPIO pins for the 4 digit selection lines (common cathodes)
const uint DIGIT_PINS[] = {16, 17, 18, 19}; // Digit 1, 2, 3, 4
const uint NUM_DIGIT_PINS = 4;

// --- GLOBAL STATE FOR DISPLAY ---
volatile uint8_t digit_patterns[NUM_DIGIT_PINS] = {0}; // Holds the segment pattern for each digit
volatile bool decimal_points[NUM_DIGIT_PINS] = {false}; // Holds the DP state for each digit
volatile uint8_t active_digit_for_mux = 0; // Current active digit for multiplexing

// Lookup table for 7-segment display (common cathode). Bit order: 0b(dp)gfedcba
const uint8_t SEGMENT_MAP[10] = {
    0b00111111, // 0
    0b00000110, // 1
    0b01011011, // 2
    0b01001111, // 3
    0b01100110, // 4
    0b01101101, // 5
    0b01111101, // 6
    0b00000111, // 7
    0b01111111, // 8
    0b01101111  // 9
};
const uint8_t CHAR_MAP_DASH = 0b01000000; // '-'
const uint8_t CHAR_MAP_BLANK = 0b00000000; // ' ' (blank)

// --- GLOBAL STATE FOR COUNTING ANIMATION ---
volatile double current_value = 0.0;
volatile double count_step = 0.01;
volatile int32_t count_delay_ms = 100;
volatile enum { PAUSED, COUNTING_UP, COUNTING_DOWN } count_state = PAUSED;
struct repeating_timer animation_timer;
struct repeating_timer display_refresh_timer;

// --- FUNCTION PROTOTYPES ---
void display_set_value(const char* value_str);
bool animation_timer_callback(struct repeating_timer *t);
bool display_refresh_callback(struct repeating_timer *t);
void handle_serial_command(char* buffer);

// --- DISPLAY DRIVER IMPLEMENTATION ---

void display_init() {
    for (int i = 0; i < NUM_SEGMENT_PINS; ++i) {
        gpio_init(SEGMENT_PINS[i]);
        gpio_set_dir(SEGMENT_PINS[i], GPIO_OUT);
    }
    for (int i = 0; i < NUM_DIGIT_PINS; ++i) {
        gpio_init(DIGIT_PINS[i]);
        gpio_set_dir(DIGIT_PINS[i], GPIO_OUT);
        gpio_put(DIGIT_PINS[i], 1); // Deactivate all digits (for common cathode)
    }
    printf("Display GPIOs Initialized.\n");
}

// This is the core multiplexing function, called by a repeating timer.
bool display_refresh_callback(struct repeating_timer *t) {
    // Deactivate the current digit's common cathode pin
    gpio_put(DIGIT_PINS[active_digit_for_mux], 1);

    // Move to the next digit, wrapping around
    active_digit_for_mux = (active_digit_for_mux + 1) % NUM_DIGIT_PINS;

    // Set the segment pins for the new active digit
    uint8_t pattern = digit_patterns[active_digit_for_mux];
    for (int i = 0; i < 7; i++) { // 7 segments for the number
        gpio_put(SEGMENT_PINS[i], (pattern >> i) & 1);
    }
    // Set the decimal point segment
    gpio_put(SEGMENT_PINS[7], decimal_points[active_digit_for_mux]);

    // Activate the new digit's common cathode pin
    gpio_put(DIGIT_PINS[active_digit_for_mux], 0);
    
    return true; // Keep the timer running
}

// Parses a string like "12.34" and updates the global pattern buffers for display.
void display_set_value(const char* value_str) {
    char clean_str[10] = {0};
    int clean_idx = 0;
    int dp_pos = -1;

    // Create a clean string with no decimal point and find its original position
    for (int i = 0; i < strlen(value_str) && clean_idx < sizeof(clean_str) -1; i++) {
        if (value_str[i] == '.') {
            dp_pos = clean_idx;
        } else {
            clean_str[clean_idx++] = value_str[i];
        }
    }

    // Reset display patterns
    for(int i=0; i<NUM_DIGIT_PINS; i++) {
        digit_patterns[i] = CHAR_MAP_BLANK;
        decimal_points[i] = false;
    }

    int len = strlen(clean_str);
    int start_digit = NUM_DIGIT_PINS - len;

    // Populate the patterns for the digits
    for (int i = 0; i < len; i++) {
        int digit_idx = start_digit + i;
        if (digit_idx >= 0 && digit_idx < NUM_DIGIT_PINS) {
            if (clean_str[i] >= '0' && clean_str[i] <= '9') {
                digit_patterns[digit_idx] = SEGMENT_MAP[clean_str[i] - '0'];
            } else if (clean_str[i] == '-') {
                digit_patterns[digit_idx] = CHAR_MAP_DASH;
            }
        }
    }

    // Set the decimal point
    if (dp_pos != -1) {
        int dp_digit_idx = start_digit + dp_pos - 1;
        if (dp_digit_idx >= 0 && dp_digit_idx < NUM_DIGIT_PINS) {
            decimal_points[dp_digit_idx] = true;
        }
    }
}


// --- ANIMATION AND COMMAND LOGIC ---

bool animation_timer_callback(struct repeating_timer *t) {
    if (count_state == COUNTING_UP) {
        current_value += count_step;
    } else if (count_state == COUNTING_DOWN) {
        current_value -= count_step;
    } else {
        return true; // Do nothing if paused
    }
    char buffer[10];
    snprintf(buffer, sizeof(buffer), "%.2f", current_value);
    display_set_value(buffer);
    return true;
}

void display_random() {
    count_state = PAUSED;
    cancel_repeating_timer(&animation_timer);
    current_value = (double)(rand() % 10000) / 100.0;
    char buffer[10];
    snprintf(buffer, sizeof(buffer), "%.2f", current_value);
    display_set_value(buffer);
    printf("Display set to random value: %s\n", buffer);
}

void display_pause() {
    count_state = PAUSED;
    cancel_repeating_timer(&animation_timer);
    printf("Display paused.\n");
}

void handle_serial_command(char* buffer) {
    if (buffer[0] == '\0') return;
    printf("Received command: %s\n", buffer);

    switch (buffer[0]) {
        case 'S':
            display_pause(); // Stop any counting
            display_set_value(&buffer[1]);
            current_value = atof(&buffer[1]);
            break;
        case 'R':
            display_random();
            break;
        case 'U':
        case 'D':
            count_delay_ms = atoi(&buffer[1]);
            if (count_delay_ms <= 0) count_delay_ms = 100;
            
            // Cancel previous timer before starting a new one to prevent issues
            cancel_repeating_timer(&animation_timer);
            add_repeating_timer_ms(count_delay_ms, animation_timer_callback, NULL, &animation_timer);
            
            count_state = (buffer[0] == 'U') ? COUNTING_UP : COUNTING_DOWN;
            printf("Command '%s' received. Delay: %ldms\n", count_state == COUNTING_UP ? "Count Up" : "Count Down", count_delay_ms);
            break;
        case 'P':
            display_pause();
            break;
        default:
            printf("Unknown command: %c\n", buffer[0]);
            break;
    }
}

void usb_task() {
    static char serial_buf[64];
    static uint8_t buf_idx = 0;
    if (tud_cdc_available()) {
        char ch = tud_cdc_read_char();
        if (ch == '\n' || ch == '\r') {
            if (buf_idx > 0) {
                serial_buf[buf_idx] = '\0';
                handle_serial_command(serial_buf);
                buf_idx = 0;
            }
        } else if (buf_idx < sizeof(serial_buf) - 1) {
            serial_buf[buf_idx++] = ch;
        }
    }
}

// --- MAIN ENTRY POINT ---
int main() {
    stdio_init_all();
    sleep_ms(2000); // Wait for serial port to be ready
    printf("Digi-Eye RP2040 Firmware Initializing...\n");

    display_init();
    
    // Start the display refresh timer. 4ms = 250Hz total refresh rate, ~62.5Hz per digit.
    add_repeating_timer_ms(4, display_refresh_callback, NULL, &display_refresh_timer);

    display_set_value("88.88");
    sleep_ms(1000);
    display_set_value("----");

    while (1) {
        tud_task(); // TinyUSB background task
        usb_task(); // Check for incoming serial commands
    }
    return 0;
}