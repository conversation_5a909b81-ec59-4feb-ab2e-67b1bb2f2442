#!/usr/bin/env python3
"""
AI-Enhanced Search Dialog
Advanced component search with AI analysis and datasheet integration
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Dict, List, Optional
from ai_analyzer import get_ai_analyzer, ComponentAnalysis
from datasheet_manager import get_datasheet_manager
from component_intelligence import analyze_component_search

class AISearchDialog:
    """AI-enhanced search dialog with datasheet analysis"""
    
    def __init__(self, parent, initial_search_term=""):
        self.parent = parent
        self.result = None
        self.search_term = initial_search_term
        self.ai_analyzer = get_ai_analyzer()
        self.datasheet_manager = get_datasheet_manager()
        
        # AI analysis results
        self.ai_analysis = None
        self.component_analysis = None
        self.manufacturer_recommendations = []
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("🤖 AI-Powered Component Search")
        self.dialog.geometry("900x700")
        self.dialog.configure(bg='#f8f9fa')
        self.dialog.resizable(True, True)
        
        # Center the dialog
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.setup_ui()
        
        # Start AI analysis in background
        if initial_search_term:
            self.start_ai_analysis()
        
        # Focus on dialog
        self.dialog.focus_set()
    
    def setup_ui(self):
        """Setup the dialog UI"""
        
        # Header with AI indicator
        header_frame = tk.Frame(self.dialog, bg='#007bff', height=80)
        header_frame.pack(fill='x', padx=5, pady=5)
        header_frame.pack_propagate(False)
        
        header_label = tk.Label(header_frame, 
                               text=f"🤖 AI Analysis for: '{self.search_term}'",
                               font=('Arial', 14, 'bold'), fg='white', bg='#007bff')
        header_label.pack(expand=True)
        
        # AI status indicator
        self.ai_status_label = tk.Label(header_frame, text="🔄 Analyzing with AI...",
                                       font=('Arial', 10), fg='#e3f2fd', bg='#007bff')
        self.ai_status_label.pack()
        
        # Main content area with notebook
        main_frame = tk.Frame(self.dialog, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True)
        
        # AI Analysis tab
        ai_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(ai_frame, text="🤖 AI Analysis")
        self.setup_ai_analysis_tab(ai_frame)
        
        # Manufacturer Recommendations tab
        manufacturers_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(manufacturers_frame, text="🏭 Top Manufacturers")
        self.setup_manufacturers_tab(manufacturers_frame)
        
        # Datasheet Analysis tab
        datasheet_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(datasheet_frame, text="📄 Datasheet Analysis")
        self.setup_datasheet_tab(datasheet_frame)
        
        # Search Configuration tab
        config_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(config_frame, text="⚙️ Search Config")
        self.setup_config_tab(config_frame)
        
        # Buttons
        button_frame = tk.Frame(self.dialog, bg='#f8f9fa')
        button_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(button_frame, text="🚀 Start AI-Enhanced Search", 
                 command=self.start_ai_search, bg='#28a745', fg='white',
                 font=('Arial', 12, 'bold'), padx=20).pack(side='left', padx=5)
        
        tk.Button(button_frame, text="📄 Download Datasheets First", 
                 command=self.download_datasheets, bg='#17a2b8', fg='white',
                 font=('Arial', 11)).pack(side='left', padx=5)
        
        tk.Button(button_frame, text="🔍 Quick Search (No AI)", 
                 command=self.quick_search, bg='#6c757d', fg='white',
                 font=('Arial', 11)).pack(side='left', padx=5)
        
        tk.Button(button_frame, text="❌ Cancel", 
                 command=self.cancel, bg='#dc3545', fg='white',
                 font=('Arial', 11)).pack(side='right', padx=5)
    
    def setup_ai_analysis_tab(self, parent):
        """Setup AI analysis results tab"""
        
        # Analysis status
        status_frame = tk.LabelFrame(parent, text="AI Analysis Status", bg='#ffffff')
        status_frame.pack(fill='x', padx=10, pady=10)
        
        self.analysis_status_text = tk.Text(status_frame, height=4, font=('Consolas', 9),
                                           bg='#f8f9fa', fg='#495057', wrap=tk.WORD)
        self.analysis_status_text.pack(fill='x', padx=10, pady=5)
        self.analysis_status_text.insert('1.0', "🔄 Starting AI analysis...\n")
        
        # Component classification
        classification_frame = tk.LabelFrame(parent, text="Component Classification", bg='#ffffff')
        classification_frame.pack(fill='x', padx=10, pady=5)
        
        self.component_type_var = tk.StringVar(value="Analyzing...")
        tk.Label(classification_frame, text="Component Type:", font=('Arial', 10, 'bold'),
                bg='#ffffff').grid(row=0, column=0, sticky='w', padx=10, pady=5)
        tk.Label(classification_frame, textvariable=self.component_type_var, font=('Arial', 10),
                bg='#ffffff', fg='#007bff').grid(row=0, column=1, sticky='w', padx=10, pady=5)
        
        self.manufacturer_var = tk.StringVar(value="Analyzing...")
        tk.Label(classification_frame, text="Likely Manufacturer:", font=('Arial', 10, 'bold'),
                bg='#ffffff').grid(row=1, column=0, sticky='w', padx=10, pady=5)
        tk.Label(classification_frame, textvariable=self.manufacturer_var, font=('Arial', 10),
                bg='#ffffff', fg='#007bff').grid(row=1, column=1, sticky='w', padx=10, pady=5)
        
        self.confidence_var = tk.StringVar(value="Analyzing...")
        tk.Label(classification_frame, text="AI Confidence:", font=('Arial', 10, 'bold'),
                bg='#ffffff').grid(row=2, column=0, sticky='w', padx=10, pady=5)
        tk.Label(classification_frame, textvariable=self.confidence_var, font=('Arial', 10),
                bg='#ffffff', fg='#007bff').grid(row=2, column=1, sticky='w', padx=10, pady=5)
        
        # Specifications
        specs_frame = tk.LabelFrame(parent, text="AI-Extracted Specifications", bg='#ffffff')
        specs_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.specs_tree = ttk.Treeview(specs_frame, columns=('Parameter', 'Value'), show='headings', height=8)
        self.specs_tree.heading('Parameter', text='Parameter')
        self.specs_tree.heading('Value', text='Value')
        self.specs_tree.column('Parameter', width=200)
        self.specs_tree.column('Value', width=300)
        
        specs_scrollbar = ttk.Scrollbar(specs_frame, orient='vertical', command=self.specs_tree.yview)
        self.specs_tree.configure(yscrollcommand=specs_scrollbar.set)
        
        self.specs_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        specs_scrollbar.pack(side='right', fill='y')
    
    def setup_manufacturers_tab(self, parent):
        """Setup manufacturer recommendations tab"""
        
        tk.Label(parent, text="🏭 Top 5 Recommended Manufacturers",
                font=('Arial', 12, 'bold'), bg='#ffffff', fg='#495057').pack(pady=10)
        
        # Manufacturer table
        columns = ('Rank', 'Manufacturer', 'Reputation', 'Availability', 'Notes')
        self.manufacturer_tree = ttk.Treeview(parent, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.manufacturer_tree.heading(col, text=col)
            self.manufacturer_tree.column(col, width=120)
        
        manufacturer_scrollbar = ttk.Scrollbar(parent, orient='vertical', command=self.manufacturer_tree.yview)
        self.manufacturer_tree.configure(yscrollcommand=manufacturer_scrollbar.set)
        
        self.manufacturer_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        manufacturer_scrollbar.pack(side='right', fill='y')
        
        # Load initial manufacturer data
        self.load_manufacturer_recommendations()
    
    def setup_datasheet_tab(self, parent):
        """Setup datasheet analysis tab"""
        
        # Datasheet search and download
        search_frame = tk.LabelFrame(parent, text="Datasheet Search & Download", bg='#ffffff')
        search_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(search_frame, text="🔍 Searching for datasheets...",
                font=('Arial', 10), bg='#ffffff', fg='#6c757d').pack(pady=5)
        
        self.datasheet_progress = ttk.Progressbar(search_frame, mode='indeterminate')
        self.datasheet_progress.pack(fill='x', padx=10, pady=5)
        
        # Found datasheets
        found_frame = tk.LabelFrame(parent, text="Available Datasheets", bg='#ffffff')
        found_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        columns = ('Source', 'Manufacturer', 'Status', 'Action')
        self.datasheet_tree = ttk.Treeview(found_frame, columns=columns, show='headings', height=8)
        
        for col in columns:
            self.datasheet_tree.heading(col, text=col)
            self.datasheet_tree.column(col, width=150)
        
        datasheet_scrollbar = ttk.Scrollbar(found_frame, orient='vertical', command=self.datasheet_tree.yview)
        self.datasheet_tree.configure(yscrollcommand=datasheet_scrollbar.set)
        
        self.datasheet_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        datasheet_scrollbar.pack(side='right', fill='y')
    
    def setup_config_tab(self, parent):
        """Setup search configuration tab"""
        
        # Search refinement
        refinement_frame = tk.LabelFrame(parent, text="Search Term Refinement", bg='#ffffff')
        refinement_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(refinement_frame, text="Refined Search Term:", font=('Arial', 10, 'bold'),
                bg='#ffffff').pack(anchor='w', padx=10, pady=5)
        
        self.refined_search_var = tk.StringVar(value=self.search_term)
        refined_entry = tk.Entry(refinement_frame, textvariable=self.refined_search_var,
                                font=('Arial', 11), width=50)
        refined_entry.pack(anchor='w', padx=10, pady=5)
        
        # Package selection
        package_frame = tk.LabelFrame(parent, text="Package Preferences", bg='#ffffff')
        package_frame.pack(fill='x', padx=10, pady=10)
        
        self.package_var = tk.StringVar(value="Any")
        packages = ["Any", "Through-hole", "SMD", "0603", "0805", "1206", "SOT-23", "TO-220", "DIP"]
        
        for i, package in enumerate(packages):
            row = i // 3
            col = i % 3
            tk.Radiobutton(package_frame, text=package, variable=self.package_var,
                          value=package, font=('Arial', 10), bg='#ffffff').grid(
                          row=row, column=col, sticky='w', padx=10, pady=2)
        
        # Quality settings
        quality_frame = tk.LabelFrame(parent, text="Quality & AI Settings", bg='#ffffff')
        quality_frame.pack(fill='x', padx=10, pady=10)
        
        self.quality_threshold = tk.IntVar(value=70)
        tk.Label(quality_frame, text="Quality Threshold:", font=('Arial', 10, 'bold'),
                bg='#ffffff').pack(anchor='w', padx=10, pady=5)
        
        quality_scale = tk.Scale(quality_frame, from_=0, to=100, orient='horizontal',
                               variable=self.quality_threshold, length=300)
        quality_scale.pack(anchor='w', padx=10, pady=5)
        
        self.use_ai_validation = tk.BooleanVar(value=True)
        tk.Checkbutton(quality_frame, text="Use AI for result validation",
                      variable=self.use_ai_validation, font=('Arial', 10),
                      bg='#ffffff').pack(anchor='w', padx=10, pady=5)
    
    def start_ai_analysis(self):
        """Start AI analysis in background thread"""
        def analyze():
            try:
                self.update_analysis_status("🤖 Connecting to AI analyzer...")
                
                # Test AI connection
                if self.ai_analyzer.active_provider:
                    connection_test = self.ai_analyzer.test_provider_connection(self.ai_analyzer.active_provider)
                    if connection_test["success"]:
                        self.update_analysis_status(f"✅ Connected to {connection_test.get('provider', 'AI')}")
                    else:
                        self.update_analysis_status(f"❌ AI connection failed: {connection_test['error']}")
                        return
                else:
                    self.update_analysis_status("❌ No AI provider configured")
                    return
                
                # Perform component analysis
                self.update_analysis_status("🔍 Analyzing component with AI...")
                self.component_analysis = self.ai_analyzer.analyze_component_query(self.search_term)
                
                # Update UI with results
                self.dialog.after(0, self.update_ai_results)
                
            except Exception as e:
                self.dialog.after(0, lambda: self.update_analysis_status(f"❌ AI analysis failed: {str(e)}"))
        
        threading.Thread(target=analyze, daemon=True).start()
    
    def update_analysis_status(self, message):
        """Update analysis status text"""
        if hasattr(self, 'analysis_status_text'):
            self.analysis_status_text.insert(tk.END, f"{message}\n")
            self.analysis_status_text.see(tk.END)
            self.dialog.update()
    
    def update_ai_results(self):
        """Update UI with AI analysis results"""
        if self.component_analysis:
            # Update header status
            confidence_pct = int(self.component_analysis.confidence_score * 100)
            self.ai_status_label.config(text=f"✅ AI Analysis Complete ({confidence_pct}% confidence)")
            
            # Update classification
            self.component_type_var.set(self.component_analysis.component_type.replace('_', ' ').title())
            self.manufacturer_var.set(self.component_analysis.manufacturer or "Unknown")
            self.confidence_var.set(f"{confidence_pct}%")
            
            # Update specifications
            for item in self.specs_tree.get_children():
                self.specs_tree.delete(item)
            
            for param, value in self.component_analysis.specifications.items():
                self.specs_tree.insert('', 'end', values=(param, str(value)))
            
            # Add package options
            if self.component_analysis.package_options:
                self.specs_tree.insert('', 'end', values=("Package Options", ", ".join(self.component_analysis.package_options)))
            
            # Add alternatives
            if self.component_analysis.alternatives:
                self.specs_tree.insert('', 'end', values=("Alternatives", ", ".join(self.component_analysis.alternatives)))
            
            # Update analysis notes
            if self.component_analysis.analysis_notes:
                self.update_analysis_status(f"📝 Notes: {self.component_analysis.analysis_notes}")
    
    def load_manufacturer_recommendations(self):
        """Load manufacturer recommendations"""
        # Get component type from analysis or use generic
        component_type = "general"
        if self.component_analysis:
            component_type = self.component_analysis.component_type
        
        manufacturers = self.datasheet_manager.get_top_manufacturers(component_type, self.search_term)
        
        # Clear existing items
        for item in self.manufacturer_tree.get_children():
            self.manufacturer_tree.delete(item)
        
        # Add manufacturers
        for i, mfg in enumerate(manufacturers, 1):
            self.manufacturer_tree.insert('', 'end', values=(
                f"#{i}",
                mfg["name"],
                mfg["reputation"],
                mfg["availability"],
                "Recommended for this component type"
            ))
    
    def download_datasheets(self):
        """Start datasheet download process"""
        messagebox.showinfo("Datasheet Download", "Datasheet download feature will be implemented in next update")
    
    def start_ai_search(self):
        """Start AI-enhanced search"""
        self.result = {
            'search_term': self.refined_search_var.get() or self.search_term,
            'original_term': self.search_term,
            'component_type': self.component_analysis.component_type if self.component_analysis else 'general',
            'package': self.package_var.get(),
            'quality_threshold': self.quality_threshold.get(),
            'use_ai_validation': self.use_ai_validation.get(),
            'ai_analysis': self.component_analysis,
            'search_strategy': 'ai_enhanced'
        }
        self.dialog.destroy()
    
    def quick_search(self):
        """Perform quick search without AI"""
        self.result = {
            'search_term': self.search_term,
            'original_term': self.search_term,
            'component_type': 'general',
            'package': 'Any',
            'quality_threshold': 0,
            'use_ai_validation': False,
            'ai_analysis': None,
            'search_strategy': 'quick'
        }
        self.dialog.destroy()
    
    def cancel(self):
        """Cancel the dialog"""
        self.result = None
        self.dialog.destroy()
    
    def get_result(self):
        """Get the dialog result"""
        self.dialog.wait_window()
        return self.result

def show_ai_search_dialog(parent, search_term):
    """Show the AI-enhanced search dialog"""
    dialog = AISearchDialog(parent, search_term)
    return dialog.get_result()

if __name__ == "__main__":
    # Test the AI search dialog
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    result = show_ai_search_dialog(root, "arduino uno")
    
    if result:
        print("AI Search Result:")
        for key, value in result.items():
            print(f"  {key}: {value}")
    else:
        print("Search cancelled")
    
    root.destroy()
