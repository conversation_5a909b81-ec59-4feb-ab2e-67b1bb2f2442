{"cmake.statusbar.advanced": {"debug": {"visibility": "hidden"}, "launch": {"visibility": "hidden"}, "build": {"visibility": "default"}, "buildTarget": {"visibility": "hidden"}}, "cmake.buildBeforeRun": true, "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools", "cortex-debug.openocdPath": "C:/Users/<USER>/pico/openocd/openocd.exe", "files.associations": {"binary_info.h": "c", "bootrom.h": "c", "string.h": "c"}}