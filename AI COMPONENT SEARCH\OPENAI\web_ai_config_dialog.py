#!/usr/bin/env python3
"""
Web AI Configuration Dialog
Configure web-based AI providers like DeepSeek, ChatGPT, Claude, etc.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from typing import Dict, Any

class WebAIConfigDialog:
    """Dialog for configuring web-based AI providers"""
    
    def __init__(self, parent):
        self.parent = parent
        self.result = None
        
        # Load current configuration
        self.config = self.load_config()
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("🌐 Web AI Configuration")
        self.dialog.geometry("800x600")
        self.dialog.configure(bg='#f8f9fa')
        self.dialog.resizable(True, True)
        
        # Center the dialog
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.setup_ui()
        
        # Focus on dialog
        self.dialog.focus_set()
    
    def load_config(self) -> Dict:
        """Load AI configuration"""
        config_file = "ai_config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    return json.load(f)
            except Exception:
                pass
        
        # Return default config
        return {
            "default_provider": "deepseek_web",
            "providers": {
                "deepseek_web": {"enabled": True},
                "perplexity": {"enabled": True},
                "you_chat": {"enabled": True},
                "chatgpt_free": {"enabled": False, "email": "", "password": ""},
                "claude_free": {"enabled": False, "email": "", "password": ""}
            }
        }
    
    def setup_ui(self):
        """Setup the dialog UI"""
        
        # Header
        header_frame = tk.Frame(self.dialog, bg='#007bff', height=60)
        header_frame.pack(fill='x', padx=5, pady=5)
        header_frame.pack_propagate(False)
        
        header_label = tk.Label(header_frame, 
                               text="🌐 Configure Web-based AI Providers",
                               font=('Arial', 14, 'bold'), fg='white', bg='#007bff')
        header_label.pack(expand=True)
        
        # Info label
        info_label = tk.Label(header_frame, 
                             text="Choose your preferred AI providers - no local installation required!",
                             font=('Arial', 10), fg='#e3f2fd', bg='#007bff')
        info_label.pack()
        
        # Main content
        main_frame = tk.Frame(self.dialog, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Create notebook for different categories
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True)
        
        # Free providers (no login)
        free_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(free_frame, text="🆓 Free (No Login)")
        self.setup_free_providers_tab(free_frame)
        
        # Login required providers
        login_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(login_frame, text="🔐 Login Required")
        self.setup_login_providers_tab(login_frame)
        
        # Settings tab
        settings_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(settings_frame, text="⚙️ Settings")
        self.setup_settings_tab(settings_frame)
        
        # Test tab
        test_frame = tk.Frame(notebook, bg='#ffffff')
        notebook.add(test_frame, text="🧪 Test Providers")
        self.setup_test_tab(test_frame)
        
        # Buttons
        button_frame = tk.Frame(self.dialog, bg='#f8f9fa')
        button_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(button_frame, text="💾 Save Configuration", 
                 command=self.save_config, bg='#28a745', fg='white',
                 font=('Arial', 11, 'bold'), padx=20).pack(side='left', padx=5)
        
        tk.Button(button_frame, text="🧪 Test All Providers", 
                 command=self.test_all_providers, bg='#17a2b8', fg='white',
                 font=('Arial', 11)).pack(side='left', padx=5)
        
        tk.Button(button_frame, text="❌ Cancel", 
                 command=self.cancel, bg='#6c757d', fg='white',
                 font=('Arial', 11)).pack(side='right', padx=5)
    
    def setup_free_providers_tab(self, parent):
        """Setup free providers tab"""
        
        tk.Label(parent, text="🆓 Free AI Providers (No Account Required)",
                font=('Arial', 12, 'bold'), bg='#ffffff', fg='#495057').pack(pady=10)
        
        tk.Label(parent, text="These providers work immediately without any setup!",
                font=('Arial', 10), bg='#ffffff', fg='#6c757d').pack(pady=5)
        
        # Provider checkboxes
        self.free_provider_vars = {}
        
        free_providers = [
            ("deepseek_web", "DeepSeek Chat", "Fast and intelligent AI, great for component analysis"),
            ("perplexity", "Perplexity AI", "Research-focused AI with web search capabilities"),
            ("you_chat", "You.com Chat", "General purpose AI with good technical knowledge")
        ]
        
        for provider_id, name, description in free_providers:
            provider_frame = tk.LabelFrame(parent, text=name, font=('Arial', 10, 'bold'),
                                         bg='#ffffff', fg='#495057', padx=10, pady=10)
            provider_frame.pack(fill='x', padx=20, pady=5)
            
            # Checkbox
            var = tk.BooleanVar(value=self.config["providers"].get(provider_id, {}).get("enabled", True))
            self.free_provider_vars[provider_id] = var
            
            checkbox = tk.Checkbutton(provider_frame, text=f"Enable {name}",
                                    variable=var, font=('Arial', 10, 'bold'),
                                    bg='#ffffff', fg='#28a745')
            checkbox.pack(anchor='w')
            
            # Description
            tk.Label(provider_frame, text=description, font=('Arial', 9),
                    bg='#ffffff', fg='#6c757d', wraplength=600).pack(anchor='w', pady=2)
            
            # Status indicator
            status_label = tk.Label(provider_frame, text="✅ Ready to use",
                                  font=('Arial', 9), bg='#ffffff', fg='#28a745')
            status_label.pack(anchor='w')
    
    def setup_login_providers_tab(self, parent):
        """Setup login required providers tab"""
        
        tk.Label(parent, text="🔐 AI Providers Requiring Login",
                font=('Arial', 12, 'bold'), bg='#ffffff', fg='#495057').pack(pady=10)
        
        tk.Label(parent, text="These providers require a free account. Enter your credentials below:",
                font=('Arial', 10), bg='#ffffff', fg='#6c757d').pack(pady=5)
        
        # Provider configurations
        self.login_provider_vars = {}
        self.credential_vars = {}
        
        login_providers = [
            ("chatgpt_free", "ChatGPT Free", "OpenAI's ChatGPT - requires free OpenAI account"),
            ("claude_free", "Claude Free", "Anthropic's Claude - requires free Anthropic account")
        ]
        
        for provider_id, name, description in login_providers:
            provider_frame = tk.LabelFrame(parent, text=name, font=('Arial', 10, 'bold'),
                                         bg='#ffffff', fg='#495057', padx=10, pady=10)
            provider_frame.pack(fill='x', padx=20, pady=5)
            
            # Checkbox
            var = tk.BooleanVar(value=self.config["providers"].get(provider_id, {}).get("enabled", False))
            self.login_provider_vars[provider_id] = var
            
            checkbox = tk.Checkbutton(provider_frame, text=f"Enable {name}",
                                    variable=var, font=('Arial', 10, 'bold'),
                                    bg='#ffffff', fg='#007bff')
            checkbox.pack(anchor='w')
            
            # Description
            tk.Label(provider_frame, text=description, font=('Arial', 9),
                    bg='#ffffff', fg='#6c757d', wraplength=600).pack(anchor='w', pady=2)
            
            # Credentials
            cred_frame = tk.Frame(provider_frame, bg='#ffffff')
            cred_frame.pack(fill='x', pady=5)
            
            # Email
            tk.Label(cred_frame, text="Email:", font=('Arial', 9, 'bold'),
                    bg='#ffffff').grid(row=0, column=0, sticky='w', padx=5)
            
            email_var = tk.StringVar(value=self.config["providers"].get(provider_id, {}).get("email", ""))
            email_entry = tk.Entry(cred_frame, textvariable=email_var, font=('Arial', 9), width=30)
            email_entry.grid(row=0, column=1, padx=5, pady=2)
            
            # Password
            tk.Label(cred_frame, text="Password:", font=('Arial', 9, 'bold'),
                    bg='#ffffff').grid(row=1, column=0, sticky='w', padx=5)
            
            password_var = tk.StringVar(value=self.config["providers"].get(provider_id, {}).get("password", ""))
            password_entry = tk.Entry(cred_frame, textvariable=password_var, font=('Arial', 9), 
                                    width=30, show="*")
            password_entry.grid(row=1, column=1, padx=5, pady=2)
            
            self.credential_vars[provider_id] = {"email": email_var, "password": password_var}
            
            # Warning
            tk.Label(provider_frame, text="⚠️ Credentials are stored locally and used for browser automation",
                    font=('Arial', 8), bg='#ffffff', fg='#dc3545').pack(anchor='w', pady=2)
    
    def setup_settings_tab(self, parent):
        """Setup settings tab"""
        
        tk.Label(parent, text="⚙️ Web Automation Settings",
                font=('Arial', 12, 'bold'), bg='#ffffff', fg='#495057').pack(pady=10)
        
        # Default provider
        default_frame = tk.LabelFrame(parent, text="Default Provider", bg='#ffffff')
        default_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Label(default_frame, text="Primary AI provider to use:",
                font=('Arial', 10), bg='#ffffff').pack(anchor='w', padx=10, pady=5)
        
        self.default_provider_var = tk.StringVar(value=self.config.get("default_provider", "deepseek_web"))
        
        providers = [
            ("deepseek_web", "DeepSeek Chat (Recommended)"),
            ("perplexity", "Perplexity AI"),
            ("you_chat", "You.com Chat"),
            ("chatgpt_free", "ChatGPT Free"),
            ("claude_free", "Claude Free")
        ]
        
        for provider_id, display_name in providers:
            tk.Radiobutton(default_frame, text=display_name, variable=self.default_provider_var,
                          value=provider_id, font=('Arial', 10), bg='#ffffff').pack(anchor='w', padx=20)
        
        # Browser settings
        browser_frame = tk.LabelFrame(parent, text="Browser Settings", bg='#ffffff')
        browser_frame.pack(fill='x', padx=20, pady=10)
        
        self.headless_var = tk.BooleanVar(value=False)
        tk.Checkbutton(browser_frame, text="Run browser in background (headless mode)",
                      variable=self.headless_var, font=('Arial', 10),
                      bg='#ffffff').pack(anchor='w', padx=10, pady=5)
        
        tk.Label(browser_frame, text="Note: Headless mode is faster but you can't see the browser",
                font=('Arial', 9), bg='#ffffff', fg='#6c757d').pack(anchor='w', padx=10)
    
    def setup_test_tab(self, parent):
        """Setup test tab"""
        
        tk.Label(parent, text="🧪 Test AI Providers",
                font=('Arial', 12, 'bold'), bg='#ffffff', fg='#495057').pack(pady=10)
        
        tk.Label(parent, text="Test your configured providers to ensure they work correctly:",
                font=('Arial', 10), bg='#ffffff', fg='#6c757d').pack(pady=5)
        
        # Test results area
        self.test_results_text = tk.Text(parent, height=15, font=('Consolas', 9),
                                       bg='#f8f9fa', fg='#495057', wrap=tk.WORD)
        self.test_results_text.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Test button
        test_btn = tk.Button(parent, text="🚀 Run Tests", command=self.run_tests,
                           bg='#17a2b8', fg='white', font=('Arial', 11, 'bold'))
        test_btn.pack(pady=10)
    
    def run_tests(self):
        """Run tests on configured providers"""
        self.test_results_text.delete('1.0', tk.END)
        self.test_results_text.insert(tk.END, "🧪 Testing AI Providers...\n\n")
        self.dialog.update()
        
        # Test each enabled provider
        enabled_providers = []
        
        # Check free providers
        for provider_id, var in self.free_provider_vars.items():
            if var.get():
                enabled_providers.append(provider_id)
        
        # Check login providers
        for provider_id, var in self.login_provider_vars.items():
            if var.get():
                enabled_providers.append(provider_id)
        
        if not enabled_providers:
            self.test_results_text.insert(tk.END, "❌ No providers enabled for testing\n")
            return
        
        for provider_id in enabled_providers:
            self.test_results_text.insert(tk.END, f"🔍 Testing {provider_id}...\n")
            self.dialog.update()
            
            try:
                # This would test the actual provider
                # For now, simulate the test
                import time
                time.sleep(1)
                
                if provider_id in ["deepseek_web", "perplexity", "you_chat"]:
                    self.test_results_text.insert(tk.END, f"✅ {provider_id}: Ready to use\n")
                else:
                    email = self.credential_vars[provider_id]["email"].get()
                    if email:
                        self.test_results_text.insert(tk.END, f"✅ {provider_id}: Configured with {email}\n")
                    else:
                        self.test_results_text.insert(tk.END, f"⚠️ {provider_id}: Missing credentials\n")
                
            except Exception as e:
                self.test_results_text.insert(tk.END, f"❌ {provider_id}: Error - {str(e)}\n")
            
            self.test_results_text.see(tk.END)
            self.dialog.update()
        
        self.test_results_text.insert(tk.END, f"\n🎉 Testing complete!\n")
    
    def test_all_providers(self):
        """Test all providers"""
        self.run_tests()
    
    def save_config(self):
        """Save configuration and close dialog"""
        # Update config with current settings
        
        # Free providers
        for provider_id, var in self.free_provider_vars.items():
            if provider_id not in self.config["providers"]:
                self.config["providers"][provider_id] = {}
            self.config["providers"][provider_id]["enabled"] = var.get()
        
        # Login providers
        for provider_id, var in self.login_provider_vars.items():
            if provider_id not in self.config["providers"]:
                self.config["providers"][provider_id] = {}
            self.config["providers"][provider_id]["enabled"] = var.get()
            
            # Save credentials
            if provider_id in self.credential_vars:
                self.config["providers"][provider_id]["email"] = self.credential_vars[provider_id]["email"].get()
                self.config["providers"][provider_id]["password"] = self.credential_vars[provider_id]["password"].get()
        
        # Default provider
        self.config["default_provider"] = self.default_provider_var.get()
        
        # Browser settings
        if "web_automation" not in self.config:
            self.config["web_automation"] = {}
        self.config["web_automation"]["headless"] = self.headless_var.get()
        
        # Save to file
        try:
            with open("ai_config.json", 'w') as f:
                json.dump(self.config, f, indent=2)
            
            messagebox.showinfo("Configuration Saved", 
                              "AI configuration has been saved successfully!\n\n"
                              "Your selected providers are now ready to use.")
            
            self.result = True
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("Save Error", f"Failed to save configuration: {str(e)}")
    
    def cancel(self):
        """Cancel the dialog"""
        self.result = False
        self.dialog.destroy()
    
    def get_result(self):
        """Get the dialog result"""
        self.dialog.wait_window()
        return self.result

def show_web_ai_config_dialog(parent):
    """Show the web AI configuration dialog"""
    dialog = WebAIConfigDialog(parent)
    return dialog.get_result()

if __name__ == "__main__":
    # Test the dialog
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    result = show_web_ai_config_dialog(root)
    
    if result:
        print("Configuration saved successfully")
    else:
        print("Configuration cancelled")
    
    root.destroy()
