#!/usr/bin/env python3
"""
Test script to verify space utilization fix in Component Searcher
This script will simulate the workflow and check if the query frame is properly hidden in Step 1
"""

import tkinter as tk
import time
import sys
import os

def test_space_utilization():
    """Test the space utilization fix"""
    print("🧪 Testing Component Searcher Space Utilization Fix...")
    
    try:
        # Import the main application
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from component_searcher import ComponentSearcher
        
        # Create the application
        app = ComponentSearcher()
        
        # Test Step 1 - Component Spec Analysis
        print("📋 Testing Step 1 (Component Spec Analysis)...")
        app.go_to_workflow_step(1)
        
        # Check if query frame is hidden (for grid-managed widgets, check if it's in grid)
        try:
            grid_info = app.query_frame.grid_info()
            query_frame_visible = bool(grid_info)  # Empty dict means not in grid (hidden)
            if not query_frame_visible:
                print("✅ SUCCESS: Query frame is properly hidden in Step 1")
                print("✅ This should maximize space for analysis results")
            else:
                print("❌ ISSUE: Query frame is still visible in Step 1")
                print("❌ This wastes valuable screen space")
        except Exception as e:
            print(f"⚠️  Could not check query frame visibility: {e}")
        
        # Test Step 2 - Should show query frame again
        print("\n🔍 Testing Step 2 (Supplier Search)...")
        app.go_to_workflow_step(2)
        
        try:
            grid_info = app.query_frame.grid_info()
            query_frame_visible = bool(grid_info)  # Empty dict means not in grid (hidden)
            if query_frame_visible:
                print("✅ SUCCESS: Query frame is properly shown in Step 2")
            else:
                print("❌ ISSUE: Query frame is hidden in Step 2 (should be visible)")
        except Exception as e:
            print(f"⚠️  Could not check query frame visibility: {e}")
        
        # Test analysis results creation
        print("\n📊 Testing analysis results space utilization...")
        
        # Simulate having analysis data
        import datetime
        app.current_analysis_data = {
            'component': '10kΩ resistor',
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'queries': {
                'What is 10kΩ resistor?': 'A passive electronic component...',
                'Applications?': 'Voltage dividers, pull-up resistors...',
                'Specifications?': 'Resistance: 10kΩ ± tolerance...',
                'Manufacturers?': 'Vishay, Yageo, Panasonic...'
            }
        }
        
        # Create analysis table
        app.show_analysis_table()
        
        # Go back to Step 1 to see the results
        app.go_to_workflow_step(1)
        
        # Check if analysis frame is visible and properly sized
        if hasattr(app, 'analysis_results_frame'):
            try:
                analysis_visible = app.analysis_results_frame.winfo_viewable()
                if analysis_visible:
                    print("✅ SUCCESS: Analysis results frame is visible in Step 1")
                    
                    # Get frame dimensions
                    width = app.analysis_results_frame.winfo_width()
                    height = app.analysis_results_frame.winfo_height()
                    print(f"📏 Analysis frame size: {width}x{height} pixels")
                    
                    if height > 400:  # Should be reasonably large
                        print("✅ SUCCESS: Analysis frame has good height utilization")
                    else:
                        print("⚠️  Analysis frame might be too small")
                        
                else:
                    print("❌ ISSUE: Analysis results frame is not visible")
            except Exception as e:
                print(f"⚠️  Could not check analysis frame: {e}")
        else:
            print("⚠️  Analysis results frame not created yet")
        
        print("\n🎯 SUMMARY:")
        print("The space utilization fix should:")
        print("1. Hide the large query configuration area in Step 1")
        print("2. Allow analysis results to expand and fill available space")
        print("3. Show query configuration again in Steps 2-5")
        print("4. Minimize padding/margins for maximum space usage")
        
        print("\n✨ Test completed! Check the application window to verify the fix.")
        print("💡 In Step 1, you should see much more space for analysis results.")
        
        # Keep the application running for manual inspection
        print("\n🔍 Application is running - manually test the workflow steps...")
        print("Press Ctrl+C to exit when done testing.")
        
        app.root.mainloop()
        
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_space_utilization()
