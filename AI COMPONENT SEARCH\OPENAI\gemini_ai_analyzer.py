#!/usr/bin/env python3
"""
Gemini AI Analyzer
AI-powered component analysis using Google Gemini API
Fast, reliable, and free alternative to local AI
"""

import json
import os
import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

@dataclass
class ComponentAnalysis:
    """Component analysis result from Gemini AI"""
    component_type: str
    manufacturer: str
    part_number: str
    specifications: Dict[str, Any]
    package_options: List[str]
    alternatives: List[str]
    applications: List[str]
    confidence_score: float
    analysis_notes: str
    raw_response: str

class GeminiAIAnalyzer:
    """AI-powered component analyzer using Gemini API"""
    
    def __init__(self):
        self.config = self.load_config()
        self.model = None
        self.api_available = False
        self.setup_gemini()
    
    def load_config(self) -> Dict:
        """Load Gemini configuration"""
        config_file = "gemini_config.json"
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    return json.load(f)
            except Exception:
                pass
        
        # Default config
        return {
            "gemini_api_key": "",
            "working_model": "gemini-1.5-flash",
            "status": "not_configured"
        }
    
    def setup_gemini(self):
        """Setup Gemini AI model"""
        try:
            api_key = self.config.get("gemini_api_key", "")
            
            if not api_key:
                print("⚠️ Gemini API key not configured")
                return False
            
            # Import and configure Gemini
            import google.generativeai as genai
            genai.configure(api_key=api_key)
            
            # Create model
            model_name = self.config.get("working_model", "gemini-1.5-flash")
            self.model = genai.GenerativeModel(model_name)
            
            # Test connection
            test_response = self.model.generate_content("Test")
            
            self.api_available = True
            print(f"✅ Gemini AI ready: {model_name}")
            return True
            
        except ImportError:
            print("❌ Google Generative AI library not installed")
            print("📦 Install with: pip install google-generativeai")
            return False
        except Exception as e:
            print(f"⚠️ Gemini setup failed: {e}")
            return False
    
    def is_available(self) -> bool:
        """Check if Gemini AI is available"""
        return self.api_available and self.model is not None
    
    def analyze_component(self, component_query: str) -> ComponentAnalysis:
        """Analyze a component using Gemini AI"""
        if not self.is_available():
            return self._create_fallback_analysis(component_query, "Gemini AI not available")
        
        try:
            # Create comprehensive analysis prompt
            prompt = self._build_analysis_prompt(component_query)
            
            # Get AI response
            response = self.model.generate_content(prompt)
            raw_response = response.text
            
            # Parse the response
            analysis = self._parse_ai_response(raw_response, component_query)
            analysis.raw_response = raw_response
            
            return analysis
            
        except Exception as e:
            return self._create_fallback_analysis(component_query, f"Analysis failed: {str(e)}")

    def ask_question(self, question: str, max_lines: int = 8) -> str:
        """Ask a specific question to Gemini AI and get a direct text response"""
        if not self.is_available():
            return "Gemini AI not available"

        try:
            # Create a simple prompt for the specific question with length constraints
            prompt = f"""
You are an electronics expert. Please answer this question clearly and concisely:

{question}

IMPORTANT FORMATTING REQUIREMENTS:
- Keep response to maximum {max_lines} lines
- Use simple, clean text without excessive markdown formatting
- Avoid multiple asterisks (**) or complex formatting
- Use simple bullet points (•) for lists, not markdown bullets
- Be concise but complete
- Focus on practical information for engineers/hobbyists
- Write in clear, readable sentences
- Avoid technical jargon unless necessary
"""

            # Get AI response
            response = self.model.generate_content(prompt)
            raw_response = response.text.strip()

            # Validate and limit response length
            lines = raw_response.split('\n')
            if len(lines) > max_lines:
                # Keep first max_lines and add continuation indicator
                limited_lines = lines[:max_lines]
                limited_response = '\n'.join(limited_lines)
                if not limited_response.endswith('.'):
                    limited_response += '...'
                return limited_response

            return raw_response

        except Exception as e:
            return f"Error getting AI response: {str(e)}"

    def _build_analysis_prompt(self, component_query: str) -> str:
        """Build comprehensive analysis prompt for Gemini"""
        return f"""
Analyze this electronics component: "{component_query}"

Please provide a comprehensive technical analysis in the following structured format:

**COMPONENT TYPE:** [resistor/capacitor/IC/microcontroller/sensor/etc.]

**MANUFACTURER:** [likely manufacturer if identifiable, or "Various" if generic]

**PART NUMBER:** [specific part number if identifiable, or refined component name]

**KEY SPECIFICATIONS:**
- Parameter 1: Value
- Parameter 2: Value
- Parameter 3: Value

**PACKAGE OPTIONS:** [list common package types like DIP, SMD, SOT-23, etc.]

**TYPICAL APPLICATIONS:**
- Application 1
- Application 2
- Application 3

**COMPATIBLE ALTERNATIVES:**
- Alternative 1
- Alternative 2
- Alternative 3

**CONFIDENCE:** [High/Medium/Low based on how well you can identify this component]

**TECHNICAL NOTES:** [Any additional important technical information]

Focus on providing accurate, practical information that would help an electronics engineer or hobbyist make informed decisions about component selection and sourcing.
"""
    
    def _parse_ai_response(self, response: str, original_query: str) -> ComponentAnalysis:
        """Parse Gemini AI response into structured data"""
        try:
            # Extract sections using regex patterns
            component_type = self._extract_section(response, r"\*\*COMPONENT TYPE:\*\*\s*(.+?)(?:\n|$)", "unknown")
            manufacturer = self._extract_section(response, r"\*\*MANUFACTURER:\*\*\s*(.+?)(?:\n|$)", "Unknown")
            part_number_raw = self._extract_section(response, r"\*\*PART NUMBER:\*\*\s*(.+?)(?:\n|$)", original_query)
            # Keep part numbers reasonable for search
            part_number = part_number_raw[:50] if len(part_number_raw) > 50 else part_number_raw
            
            # Extract specifications
            specs_section = self._extract_section(response, r"\*\*KEY SPECIFICATIONS:\*\*(.*?)(?:\*\*|$)", "", multiline=True)
            specifications = self._parse_specifications(specs_section)
            
            # Extract lists
            package_options = self._extract_list(response, r"\*\*PACKAGE OPTIONS:\*\*\s*(.+?)(?:\n\*\*|$)")
            applications = self._extract_list(response, r"\*\*TYPICAL APPLICATIONS:\*\*(.*?)(?:\*\*|$)", multiline=True)
            alternatives = self._extract_list(response, r"\*\*COMPATIBLE ALTERNATIVES:\*\*(.*?)(?:\*\*|$)", multiline=True)
            
            # Extract confidence
            confidence_text = self._extract_section(response, r"\*\*CONFIDENCE:\*\*\s*(.+?)(?:\n|$)", "Medium")
            confidence_score = self._parse_confidence(confidence_text)
            
            # Extract notes
            notes = self._extract_section(response, r"\*\*TECHNICAL NOTES:\*\*\s*(.+?)(?:\n\*\*|$)", "", multiline=True)
            
            return ComponentAnalysis(
                component_type=component_type.strip(),
                manufacturer=manufacturer.strip(),
                part_number=part_number.strip(),
                specifications=specifications,
                package_options=package_options,
                alternatives=alternatives,
                applications=applications,
                confidence_score=confidence_score,
                analysis_notes=notes.strip(),
                raw_response=response
            )
            
        except Exception as e:
            # Fallback parsing if structured parsing fails
            return ComponentAnalysis(
                component_type=self._guess_component_type(original_query),
                manufacturer="Unknown",
                part_number=original_query,
                specifications={"raw_analysis": response[:500] + "..."},
                package_options=["Various"],
                alternatives=[],
                applications=["General purpose"],
                confidence_score=0.5,
                analysis_notes=f"Parsing failed: {str(e)}",
                raw_response=response
            )
    
    def _extract_section(self, text: str, pattern: str, default: str = "", multiline: bool = False) -> str:
        """Extract a section from the AI response"""
        flags = re.DOTALL if multiline else 0
        match = re.search(pattern, text, flags)
        return match.group(1).strip() if match else default
    
    def _extract_list(self, text: str, pattern: str, multiline: bool = False) -> List[str]:
        """Extract a list from the AI response"""
        section = self._extract_section(text, pattern, "", multiline)
        if not section:
            return []
        
        # Split by lines and clean up
        items = []
        for line in section.split('\n'):
            line = line.strip()
            if line and not line.startswith('**'):
                # Remove bullet points and clean
                line = re.sub(r'^[-•*]\s*', '', line)
                if line:
                    items.append(line)
        
        return items[:5]  # Limit to top 5 items
    
    def _parse_specifications(self, specs_text: str) -> Dict[str, str]:
        """Parse specifications from text"""
        specs = {}
        
        for line in specs_text.split('\n'):
            line = line.strip()
            if ':' in line and not line.startswith('**'):
                # Remove bullet points
                line = re.sub(r'^[-•*]\s*', '', line)
                
                parts = line.split(':', 1)
                if len(parts) == 2:
                    key = parts[0].strip()
                    value = parts[1].strip()
                    if key and value:
                        specs[key] = value
        
        return specs
    
    def _parse_confidence(self, confidence_text: str) -> float:
        """Parse confidence level to numeric score"""
        confidence_text = confidence_text.lower()
        
        if 'high' in confidence_text:
            return 0.9
        elif 'medium' in confidence_text:
            return 0.7
        elif 'low' in confidence_text:
            return 0.4
        else:
            return 0.6  # Default
    
    def _guess_component_type(self, query: str) -> str:
        """Guess component type from query"""
        query_lower = query.lower()
        
        type_keywords = {
            'resistor': ['resistor', 'ohm', 'kohm', 'mohm'],
            'capacitor': ['capacitor', 'cap', 'uf', 'pf', 'nf'],
            'microcontroller': ['arduino', 'esp32', 'esp8266', 'atmega', 'pic'],
            'ic': ['lm358', 'lm741', 'ne555', '555', 'op amp', 'opamp'],
            'sensor': ['sensor', 'temperature', 'humidity', 'pressure'],
            'transistor': ['transistor', 'mosfet', 'bjt'],
            'diode': ['diode', 'led'],
            'connector': ['connector', 'header', 'socket']
        }
        
        for comp_type, keywords in type_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                return comp_type
        
        return 'component'
    
    def _create_fallback_analysis(self, component_query: str, error_msg: str) -> ComponentAnalysis:
        """Create fallback analysis when AI is not available"""
        return ComponentAnalysis(
            component_type=self._guess_component_type(component_query),
            manufacturer="Unknown",
            part_number=component_query,
            specifications={"note": "AI analysis not available"},
            package_options=["Various"],
            alternatives=[],
            applications=["General purpose"],
            confidence_score=0.1,
            analysis_notes=error_msg,
            raw_response=""
        )
    
    def get_manufacturer_recommendations(self, component_type: str, component_name: str) -> List[Dict[str, str]]:
        """Get AI-powered manufacturer recommendations"""
        if not self.is_available():
            return self._get_fallback_manufacturers(component_type)
        
        try:
            prompt = f"""
For the component type "{component_type}" (specifically "{component_name}"), recommend the top 5 manufacturers.

For each manufacturer, provide:
- Name
- Reputation (Excellent/Good/Fair)
- Availability (High/Medium/Low)
- Brief note about why they're recommended

Format as a simple list:
1. Manufacturer Name - Reputation - Availability - Note
2. Manufacturer Name - Reputation - Availability - Note
etc.

Focus on manufacturers that are commonly available in India and have good reputation for this component type.
"""
            
            response = self.model.generate_content(prompt)
            return self._parse_manufacturer_recommendations(response.text)
            
        except Exception as e:
            print(f"⚠️ Manufacturer recommendation failed: {e}")
            return self._get_fallback_manufacturers(component_type)
    
    def _parse_manufacturer_recommendations(self, response: str) -> List[Dict[str, str]]:
        """Parse manufacturer recommendations from AI response"""
        manufacturers = []
        
        for line in response.split('\n'):
            line = line.strip()
            if re.match(r'^\d+\.', line):  # Lines starting with numbers
                # Remove number prefix
                line = re.sub(r'^\d+\.\s*', '', line)
                
                # Try to parse: Name - Reputation - Availability - Note
                parts = [part.strip() for part in line.split(' - ')]
                
                if len(parts) >= 3:
                    manufacturers.append({
                        "name": parts[0],
                        "reputation": parts[1],
                        "availability": parts[2],
                        "notes": parts[3] if len(parts) > 3 else "Recommended manufacturer"
                    })
        
        return manufacturers[:5]  # Top 5
    
    def _get_fallback_manufacturers(self, component_type: str) -> List[Dict[str, str]]:
        """Fallback manufacturer recommendations"""
        fallback_db = {
            "resistor": [
                {"name": "Vishay", "reputation": "Excellent", "availability": "High", "notes": "Industry standard"},
                {"name": "Yageo", "reputation": "Good", "availability": "High", "notes": "Cost effective"},
                {"name": "Panasonic", "reputation": "Excellent", "availability": "Medium", "notes": "High quality"},
            ],
            "capacitor": [
                {"name": "Murata", "reputation": "Excellent", "availability": "High", "notes": "Premium quality"},
                {"name": "TDK", "reputation": "Excellent", "availability": "High", "notes": "Reliable"},
                {"name": "KEMET", "reputation": "Good", "availability": "Medium", "notes": "Wide range"},
            ],
            "microcontroller": [
                {"name": "Arduino", "reputation": "Excellent", "availability": "High", "notes": "Original boards"},
                {"name": "SparkFun", "reputation": "Good", "availability": "Medium", "notes": "Quality clones"},
                {"name": "Adafruit", "reputation": "Good", "availability": "Medium", "notes": "Educational focus"},
            ]
        }
        
        return fallback_db.get(component_type, [
            {"name": "Generic", "reputation": "Variable", "availability": "High", "notes": "Various manufacturers"}
        ])

# Global instance
gemini_analyzer = GeminiAIAnalyzer()

def get_gemini_analyzer() -> GeminiAIAnalyzer:
    """Get the global Gemini analyzer instance"""
    return gemini_analyzer

if __name__ == "__main__":
    # Test the Gemini analyzer
    analyzer = get_gemini_analyzer()
    
    if analyzer.is_available():
        print("🤖 Testing Gemini AI Analyzer...")
        
        test_components = ["arduino uno", "10k resistor", "LM358"]
        
        for component in test_components:
            print(f"\n🔍 Analyzing: {component}")
            analysis = analyzer.analyze_component(component)
            
            print(f"  Type: {analysis.component_type}")
            print(f"  Manufacturer: {analysis.manufacturer}")
            print(f"  Confidence: {analysis.confidence_score:.2f}")
            print(f"  Packages: {', '.join(analysis.package_options[:3])}")
    else:
        print("❌ Gemini AI not available")
        print("🔧 Check your API key configuration")
