#!/usr/bin/env python3
"""
SIMPLE WORKING TESTER - NO COMPLEX CONTROLS
Just shows what's happening and tests pages
"""

import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class SimpleWorkingTester:
    """Simple tester that actually works"""
    
    def __init__(self, product_url="http://localhost:8080"):
        self.product_url = product_url
        self.driver = None
        self.wait = None
        
    def setup_browser(self):
        """Setup browser"""
        print("Setting up Simple Working Tester...")
        
        options = Options()
        options.add_argument("--start-maximized")
        options.add_argument("--disable-web-security")
        
        try:
            self.driver = webdriver.Chrome(options=options)
            self.wait = WebDriverWait(self.driver, 15)
            print("SUCCESS: Browser ready!")
            return True
        except Exception as e:
            print(f"ERROR: Browser setup failed: {e}")
            return False
    
    def inject_simple_overlay(self):
        """Inject simple overlay"""
        overlay_html = """
        <div id="simple-overlay" style="
            position: fixed; top: 20px; right: 20px; width: 350px;
            background: rgba(0,0,0,0.8); color: white; padding: 15px;
            border-radius: 8px; font-family: monospace; font-size: 11px;
            z-index: 10000; border: 2px solid #00ff00;
        ">
            <div style="color: #00ff00; font-size: 13px; font-weight: bold; margin-bottom: 10px;">SIMPLE TESTER</div>
            
            <div style="margin-bottom: 8px;">
                <div style="color: #ffff00; font-size: 10px;">OBJECTIVES:</div>
                <div style="font-size: 9px;">• Test navigation • Test forms • Test AI • Test UI</div>
            </div>
            
            <div style="margin-bottom: 8px;">
                <div style="color: #00ffff; font-size: 10px;">PROGRESS:</div>
                <div id="progress-info" style="font-size: 12px; font-weight: bold;">
                    Page: <span id="current-page" style="color: #00ff00;">0</span>/6
                </div>
                <div style="font-size: 11px; margin-top: 3px;">
                    <span style="color: #00ff00;">✓ <span id="passed">0</span></span> | 
                    <span style="color: #ff0000;">✗ <span id="failed">0</span></span>
                </div>
            </div>
            
            <div style="margin-bottom: 8px;">
                <div style="color: #ff6600; font-size: 10px;">CURRENT:</div>
                <div id="current-action" style="font-size: 11px; font-weight: bold; color: #fff;">Starting...</div>
            </div>
            
            <div style="font-size: 9px; color: #ccc; max-height: 80px; overflow-y: auto;">
                <div>LOG:</div>
                <div id="log-content">Ready to start testing...</div>
            </div>
        </div>
        """
        
        try:
            self.driver.execute_script(f"""
                if (!document.getElementById('simple-overlay')) {{
                    document.body.insertAdjacentHTML('beforeend', `{overlay_html}`);
                }}
            """)
        except:
            pass
    
    def update_overlay(self, current_action="", page_num=0, passed=0, failed=0, log_msg=""):
        """Update overlay"""
        try:
            if current_action:
                self.driver.execute_script(f"""
                    var el = document.getElementById('current-action');
                    if (el) el.textContent = '{current_action}';
                """)
            
            if page_num > 0:
                self.driver.execute_script(f"""
                    var el = document.getElementById('current-page');
                    if (el) el.textContent = '{page_num}';
                """)
            
            self.driver.execute_script(f"""
                var passedEl = document.getElementById('passed');
                var failedEl = document.getElementById('failed');
                if (passedEl) passedEl.textContent = '{passed}';
                if (failedEl) failedEl.textContent = '{failed}';
            """)
            
            if log_msg:
                escaped_msg = log_msg.replace("'", "\\'")
                self.driver.execute_script(f"""
                    var logEl = document.getElementById('log-content');
                    if (logEl) {{
                        logEl.innerHTML += '<br>[' + new Date().toLocaleTimeString() + '] {escaped_msg}';
                        logEl.scrollTop = logEl.scrollHeight;
                    }}
                """)
        except:
            pass
    
    def test_page_simple(self, page_url, page_name):
        """Test page simply"""
        print(f"Testing page: {page_name}")
        
        try:
            full_url = f"{self.product_url}{page_url}"
            self.driver.get(full_url)
            time.sleep(2)
            self.inject_simple_overlay()
            
            self.update_overlay(f"Testing {page_name}", log_msg=f"Testing {page_name}")
            
            # Simple test - check page loads
            if "404" in self.driver.page_source or "Error" in self.driver.title:
                self.update_overlay(log_msg=f"FAILED: {page_name} - Page error")
                return False
            else:
                self.update_overlay(log_msg=f"PASSED: {page_name} - Page loaded OK")
                return True
                
        except Exception as e:
            self.update_overlay(log_msg=f"ERROR: {page_name} - {str(e)}")
            return False
    
    def run_simple_tests(self):
        """Run simple tests"""
        print("SIMPLE WORKING TESTER")
        print("=" * 40)
        print("Testing all pages simply")
        print("=" * 40)
        
        if not self.setup_browser():
            return False
        
        # Test pages
        pages = [
            ('/', 'Dashboard'),
            ('/components', 'Components List'),
            ('/components/add', 'Add Component'),
            ('/ai-research', 'AI Research Lab'),
            ('/analytics', 'Analytics'),
            ('/system-info', 'System Info')
        ]
        
        passed = 0
        failed = 0
        
        try:
            # Navigate to dashboard first
            self.driver.get(self.product_url)
            time.sleep(2)
            self.inject_simple_overlay()
            
            self.update_overlay("Starting tests...", 0, 0, 0, "Starting comprehensive testing...")
            time.sleep(2)
            
            for i, (page_url, page_name) in enumerate(pages):
                self.update_overlay(f"Testing {page_name}", i+1, passed, failed)
                
                result = self.test_page_simple(page_url, page_name)
                
                if result:
                    passed += 1
                    print(f"  ✓ PASSED: {page_name}")
                else:
                    failed += 1
                    print(f"  ✗ FAILED: {page_name}")
                
                self.update_overlay(f"Completed {page_name}", i+1, passed, failed)
                time.sleep(2)  # Let you see each test
            
            # Final results
            total = len(pages)
            success_rate = (passed / total * 100) if total > 0 else 0
            
            self.update_overlay(f"Testing complete!", total, passed, failed, 
                              f"FINAL: {passed}/{total} passed ({success_rate:.1f}%)")
            
            print(f"\nFINAL RESULTS:")
            print(f"Total Pages: {total}")
            print(f"Passed: {passed}")
            print(f"Failed: {failed}")
            print(f"Success Rate: {success_rate:.1f}%")
            
            if success_rate >= 80:
                print("✓ TESTING SUCCESSFUL!")
            else:
                print("✗ TESTING NEEDS ATTENTION!")
            
            print("\nBrowser will stay open for 2 minutes for review...")
            print("You can see the overlay with results!")
            
            # Keep browser open for review
            for i in range(120):  # 2 minutes
                time.sleep(1)
                if i % 10 == 0:  # Every 10 seconds
                    remaining = 120 - i
                    print(f"Browser closing in {remaining} seconds... (Ctrl+C to close now)")
            
            return success_rate >= 80
            
        except KeyboardInterrupt:
            print("\nTesting interrupted by user")
            return False
        except Exception as e:
            print(f"Testing failed: {e}")
            return False
        
        finally:
            if self.driver:
                print("Closing browser...")
                self.driver.quit()

def main():
    """Main function"""
    print("Simple Working Tester")
    print("=" * 30)
    
    # Check if product server is running
    product_url = "http://localhost:8080"
    try:
        response = requests.get(product_url, timeout=5)
        print(f"✓ Product server running at {product_url}")
    except:
        print(f"✗ Product server NOT running at {product_url}")
        print("Please start the product server first:")
        print("  cd ComponentAI")
        print("  python launch_admin.py")
        return False
    
    tester = SimpleWorkingTester(product_url)
    success = tester.run_simple_tests()
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
