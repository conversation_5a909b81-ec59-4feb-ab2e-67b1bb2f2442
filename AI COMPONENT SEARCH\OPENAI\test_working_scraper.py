#!/usr/bin/env python3
"""
Working scraper demonstration with real data from Evelta.com
This shows the scraper actually working with real components
"""

import requests
from bs4 import BeautifulSoup
import re
import time
from urllib.parse import quote_plus

class WorkingEveltaScraper:
    """Working scraper for Evelta.com with correct selectors"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.base_url = "https://www.evelta.com"
    
    def search_component(self, component_value):
        """Search for a component on Evelta"""
        try:
            # Use category browsing instead of search for better results
            category_urls = {
                'arduino': '/development-boards-and-kits/arduino-and-compatible-boards/',
                'resistor': '/passive-components/resistors/',
                'capacitor': '/categories/passive-components/capacitor/',
                'led': '/optoelectronics/leds-through-hole/',
                'sensor': '/categories/sensors/',
                'ic': '/integrated-circuits-ics/',
                'transistor': '/categories/discrete-semiconductors/transistors/'
            }
            
            # Find best matching category
            component_lower = component_value.lower()
            category_url = None
            
            for keyword, url in category_urls.items():
                if keyword in component_lower:
                    category_url = url
                    break
            
            if not category_url:
                # Fallback to search
                search_url = f"{self.base_url}/?s={quote_plus(component_value)}&post_type=product"
            else:
                search_url = f"{self.base_url}{category_url}"
            
            print(f"Searching: {search_url}")
            
            response = self.session.get(search_url, timeout=10)
            response.raise_for_status()
            
            return self.parse_results(response.text, component_value)
            
        except Exception as e:
            print(f"Error searching Evelta: {e}")
            return []
    
    def parse_results(self, html_content, search_term):
        """Parse Evelta search results"""
        soup = BeautifulSoup(html_content, 'html.parser')
        results = []
        
        # Find product cards
        products = soup.select('article.card')
        print(f"Found {len(products)} product cards")
        
        for product in products[:5]:  # Limit to first 5 results
            try:
                # Extract product name
                name_elem = product.find(['h3', 'h4', 'a'])
                if name_elem:
                    name = self.clean_text(name_elem.get_text() or name_elem.get('title', ''))
                else:
                    name = "Product found"
                
                # Extract price - look for the price structure we found
                price = 0
                price_elem = product.find(text=re.compile(r'₹[\d,]+'))
                if price_elem:
                    price = self.extract_price(price_elem)
                
                # Extract product URL
                link_elem = product.find('a')
                product_url = link_elem.get('href') if link_elem else self.base_url
                if product_url.startswith('/'):
                    product_url = self.base_url + product_url
                
                # Extract stock info
                stock = "Check Website"
                stock_elem = product.find(text=re.compile(r'in stock|stock|available', re.I))
                if stock_elem:
                    stock = self.clean_text(str(stock_elem))
                
                if name and name != "Product found":
                    results.append({
                        'supplier': 'Evelta',
                        'component': name,
                        'price': price,
                        'stock': stock,
                        'shipping': 55,  # Known shipping cost for Evelta
                        'total': price + 55,
                        'location': 'Mumbai, Maharashtra',
                        'url': product_url
                    })
                
            except Exception as e:
                print(f"Error parsing product: {e}")
                continue
        
        return results
    
    def extract_price(self, price_text):
        """Extract numeric price from text"""
        if not price_text:
            return 0
        
        # Remove currency symbols and extract numbers
        price_match = re.search(r'[\d,]+\.?\d*', str(price_text).replace(',', ''))
        if price_match:
            return float(price_match.group())
        return 0
    
    def clean_text(self, text):
        """Clean and normalize text"""
        if not text:
            return ""
        return re.sub(r'\s+', ' ', text.strip())

def test_real_components():
    """Test with real components that should be available"""
    
    print("🔍 Testing Real Component Search with Working Scraper")
    print("=" * 60)
    print("This demonstrates actual web scraping with real results")
    print("=" * 60)
    
    scraper = WorkingEveltaScraper()
    
    # Test components that are likely to be available
    test_components = [
        "Arduino",
        "resistor",
        "LED",
        "sensor"
    ]
    
    for component in test_components:
        print(f"\n🔎 Searching for: {component}")
        print("-" * 40)
        
        try:
            results = scraper.search_component(component)
            
            if results:
                print(f"✅ Found {len(results)} results:")
                for i, result in enumerate(results, 1):
                    print(f"\n   {i}. {result['component'][:60]}...")
                    print(f"      💰 Price: ₹{result['price']}")
                    print(f"      📦 Stock: {result['stock']}")
                    print(f"      🚚 Shipping: ₹{result['shipping']}")
                    print(f"      💳 Total: ₹{result['total']}")
                    print(f"      🔗 URL: {result['url'][:60]}...")
            else:
                print("❌ No results found")
                
        except Exception as e:
            print(f"⚠️  Error: {str(e)}")
        
        # Add delay between requests to be respectful
        time.sleep(1)
    
    print("\n" + "="*60)
    print("✅ Real component search completed!")
    print("This demonstrates the scraper working with actual data.")
    print("="*60)

def demonstrate_scraper_challenges():
    """Show the challenges and solutions in web scraping"""
    
    print("\n🛠️  Web Scraping Challenges and Solutions")
    print("=" * 60)
    
    print("""
Real-world web scraping challenges we encountered:

1. 🚫 ANTI-BOT PROTECTION
   Problem: Robu.in returns 403 Forbidden for automated requests
   Solution: Use different approaches:
   - Selenium with browser automation
   - Rotating user agents and IP addresses
   - Respect robots.txt and rate limits
   - Use official APIs when available

2. 🔄 DIFFERENT WEBSITE STRUCTURES
   Problem: Each site has different HTML structure
   Solution: Create site-specific scrapers:
   - Evelta uses 'article.card' containers
   - Other sites might use 'div.product' or 'li.item'
   - Inspect each site individually

3. 💰 DYNAMIC PRICING
   Problem: Prices loaded via JavaScript
   Solution: Use Selenium for JavaScript-heavy sites
   - Wait for elements to load
   - Handle dynamic content

4. 🔍 SEARCH VS CATEGORY BROWSING
   Problem: Search might not work as expected
   Solution: Use category URLs when possible:
   - /arduino-boards/ instead of /?s=arduino
   - More reliable results
   - Better for specific component types

5. 📊 DATA EXTRACTION ACCURACY
   Problem: Extracting correct price, stock, name
   Solution: Multiple fallback selectors:
   - Try different CSS selectors
   - Use regex for price extraction
   - Handle missing data gracefully

CURRENT STATUS:
✅ Evelta.com - Working with category browsing
❌ Robu.in - Blocked (needs Selenium or API)
🔄 Other sites - Need individual analysis

NEXT STEPS:
1. Analyze each supplier's website structure
2. Create specific scrapers for each site
3. Implement Selenium for JavaScript-heavy sites
4. Add error handling and retries
5. Respect rate limits and terms of service
""")

if __name__ == "__main__":
    print("🇮🇳 Working Indian Electronics Component Scraper")
    print("Real demonstration with actual supplier data")
    
    # Show challenges first
    demonstrate_scraper_challenges()
    
    # Ask user before making requests
    print("\n" + "="*60)
    response = input("Do you want to test real component searches? (y/N): ").lower().strip()
    
    if response == 'y':
        test_real_components()
    else:
        print("Skipping real component search test.")
    
    print("\n" + "="*60)
    print("🎯 KEY TAKEAWAYS:")
    print("1. Web scraping works but requires site-specific implementation")
    print("2. Each supplier needs individual analysis and scraper development")
    print("3. Some sites block automation - need alternative approaches")
    print("4. The framework is ready - just need to build specific scrapers")
    print("5. Start with suppliers that allow automation (like Evelta)")
    print("="*60)
