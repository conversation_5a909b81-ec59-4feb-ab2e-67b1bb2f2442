{% extends "base.html" %}

{% block title %}{{ component.name }} - ComponentAI Admin{% endblock %}
{% block page_title %}{{ component.name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-info-circle"></i> Component Details</h6>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th>ID:</th>
                        <td>{{ component.id }}</td>
                    </tr>
                    <tr>
                        <th>Name:</th>
                        <td>{{ component.name }}</td>
                    </tr>
                    <tr>
                        <th>Type:</th>
                        <td>{{ component.type }}</td>
                    </tr>
                    <tr>
                        <th>Description:</th>
                        <td>{{ component.description }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-gear"></i> Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('edit_component', component_id=component.id) }}" class="btn btn-warning">
                        <i class="bi bi-pencil"></i> Edit Component
                    </a>
                    <form method="POST" action="{{ url_for('delete_component', component_id=component.id) }}" 
                          onsubmit="return confirm('Are you sure you want to delete this component?')">
                        <button type="submit" class="btn btn-danger w-100">
                            <i class="bi bi-trash"></i> Delete Component
                        </button>
                    </form>
                    <a href="{{ url_for('components_list') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Components
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
