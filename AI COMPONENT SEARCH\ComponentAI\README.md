# 🔧 ComponentAI - AI-Powered Component Search Tool

## 📋 Overview
ComponentAI is an AI-powered electronic component search and management system with real AI/LLM integration.

## 📁 Clean Product Structure
```
ComponentAI/                           # PRODUCT DIRECTORY (CLEAN)
├── README.md                          # This file
├── launch_admin.py                    # Main application launcher
├── src/                              # Source code
│   └── admin/                        # Admin interface
│       ├── web_server.py             # Flask web server
│       ├── ai_research_service.py    # Real AI/LLM integration
│       └── templates/                # HTML templates
│           ├── base.html
│           ├── dashboard.html
│           ├── components_list.html
│           ├── add_component.html
│           ├── ai_research_lab.html
│           ├── analytics.html
│           └── system_info.html
└── tests/                            # Minimal product tests only
    └── interactive_visual_test.py    # Basic interactive test
```

## 🚀 How to Run
```bash
cd ComponentAI
python launch_admin.py
```

Then open: http://localhost:8080

## ✨ Features
- **🔬 AI Research Lab** - Real AI/LLM component research
- **📋 Component Management** - Add, view, edit components
- **💾 Database Operations** - Real data persistence
- **📊 Analytics Dashboard** - Usage statistics
- **🔧 System Information** - Version tracking and health

## 🤖 AI Integration
- **Real Gemini AI API** integration with fallback system
- **Comprehensive component research** with detailed analysis
- **Automatic form filling** with AI-derived data
- **Intelligent component type detection**

## 🧪 Testing
**IMPORTANT**: All robot testing is in a **SEPARATE DIRECTORY**:
- Testing Directory: `ComponentAI_Testing/`
- **DO NOT** mix test files with product code
- **KEEP** this directory clean and focused

To run comprehensive tests:
```bash
cd ComponentAI_Testing
python test_runners/run_all_tests.py
```

## 🎯 Architecture
- **Flask** web framework
- **Real AI/LLM** integration (Gemini API + fallback)
- **Mock database** with real persistence
- **Responsive UI** with Bootstrap
- **Clean separation** of concerns

## 📝 Development
- **Product code**: Keep in ComponentAI/
- **Test code**: Keep in ComponentAI_Testing/
- **Clean structure**: No mixing of concerns
- **Professional**: Separate testing from product

---

**This is the CLEAN PRODUCT DIRECTORY - No test files mixed in!**
