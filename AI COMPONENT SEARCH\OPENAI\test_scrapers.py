#!/usr/bin/env python3
"""
Test script to demonstrate how the scrapers work
This shows how to add new scrapers for different websites
"""

import json
from scrapers import get_scraper

def test_scrapers():
    """Test the scraper functionality"""
    
    # Load suppliers
    with open('suppliers.json', 'r') as f:
        suppliers = json.load(f)
    
    # Test component
    test_component = "10kΩ"
    test_package = "Through-hole"
    test_manufacturer = ""
    test_quantity = 1
    
    print(f"🔍 Testing scrapers for component: {test_component}")
    print("=" * 60)
    
    # Test a few suppliers from each tier
    test_suppliers = [
        suppliers['indian_tier1'][0],  # Robu
        suppliers['indian_tier1'][1],  # Evelta
        suppliers['indian_tier2'][0],  # CrazyPi
    ]
    
    for supplier in test_suppliers:
        print(f"\n📍 Testing {supplier['name']} ({supplier['location']})")
        print(f"   URL: {supplier['url']}")
        
        try:
            # Get scraper
            scraper = get_scraper(supplier)
            print(f"   Scraper: {scraper.__class__.__name__}")
            
            # Search for component
            results = scraper.search_component(
                test_component, 
                test_package, 
                test_manufacturer, 
                test_quantity
            )
            
            if results:
                print(f"   ✅ Found {len(results)} results:")
                for i, result in enumerate(results[:2], 1):  # Show first 2 results
                    print(f"      {i}. {result['component']}")
                    print(f"         Price: ₹{result['price']}")
                    print(f"         Stock: {result['stock']}")
                    print(f"         Shipping: ₹{result['shipping']}")
                    print(f"         Total: ₹{result['total']}")
            else:
                print("   ❌ No results found")
                
        except Exception as e:
            print(f"   ⚠️  Error: {str(e)}")

def demonstrate_adding_new_supplier():
    """Demonstrate how to add a new supplier"""
    
    print("\n" + "=" * 60)
    print("📝 How to Add a New Supplier")
    print("=" * 60)
    
    print("""
1. Add to suppliers.json:
   {
     "name": "New Electronics Store",
     "url": "https://newstore.com",
     "search_url": "https://newstore.com/search?q={query}",
     "location": "Mumbai, Maharashtra",
     "active": true,
     "scraper_class": "NewStoreScraper",
     "shipping_info": "₹60 for small components",
     "notes": "Good for passive components"
   }

2. Create specific scraper in scrapers.py:
   
   class NewStoreScraper(BaseScraper):
       def perform_search(self, search_query):
           # Build search URL
           search_url = self.config['search_url'].format(query=quote_plus(search_query))
           
           # Make request
           response = self.session.get(search_url, timeout=10)
           return response.text
       
       def parse_results(self, html_content, quantity):
           soup = BeautifulSoup(html_content, 'html.parser')
           results = []
           
           # Find product containers (inspect website to find correct selectors)
           products = soup.find_all('div', class_='product-item')
           
           for product in products[:5]:
               # Extract product details
               name = product.find('h3', class_='product-title').text.strip()
               price_text = product.find('span', class_='price').text
               price = self.extract_price(price_text)
               stock = product.find('span', class_='stock-status').text.strip()
               
               results.append({
                   'supplier': self.config['name'],
                   'component': name,
                   'price': price,
                   'stock': stock,
                   'shipping': 60,  # Known shipping cost
                   'total': price + 60,
                   'location': self.config['location'],
                   'url': self.config['url']
               })
           
           return results

3. Update the scraper factory in scrapers.py:
   Add 'NewStoreScraper': NewStoreScraper to the scraper_classes dictionary
""")

def show_current_supplier_count():
    """Show how many suppliers are currently configured"""
    
    with open('suppliers.json', 'r') as f:
        suppliers = json.load(f)
    
    print("\n" + "=" * 60)
    print("📊 Current Supplier Database")
    print("=" * 60)
    
    tier1_count = len(suppliers.get('indian_tier1', []))
    tier2_count = len(suppliers.get('indian_tier2', []))
    additional_count = len(suppliers.get('indian_additional', []))
    international_count = len(suppliers.get('international', []))
    
    total_indian = tier1_count + tier2_count + additional_count
    total_all = total_indian + international_count
    
    print(f"🇮🇳 Indian Suppliers:")
    print(f"   Tier 1 (Most Reliable): {tier1_count} suppliers")
    print(f"   Tier 2 (Good Options): {tier2_count} suppliers")
    print(f"   Additional: {additional_count} suppliers")
    print(f"   Total Indian: {total_indian} suppliers")
    print(f"\n🌍 International Suppliers: {international_count} suppliers")
    print(f"\n📈 Total Database: {total_all} suppliers")
    
    print(f"\n🎯 Search Strategy:")
    print(f"   1. Search {tier1_count} Tier 1 Indian suppliers first")
    print(f"   2. Then search {tier2_count} Tier 2 Indian suppliers")
    print(f"   3. Then search {additional_count} additional Indian suppliers")
    print(f"   4. Only search {international_count} international suppliers if no local results")

if __name__ == "__main__":
    print("🇮🇳 Indian Electronics Component Searcher - Test Suite")
    print("Testing scraper functionality and demonstrating extensibility")
    
    # Show current database
    show_current_supplier_count()
    
    # Test scrapers (commented out to avoid making actual web requests during demo)
    # Uncomment the line below to test actual scraping
    # test_scrapers()
    
    # Show how to add new suppliers
    demonstrate_adding_new_supplier()
    
    print("\n" + "=" * 60)
    print("✅ Test completed! The application is ready to use.")
    print("Run 'python component_searcher.py' to start the GUI application.")
    print("=" * 60)
