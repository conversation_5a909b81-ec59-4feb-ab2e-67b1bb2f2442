#!/usr/bin/env python3
"""
Simple Gemini API Test
Quick test to verify Gemini API works for component analysis
"""

import os
import json

def test_gemini_simple():
    """Simple test using the official Google library"""
    print("🤖 Simple Gemini API Test")
    print("=" * 30)
    
    # Get API key
    api_key = input("🔑 Enter your Gemini API key (from https://aistudio.google.com/): ").strip()
    
    if not api_key:
        print("❌ No API key provided")
        return False
    
    try:
        # Import the library
        import google.generativeai as genai
        
        # Configure the API
        genai.configure(api_key=api_key)
        
        # Create model
        model = genai.GenerativeModel('gemini-pro')
        
        print("✅ Gemini API configured successfully")
        
        # Test simple query
        print("\n🧪 Testing simple query...")
        response = model.generate_content("Hello! Please respond with 'Gemini API test successful'")
        print(f"📝 Response: {response.text}")
        
        # Test component analysis
        print("\n🔍 Testing component analysis...")
        component_query = "arduino uno"
        
        prompt = f"""
Analyze this electronics component: "{component_query}"

Provide:
1. Component type
2. Manufacturer
3. Key specifications
4. Common package/form factor
5. Typical applications

Keep response concise and technical.
"""
        
        response = model.generate_content(prompt)
        print(f"📋 Component Analysis:")
        print(response.text)
        
        # Save API key for future use
        save_key = input("\n💾 Save API key for future use? (y/n): ").lower().strip()
        if save_key == 'y':
            config = {"gemini_api_key": api_key}
            with open("gemini_config.json", "w") as f:
                json.dump(config, f, indent=2)
            print("✅ API key saved to gemini_config.json")
        
        print("\n🎉 Gemini API test successful!")
        print("✅ Ready to integrate with component search application")
        
        return True
        
    except ImportError:
        print("❌ Google Generative AI library not installed")
        print("📦 Install with: pip install google-generativeai")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def load_saved_key():
    """Load saved API key if available"""
    try:
        if os.path.exists("gemini_config.json"):
            with open("gemini_config.json", "r") as f:
                config = json.load(f)
                return config.get("gemini_api_key")
    except:
        pass
    return None

def test_with_saved_key():
    """Test with previously saved API key"""
    api_key = load_saved_key()
    if not api_key:
        print("❌ No saved API key found")
        return False
    
    try:
        import google.generativeai as genai
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-pro')
        
        print("🔑 Using saved API key...")
        
        # Quick test
        response = model.generate_content("Test: respond with 'OK'")
        print(f"✅ Quick test: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Saved key test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Gemini API Setup and Test")
    print("=" * 35)
    
    # Check if we have a saved key first
    if test_with_saved_key():
        print("✅ Saved API key works!")
        print("🎯 Ready to use Gemini for component analysis")
    else:
        print("🔧 Setting up new API key...")
        if test_gemini_simple():
            print("🎯 Setup complete!")
        else:
            print("❌ Setup failed")
    
    input("\nPress Enter to exit...")
