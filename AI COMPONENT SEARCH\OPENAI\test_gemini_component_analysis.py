#!/usr/bin/env python3
"""
Test Gemini Component Analysis
Working example of using Gemini API for component analysis
"""

import json
import os

def test_gemini_component_analysis():
    """Test Gemini API for component analysis"""
    print("🤖 Gemini Component Analysis Test")
    print("=" * 35)
    
    # Get API key
    api_key = input("🔑 Enter your Gemini API key: ").strip()
    
    if not api_key:
        print("❌ No API key provided")
        return False
    
    try:
        # Import and configure Gemini
        import google.generativeai as genai
        genai.configure(api_key=api_key)
        
        # Create model
        model = genai.GenerativeModel('gemini-pro')
        print("✅ Gemini API configured successfully")
        
        # Test components
        test_components = [
            "arduino uno",
            "10k resistor",
            "LM358 op amp",
            "ESP32 development board"
        ]
        
        print(f"\n🧪 Testing component analysis...")
        
        for component in test_components:
            print(f"\n📋 Analyzing: '{component}'")
            
            # Create analysis prompt
            prompt = f"""
Analyze this electronics component: "{component}"

Please provide a structured analysis with:
1. Component Type: (resistor, microcontroller, IC, etc.)
2. Manufacturer: (if identifiable from the name)
3. Key Specifications: (voltage, current, package, etc.)
4. Common Packages: (DIP, SMD, etc.)
5. Typical Applications: (where it's commonly used)
6. Alternatives: (similar components)

Keep the response concise and technical. Focus on the most important information for electronics engineers.
"""
            
            try:
                # Generate response
                response = model.generate_content(prompt)
                
                print(f"✅ Analysis completed!")
                print(f"📝 Response:")
                print("-" * 40)
                print(response.text)
                print("-" * 40)
                
            except Exception as e:
                print(f"❌ Analysis failed for {component}: {e}")
        
        # Save API key for future use
        save_key = input(f"\n💾 Save API key for future use? (y/n): ").lower().strip()
        if save_key == 'y':
            config = {"gemini_api_key": api_key}
            with open("gemini_config.json", "w") as f:
                json.dump(config, f, indent=2)
            print("✅ API key saved to gemini_config.json")
        
        print(f"\n🎉 Gemini component analysis test successful!")
        print(f"✅ Ready to integrate with component search application")
        
        return True
        
    except ImportError:
        print("❌ Google Generative AI library not installed")
        print("📦 Install with: pip install google-generativeai")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def load_and_test_saved_key():
    """Test with saved API key if available"""
    try:
        if os.path.exists("gemini_config.json"):
            with open("gemini_config.json", "r") as f:
                config = json.load(f)
                api_key = config.get("gemini_api_key")
                
                if api_key:
                    print("🔑 Found saved API key, testing...")
                    
                    import google.generativeai as genai
                    genai.configure(api_key=api_key)
                    model = genai.GenerativeModel('gemini-pro')
                    
                    # Quick test
                    response = model.generate_content("Test: respond with 'Gemini API working'")
                    print(f"✅ Saved API key works: {response.text}")
                    
                    return True
    except Exception as e:
        print(f"⚠️ Saved key test failed: {e}")
    
    return False

def show_integration_preview():
    """Show how this will integrate with the component search app"""
    print(f"\n🔮 Integration Preview")
    print(f"=" * 25)
    
    print(f"Once integrated with your component search application:")
    print(f"")
    print(f"1. 🔍 User enters component name (e.g., 'arduino uno')")
    print(f"2. 🤖 Gemini AI analyzes the component")
    print(f"3. 📊 AI extracts specifications and package info")
    print(f"4. 🏭 AI suggests top manufacturers")
    print(f"5. 🔍 Enhanced search across Indian suppliers")
    print(f"6. 📈 Quality-scored results with AI insights")
    print(f"")
    print(f"Benefits:")
    print(f"• ⚡ Fast analysis (2-3 seconds)")
    print(f"• 🆓 Completely free (1,500 requests/day)")
    print(f"• 🧠 Smart component understanding")
    print(f"• 📋 Structured data extraction")
    print(f"• 🔄 Always up-to-date AI model")

if __name__ == "__main__":
    print("🚀 Gemini API Component Analysis Test")
    print("=" * 40)
    
    # Check if we have a saved key first
    if load_and_test_saved_key():
        print("🎯 Saved API key is working!")
        
        # Ask if user wants to run full test
        run_full_test = input("🧪 Run full component analysis test? (y/n): ").lower().strip()
        if run_full_test == 'y':
            test_gemini_component_analysis()
    else:
        print("🔧 No saved API key found, running setup...")
        if test_gemini_component_analysis():
            print("🎯 Setup and test complete!")
        else:
            print("❌ Setup failed")
    
    show_integration_preview()
    
    print(f"\n📋 Summary:")
    print(f"✅ Gemini API is installed and ready")
    print(f"🔑 Get your free API key: https://aistudio.google.com/")
    print(f"🤖 Perfect replacement for slow local AI")
    print(f"🚀 Ready to integrate with component search!")
    
    input(f"\nPress Enter to exit...")
