# 🇮🇳 Indian Electronics Component Searcher

An automated tool to search for electronic components across Indian suppliers first, then international ones as a fallback. Designed specifically for Indian electronics enthusiasts and professionals to save on shipping costs and support local businesses.

## Features

- **🎯 Indian-First Search Strategy**: Searches Indian suppliers first (Tier 1, Tier 2, Additional), then international suppliers only if needed
- **💰 Cost Comparison**: Shows price, shipping cost, and total cost for easy comparison
- **📊 Comprehensive Results**: Displays stock status, supplier location, and direct links
- **📈 Export Functionality**: Export search results to CSV for further analysis
- **⚙️ Expandable Supplier Database**: Easy to add new suppliers as the ecosystem grows
- **🔍 Smart Search**: Supports component value, package type, manufacturer, and quantity

## Supported Suppliers

### Indian Suppliers (Tier 1 - Most Reliable)
- **Robu** (Pune, Maharashtra) - Wide selection, ₹49 shipping
- **Evelta** (Mumbai, Maharashtra) - Reliable, ₹55 shipping  
- **ElectronicsComp** (Bangalore, Karnataka)
- **Sunrom** (Ahmedabad, Gujarat)
- **Probots** (Bangalore, Karnataka)
- **Rhydolabz** (Cochin, Kerala)
- **FabtoLab** (Bangalore, Karnataka) - ₹100 shipping

### Indian Suppliers (Tier 2)
- CrazyPi, RoboCraze, Robokits, Ktron, Module143, ThinkRobotics, FlyRobo, Quartz Components, DNA Tech India, RoboElements, Potential Labs, FactoryForward, Silver Electronics, TomSon Electronics, Thingbits, Ventor Technologies, Rare Components

### Additional Indian Suppliers
- Hacktronics, Tanotis, ElementZOnline, EasyElectronics, RobotBanao, HNH Cart, InkOcean, OnlineTPS, SharviElectronics, Bafna Electronics, Sourcewell Electronics

### International Suppliers (Fallback Only)
- Digikey India, Mouser India, Element14 India, RS Components India, LCSC

## Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
2. **Run the application**:
   ```bash
   python component_searcher.py
   ```

## Usage

1. **Enter Component Details**:
   - Component Value (e.g., "10kΩ", "100nF", "ATmega328P")
   - Package Type (optional): Through-hole, SMD, 0603, 0805, etc.
   - Manufacturer (optional): Specific brand preference
   - Quantity: Number of components needed

2. **Click Search**: The application will automatically:
   - Search Tier 1 Indian suppliers first
   - Then search Tier 2 Indian suppliers
   - Search additional Indian suppliers
   - Only search international suppliers if no results found locally

3. **Review Results**:
   - Results are sorted with Indian suppliers first
   - Compare prices, shipping costs, and total costs
   - Check stock availability
   - Double-click any result to open the supplier's website

4. **Export Results**: Save search results to CSV for record-keeping

## Web Scraping Strategy

The application uses a **modular scraper architecture**:

### How Scrapers Work
1. **BaseScraper Class**: Common interface for all suppliers
2. **Specific Scrapers**: Each major supplier has its own scraper (RobuScraper, EveltaScraper, etc.)
3. **Generic Scraper**: Fallback for suppliers without specific implementations
4. **Selenium Support**: For JavaScript-heavy websites

### Scraping Techniques
- **BeautifulSoup**: For simple HTML parsing
- **Selenium**: For dynamic content and JavaScript-heavy sites
- **Requests**: For direct HTTP requests
- **Anti-bot Measures**: Random delays, proper user agents

### Adding New Suppliers

To add a new supplier:

1. **Update suppliers.json**:
   ```json
   {
     "name": "New Supplier",
     "url": "https://newsupplier.com",
     "search_url": "https://newsupplier.com/search?q={query}",
     "location": "City, State",
     "active": true,
     "scraper_class": "NewSupplierScraper"
   }
   ```

2. **Create specific scraper** (optional):
   ```python
   class NewSupplierScraper(BaseScraper):
       def perform_search(self, search_query):
           # Implement search logic
           pass
       
       def parse_results(self, html_content, quantity):
           # Implement result parsing
           pass
   ```

## Why This Approach?

### For Indian Users:
- **Lower Shipping Costs**: Indian suppliers typically have much lower or free shipping
- **Faster Delivery**: Local suppliers mean faster delivery times
- **Support Local Business**: Helps grow the Indian electronics ecosystem
- **Better Customer Service**: Local suppliers often provide better support

### Smart Search Strategy:
- **Prioritizes Local**: Always searches Indian suppliers first
- **Comprehensive Coverage**: Covers 40+ Indian suppliers
- **International Fallback**: Only searches expensive international suppliers if needed
- **Cost Transparency**: Shows total cost including shipping

## Technical Details

- **Language**: Python 3.7+
- **GUI Framework**: Tkinter (cross-platform)
- **Web Scraping**: BeautifulSoup4, Selenium, Requests
- **Data Export**: CSV format
- **Configuration**: JSON-based supplier database

## Future Enhancements

- [ ] **Bulk Search**: Upload BOM (Bill of Materials) files
- [ ] **Price Tracking**: Track component prices over time
- [ ] **Stock Alerts**: Notify when out-of-stock components become available
- [ ] **Supplier Management UI**: Add/remove suppliers through the interface
- [ ] **Advanced Filters**: Filter by price range, location, stock status
- [ ] **API Integration**: Direct integration with supplier APIs where available

## How Web Scraping Works for Each Site

### Current Implementation Status:

**✅ Implemented:**
- Robu.in - Custom scraper with known shipping costs
- Evelta.com - Custom scraper with known shipping costs
- Generic scraper for all other sites

**🔄 In Development:**
- Site-specific scrapers for major suppliers
- Selenium-based scrapers for JavaScript-heavy sites
- API integrations where available

### Scraper Development Process:

For each new website, we:
1. **Analyze the site structure** - inspect search pages and result layouts
2. **Identify search patterns** - URL structure, form parameters
3. **Create specific parsers** - extract product name, price, stock, etc.
4. **Handle anti-bot measures** - delays, user agents, session management
5. **Test and refine** - ensure reliable data extraction

### Example: Adding a New Scraper

```python
class NewSiteScraper(BaseScraper):
    def perform_search(self, search_query):
        # Build search URL
        search_url = f"https://newsite.com/search?q={search_query}"
        
        # Make request
        response = self.session.get(search_url)
        return response.text
    
    def parse_results(self, html_content, quantity):
        soup = BeautifulSoup(html_content, 'html.parser')
        results = []
        
        # Find product containers
        products = soup.find_all('div', class_='product-item')
        
        for product in products:
            # Extract data
            name = product.find('h3').text.strip()
            price = self.extract_price(product.find('.price').text)
            stock = product.find('.stock').text.strip()
            
            results.append({
                'supplier': self.config['name'],
                'component': name,
                'price': price,
                'stock': stock,
                'shipping': 50,  # Default shipping
                'total': price + 50,
                'location': self.config['location'],
                'url': self.config['url']
            })
        
        return results
```

## Contributing

This application is designed to grow with the Indian electronics ecosystem. To contribute:

1. **Add New Suppliers**: Update the suppliers.json file
2. **Improve Scrapers**: Create specific scrapers for better accuracy
3. **Report Issues**: Help identify suppliers with changed website structures
4. **Suggest Features**: Propose new functionality

## Disclaimer

This tool is for educational and personal use. Please respect the terms of service of the websites being scraped. The application includes delays and respectful scraping practices to avoid overloading servers.

---

**Made with ❤️ for the Indian Electronics Community**

*Supporting local suppliers • Reducing shipping costs • Growing the ecosystem*
