#!/usr/bin/env python3
"""
Proper Button Design - Simple, Clean, Professional
No fancy nonsense, just good basic button design
"""

import tkinter as tk
from tkinter import ttk

class ProperButtons:
    """Simple, clean, professional button design"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("✅ Proper Button Design - Simple & Clean")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#ffffff')
        self.root.state('zoomed')
        
        self.current_step = 1
        self.setup_ui()
    
    def setup_ui(self):
        """Setup with proper button design"""
        
        # Simple, clean workflow buttons
        self.setup_proper_buttons()
        
        # Main content
        self.main_content = tk.Frame(self.root, bg='#ffffff')
        self.main_content.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Show content
        self.show_step_content()
    
    def setup_proper_buttons(self):
        """Create simple, clean, professional buttons"""
        
        # Button container
        button_container = tk.Frame(self.root, bg='#f0f0f0', height=100)
        button_container.pack(fill='x', padx=20, pady=15)
        button_container.pack_propagate(False)
        
        # Title
        title_frame = tk.Frame(button_container, bg='#f0f0f0')
        title_frame.pack(fill='x', pady=5)
        
        tk.Label(title_frame, text="Component Search Workflow", 
                font=('Arial', 14, 'bold'), bg='#f0f0f0', fg='#333333').pack()
        
        # Buttons frame
        buttons_frame = tk.Frame(button_container, bg='#f0f0f0')
        buttons_frame.pack(expand=True, fill='x', pady=10)
        
        # Button definitions
        self.buttons = [
            {"num": "1", "title": "Search", "step": 1},
            {"num": "2", "title": "AI Analysis", "step": 2},
            {"num": "3", "title": "Results", "step": 3},
            {"num": "4", "title": "Details", "step": 4},
            {"num": "5", "title": "Decision", "step": 5}
        ]
        
        self.button_widgets = []
        
        # Create simple, clean buttons
        for i, btn in enumerate(self.buttons):
            
            # Button container
            btn_container = tk.Frame(buttons_frame, bg='#f0f0f0')
            btn_container.pack(side='left', expand=True, padx=10)
            
            # Simple button frame
            btn_frame = tk.Frame(btn_container, bg='#ffffff', relief='raised', bd=2, cursor='hand2')
            btn_frame.pack(fill='x', pady=5)
            
            # Make clickable
            btn_frame.bind('<Button-1>', lambda e, step=btn['step']: self.go_to_step(step))
            
            # Number at top - SIMPLE
            number_label = tk.Label(btn_frame, text=btn['num'], 
                                   font=('Arial', 24, 'bold'),
                                   bg='#ffffff', fg='#666666',
                                   pady=10)
            number_label.pack()
            number_label.bind('<Button-1>', lambda e, step=btn['step']: self.go_to_step(step))
            
            # Title below - SIMPLE
            title_label = tk.Label(btn_frame, text=btn['title'], 
                                  font=('Arial', 12, 'bold'),
                                  bg='#ffffff', fg='#333333',
                                  pady=5)
            title_label.pack()
            title_label.bind('<Button-1>', lambda e, step=btn['step']: self.go_to_step(step))
            
            self.button_widgets.append({
                'frame': btn_frame,
                'number': number_label,
                'title': title_label
            })
            
            # Simple arrow between buttons
            if i < len(self.buttons) - 1:
                arrow_label = tk.Label(buttons_frame, text="→", 
                                     font=('Arial', 20), bg='#f0f0f0', fg='#cccccc')
                arrow_label.pack(side='left', padx=5)
        
        # Update button appearance
        self.update_button_appearance()
    
    def update_button_appearance(self):
        """Update button appearance based on current step"""
        
        for i, btn_widget in enumerate(self.button_widgets):
            step_num = i + 1
            
            if step_num == self.current_step:
                # Current step - BLUE
                btn_widget['frame'].config(bg='#0066cc', relief='raised', bd=3)
                btn_widget['number'].config(bg='#0066cc', fg='#ffffff', font=('Arial', 26, 'bold'))
                btn_widget['title'].config(bg='#0066cc', fg='#ffffff', font=('Arial', 13, 'bold'))
                
            elif step_num < self.current_step:
                # Completed step - GREEN
                btn_widget['frame'].config(bg='#009900', relief='raised', bd=2)
                btn_widget['number'].config(bg='#009900', fg='#ffffff', font=('Arial', 24, 'bold'))
                btn_widget['title'].config(bg='#009900', fg='#ffffff', font=('Arial', 12, 'bold'))
                
            else:
                # Future step - GRAY
                btn_widget['frame'].config(bg='#ffffff', relief='raised', bd=1)
                btn_widget['number'].config(bg='#ffffff', fg='#666666', font=('Arial', 24, 'bold'))
                btn_widget['title'].config(bg='#ffffff', fg='#333333', font=('Arial', 12, 'bold'))
    
    def show_step_content(self):
        """Show content for current step"""
        
        # Clear content
        for widget in self.main_content.winfo_children():
            widget.destroy()
        
        # Header
        header_frame = tk.Frame(self.main_content, bg='#ffffff')
        header_frame.pack(fill='x', pady=20)
        
        step_names = ["Component Search", "AI Analysis", "Search Results", "Component Details", "Final Decision"]
        current_name = step_names[self.current_step - 1]
        
        tk.Label(header_frame, text=f"Step {self.current_step}: {current_name}", 
                font=('Arial', 24, 'bold'), bg='#ffffff', fg='#0066cc').pack(side='left')
        
        # Content area
        content_frame = tk.Frame(self.main_content, bg='#f8f8f8', relief='solid', bd=1)
        content_frame.pack(fill='both', expand=True, pady=20)
        
        # Large content display
        content_text = tk.Text(content_frame, font=('Arial', 14), height=20,
                              bg='#ffffff', relief='flat', padx=40, pady=40)
        content_text.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Simple content based on step
        contents = [
            "STEP 1: COMPONENT SEARCH\n\nEnter the component you're looking for.\n\nThis is where you input your component requirements.",
            "STEP 2: AI ANALYSIS\n\nAI analyzes your component request.\n\nGemini AI processes your input and provides recommendations.",
            "STEP 3: SEARCH RESULTS\n\nView search results with quality scoring.\n\nSee all available options from different suppliers.",
            "STEP 4: COMPONENT DETAILS\n\nView detailed specifications and datasheets.\n\nReview technical information and documentation.",
            "STEP 5: FINAL DECISION\n\nMake your final selection and export.\n\nComplete your component sourcing process."
        ]
        
        content_text.insert('1.0', contents[self.current_step - 1])
        content_text.config(state='disabled')
        
        # Simple navigation buttons
        nav_frame = tk.Frame(self.main_content, bg='#ffffff')
        nav_frame.pack(fill='x', pady=20)
        
        if self.current_step > 1:
            prev_btn = tk.Button(nav_frame, text="← Previous", 
                                command=lambda: self.go_to_step(self.current_step - 1),
                                bg='#666666', fg='white', font=('Arial', 12, 'bold'),
                                padx=20, pady=10, relief='raised', bd=2)
            prev_btn.pack(side='left')
        
        if self.current_step < 5:
            next_btn = tk.Button(nav_frame, text="Next →", 
                                command=lambda: self.go_to_step(self.current_step + 1),
                                bg='#0066cc', fg='white', font=('Arial', 12, 'bold'),
                                padx=20, pady=10, relief='raised', bd=2)
            next_btn.pack(side='right')
        else:
            new_btn = tk.Button(nav_frame, text="New Search", 
                               command=lambda: self.go_to_step(1),
                               bg='#009900', fg='white', font=('Arial', 12, 'bold'),
                               padx=20, pady=10, relief='raised', bd=2)
            new_btn.pack(side='right')
    
    def go_to_step(self, step):
        """Navigate to specific step"""
        self.current_step = step
        self.update_button_appearance()
        self.show_step_content()
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

if __name__ == "__main__":
    print("✅ Proper Button Design")
    print("=" * 25)
    print("Simple rectangle buttons")
    print("Clean numbers: 1, 2, 3, 4, 5")
    print("Professional appearance")
    print("No fancy nonsense")
    print("Just good basic design")
    
    app = ProperButtons()
    app.run()
