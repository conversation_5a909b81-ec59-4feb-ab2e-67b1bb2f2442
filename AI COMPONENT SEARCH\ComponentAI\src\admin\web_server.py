#!/usr/bin/env python3
"""
ComponentAI Web Server
Flask-based admin interface for ComponentAI
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
from datetime import datetime
import os
import sys

# Import AI research service
try:
    from .ai_research_service import ai_research_service
except ImportError:
    # Fallback if import fails
    ai_research_service = None

class ComponentAIWebServer:
    """ComponentAI Web Server"""
    
    def __init__(self, host='localhost', port=8080):
        self.host = host
        self.port = port
        self.app = Flask(__name__, 
                        template_folder=os.path.join(os.path.dirname(__file__), 'templates'),
                        static_folder=os.path.join(os.path.dirname(__file__), 'static'))
        
        # Initialize database (mock for now)
        self.db = MockDatabase()
        
        # Setup routes
        self.setup_routes()
    
    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def dashboard():
            """Dashboard page"""
            stats = {
                'total_components': 9,
                'recent_searches': 15,
                'ai_research_count': 3
            }
            return render_template('dashboard.html', stats=stats)
        
        @self.app.route('/components')
        def components_list():
            """Components list page"""
            components = self.db.get_all_components()
            return render_template('components.html', components=components)
        
        @self.app.route('/components/add', methods=['GET', 'POST'])
        def add_component():
            """Add component page"""
            if request.method == 'POST':
                # Handle form submission
                component_data = {
                    'name': request.form.get('name'),
                    'type': request.form.get('type'),
                    'description': request.form.get('description')
                }

                # REAL database save
                new_component = self.db.add_component(
                    component_data['name'],
                    component_data['type'],
                    component_data['description']
                )

                print(f"✅ Component saved to database: {component_data['name']}")
                return redirect(url_for('components_list'))

            return render_template('add_component.html')

        @self.app.route('/components/<int:component_id>')
        def view_component(component_id):
            """View component details"""
            component = self.db.get_component_by_id(component_id)
            if not component:
                return "Component not found", 404
            return render_template('view_component.html', component=component)

        @self.app.route('/components/<int:component_id>/edit', methods=['GET', 'POST'])
        def edit_component(component_id):
            """Edit component"""
            component = self.db.get_component_by_id(component_id)
            if not component:
                return "Component not found", 404

            if request.method == 'POST':
                # Handle form submission
                return redirect(url_for('view_component', component_id=component_id))

            return render_template('edit_component.html', component=component)

        @self.app.route('/components/<int:component_id>/delete', methods=['POST'])
        def delete_component(component_id):
            """Delete component"""
            # Mock delete
            return redirect(url_for('components_list'))

        @self.app.route('/ai-research')
        def ai_research_lab():
            """AI Research Lab page"""
            return render_template('ai_research_lab.html')
        
        @self.app.route('/analytics')
        def analytics():
            """Analytics page"""
            return render_template('analytics.html')
        
        @self.app.route('/system-info')
        def system_info():
            """System information page"""
            info = {
                'componentai_version': '1.0.0',
                'build_date': '2024-12-08',
                'components': {
                    'admin_ui': '1.0.0',
                    'mcp_server': '1.0.0'
                }
            }
            return render_template('system_info.html', info=info)
        
        @self.app.route('/api/components')
        def api_components():
            """API endpoint for components"""
            components = self.db.get_all_components()
            return jsonify({
                'success': True,
                'components': [c.__dict__ for c in components],
                'total': len(components)
            })
        
        @self.app.route('/api/ai/research', methods=['POST'])
        def api_ai_research():
            """API endpoint for AI research"""
            data = request.get_json()
            component_name = data.get('component_name', '')

            if not component_name:
                return jsonify({
                    'success': False,
                    'error': 'Component name is required'
                })

            # Use REAL AI research service
            if ai_research_service:
                result = ai_research_service.research_component(component_name)
                if result['success']:
                    return jsonify({
                        'success': True,
                        'component': result['final_component']
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': result.get('error', 'AI research failed')
                    })
            else:
                # Fallback if AI service unavailable
                return jsonify({
                    'success': False,
                    'error': 'AI research service unavailable'
                })
        
        @self.app.route('/api/ai/research-detailed', methods=['POST'])
        def api_ai_research_detailed():
            """API endpoint for detailed AI research"""
            data = request.get_json()
            component_name = data.get('component_name', '')

            if not component_name:
                return jsonify({
                    'success': False,
                    'error': 'Component name is required'
                })

            print(f"🔬 API: Starting REAL AI research for: {component_name}")

            # Use REAL AI research service
            if ai_research_service:
                result = ai_research_service.research_component(component_name)
                print(f"🔬 API: AI research result: {result['success']}")
                return jsonify(result)
            else:
                print("❌ API: AI research service unavailable")
                return jsonify({
                    'success': False,
                    'error': 'AI research service unavailable',
                    'component_name': component_name,
                    'timestamp': datetime.now().isoformat()
                })
    
    def run(self):
        """Run the Flask server"""
        self.app.run(host=self.host, port=self.port, debug=False)

class MockComponent:
    """Mock component for testing"""
    def __init__(self, id, name, type, description):
        self.id = id
        self.name = name
        self.type = type
        self.description = description

class MockDatabase:
    """Mock database for testing - now with REAL storage"""

    def __init__(self):
        self.components = [
            MockComponent(1, "Arduino Uno R3", "Microcontroller", "Popular development board"),
            MockComponent(2, "ESP32-S3", "WiFi Module", "WiFi and Bluetooth module"),
            MockComponent(3, "Raspberry Pi 4", "Single Board Computer", "ARM-based computer"),
            MockComponent(4, "STM32F103C8T6", "Microcontroller", "ARM Cortex-M3 MCU"),
            MockComponent(5, "Arduino Nano 33 IoT", "Development Board", "IoT development board"),
            MockComponent(6, "ESP8266", "WiFi Module", "Low-cost WiFi module"),
            MockComponent(7, "Raspberry Pi Pico", "Microcontroller", "RP2040-based board"),
            MockComponent(8, "NodeMCU", "Development Board", "ESP8266-based board"),
            MockComponent(9, "Arduino Pro Mini", "Microcontroller", "Compact Arduino board")
        ]
        self.next_id = 10  # For new components
    
    def get_all_components(self):
        """Get all components"""
        return self.components
    
    def get_component_count(self):
        """Get component count"""
        return len(self.components)

    def get_component_by_id(self, component_id):
        """Get component by ID"""
        for component in self.components:
            if component.id == component_id:
                return component
        return None

    def add_component(self, name, type, description):
        """Add new component to database"""
        new_component = MockComponent(self.next_id, name, type, description)
        self.components.append(new_component)
        self.next_id += 1
        print(f"💾 Database: Added component {name} with ID {new_component.id}")
        return new_component
