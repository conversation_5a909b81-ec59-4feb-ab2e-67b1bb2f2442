#!/usr/bin/env python3
"""
AI Analyzer Module
Handles AI/LLM integration for component analysis and datasheet processing
Supports multiple LLM providers including web-based services
"""

import os
import json
import requests
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import threading
from urllib.parse import urljoin

class LLMProvider(Enum):
    """Supported LLM providers"""
    DEEPSEEK_WEB = "deepseek_web"
    CHATGPT_FREE = "chatgpt_free"
    CLAUDE_FREE = "claude_free"
    PERPLEXITY = "perplexity"
    YOU_CHAT = "you_chat"
    OLLAMA_LOCAL = "ollama_local"  # Keep as fallback

@dataclass
class ComponentAnalysis:
    """Component analysis result"""
    component_type: str
    manufacturer: str
    part_number: str
    specifications: Dict[str, Any]
    package_options: List[str]
    alternatives: List[str]
    datasheet_url: Optional[str]
    confidence_score: float
    analysis_notes: str

@dataclass
class DatasheetAnalysis:
    """Datasheet analysis result"""
    component_name: str
    manufacturer: str
    key_parameters: Dict[str, str]
    package_types: List[str]
    applications: List[str]
    electrical_characteristics: Dict[str, str]
    mechanical_specs: Dict[str, str]
    ordering_information: Dict[str, str]
    confidence_score: float

class AIAnalyzer:
    """AI-powered component and datasheet analyzer"""
    
    def __init__(self):
        self.config = self.load_config()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

        # Initialize provider configurations
        self.providers = {}
        self.active_provider = None
        self.web_automator = None
        self.setup_providers()
    
    def load_config(self) -> Dict:
        """Load AI configuration"""
        config_file = "ai_config.json"
        default_config = {
            "default_provider": "deepseek_web",
            "providers": {
                "deepseek_web": {
                    "enabled": True,
                    "name": "DeepSeek Chat (Free)",
                    "description": "Free web-based AI, no login required"
                },
                "perplexity": {
                    "enabled": True,
                    "name": "Perplexity AI (Free)",
                    "description": "Free web-based AI, no login required"
                },
                "you_chat": {
                    "enabled": True,
                    "name": "You.com Chat (Free)",
                    "description": "Free web-based AI, no login required"
                },
                "chatgpt_free": {
                    "enabled": False,
                    "name": "ChatGPT Free",
                    "description": "Requires OpenAI account login",
                    "email": "",
                    "password": ""
                },
                "claude_free": {
                    "enabled": False,
                    "name": "Claude Free",
                    "description": "Requires Anthropic account login",
                    "email": "",
                    "password": ""
                },
                "ollama_local": {
                    "enabled": False,
                    "name": "Ollama Local",
                    "description": "Local AI server (slow)",
                    "base_url": "http://localhost:11434",
                    "model": "qwen2:7b"
                }
            },
            "web_automation": {
                "headless": False,
                "timeout": 30,
                "typing_delay": 0.1,
                "response_timeout": 60
            },
            "analysis_settings": {
                "max_retries": 3,
                "confidence_threshold": 0.7
            }
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    return json.load(f)
            except Exception:
                return default_config
        else:
            # Save default config
            with open(config_file, 'w') as f:
                json.dump(default_config, f, indent=2)
            return default_config
    
    def save_config(self):
        """Save AI configuration"""
        with open("ai_config.json", 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def setup_providers(self):
        """Setup available LLM providers"""
        for provider_name, provider_config in self.config["providers"].items():
            if provider_config.get("enabled", False):
                self.providers[provider_name] = provider_config
        
        # Set active provider
        default_provider = self.config.get("default_provider", "ollama_local")
        if default_provider in self.providers:
            self.active_provider = default_provider
        elif self.providers:
            self.active_provider = list(self.providers.keys())[0]
    
    def test_provider_connection(self, provider_name: str) -> Dict[str, Any]:
        """Test connection to a specific provider"""
        if provider_name not in self.providers:
            return {"success": False, "error": "Provider not configured"}
        
        provider_config = self.providers[provider_name]
        
        try:
            if provider_name == "ollama_local":
                return self._test_ollama_connection(provider_config)
            elif provider_name.endswith("_web"):
                return self._test_web_provider_connection(provider_name, provider_config)
            else:
                return {"success": False, "error": "Provider type not supported yet"}
        
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _test_ollama_connection(self, config: Dict) -> Dict[str, Any]:
        """Test Ollama local connection"""
        try:
            base_url = config.get("base_url", "http://localhost:11434")
            response = self.session.get(f"{base_url}/api/tags", timeout=5)
            
            if response.status_code == 200:
                models = response.json().get("models", [])
                model_names = [m.get("name", "") for m in models]
                
                return {
                    "success": True,
                    "provider": "Ollama Local",
                    "models": model_names,
                    "status": "Connected"
                }
            else:
                return {"success": False, "error": f"HTTP {response.status_code}"}
        
        except requests.exceptions.ConnectionError:
            return {"success": False, "error": "Ollama server not running"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _test_web_provider_connection(self, provider_name: str, config: Dict) -> Dict[str, Any]:
        """Test web-based provider connection"""
        try:
            # Initialize web automator if needed
            if not self.web_automator:
                from web_ai_provider import get_web_ai_automator
                self.web_automator = get_web_ai_automator()

            # Test the provider
            result = self.web_automator.test_provider(provider_name)

            if result["success"]:
                return {
                    "success": True,
                    "provider": result["provider"],
                    "status": "Connected via web automation",
                    "test_response": result.get("test_response", "")
                }
            else:
                return result

        except ImportError:
            return {
                "success": False,
                "error": "Web automation dependencies not installed",
                "note": "Run: pip install selenium undetected-chromedriver"
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def analyze_component_query(self, query: str) -> ComponentAnalysis:
        """Analyze a component search query using AI"""
        if not self.active_provider:
            raise Exception("No AI provider configured")
        
        prompt = self._build_component_analysis_prompt(query)
        
        try:
            response = self._query_llm(prompt)
            return self._parse_component_analysis(response, query)
        
        except Exception as e:
            # Return fallback analysis
            return ComponentAnalysis(
                component_type="unknown",
                manufacturer="",
                part_number=query,
                specifications={},
                package_options=["Any"],
                alternatives=[],
                datasheet_url=None,
                confidence_score=0.1,
                analysis_notes=f"AI analysis failed: {str(e)}"
            )
    
    def analyze_datasheet(self, datasheet_path: str) -> DatasheetAnalysis:
        """Analyze a datasheet PDF using AI"""
        # This would implement PDF parsing and AI analysis
        # For now, return a placeholder
        return DatasheetAnalysis(
            component_name="Unknown",
            manufacturer="Unknown",
            key_parameters={},
            package_types=[],
            applications=[],
            electrical_characteristics={},
            mechanical_specs={},
            ordering_information={},
            confidence_score=0.0
        )
    
    def _build_component_analysis_prompt(self, query: str) -> str:
        """Build prompt for component analysis"""
        return f"""
Analyze this electronics component search query: "{query}"

Please provide a structured analysis including:
1. Component type (resistor, capacitor, IC, etc.)
2. Likely manufacturer (if identifiable)
3. Part number or value
4. Key specifications
5. Common package options
6. Alternative components
7. Confidence in analysis (0-1)

Respond in JSON format with these fields:
{{
    "component_type": "string",
    "manufacturer": "string",
    "part_number": "string",
    "specifications": {{}},
    "package_options": [],
    "alternatives": [],
    "confidence_score": 0.0,
    "analysis_notes": "string"
}}
"""
    
    def _query_llm(self, prompt: str) -> str:
        """Query the active LLM provider"""
        if self.active_provider == "ollama_local":
            return self._query_ollama(prompt)
        elif self.active_provider in ["deepseek_web", "chatgpt_free", "claude_free", "perplexity", "you_chat"]:
            return self._query_web_provider(prompt)
        else:
            raise Exception(f"Provider {self.active_provider} not implemented yet")
    
    def _query_ollama(self, prompt: str) -> str:
        """Query Ollama local instance"""
        config = self.providers["ollama_local"]
        base_url = config.get("base_url", "http://localhost:11434")
        model = config.get("model", "qwen2:7b")
        
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False
        }
        
        response = self.session.post(
            f"{base_url}/api/generate",
            json=payload,
            timeout=self.config["analysis_settings"]["timeout"]
        )
        
        if response.status_code == 200:
            return response.json().get("response", "")
        else:
            raise Exception(f"Ollama query failed: HTTP {response.status_code}")

    def _query_web_provider(self, prompt: str) -> str:
        """Query web-based AI provider"""
        try:
            # Try advanced web automator first
            if not self.web_automator:
                try:
                    from web_ai_provider import get_web_ai_automator
                    self.web_automator = get_web_ai_automator()

                    # Connect to provider if not already connected
                    if not self.web_automator.session_active:
                        connect_result = self.web_automator.connect_to_provider(self.active_provider)
                        if not connect_result["success"]:
                            raise Exception(f"Failed to connect to {self.active_provider}: {connect_result['error']}")

                    # Send query
                    result = self.web_automator.send_query(prompt)

                    if result["success"]:
                        return result["response"]
                    else:
                        raise Exception(f"Query failed: {result['error']}")

                except Exception as e:
                    print(f"Advanced web automator failed: {e}")
                    # Fall back to simple web AI
                    return self._query_simple_web_ai(prompt)
            else:
                # Use existing web automator
                result = self.web_automator.send_query(prompt)
                if result["success"]:
                    return result["response"]
                else:
                    raise Exception(f"Query failed: {result['error']}")

        except Exception as e:
            # Try simple web AI as fallback
            try:
                return self._query_simple_web_ai(prompt)
            except Exception as fallback_error:
                raise Exception(f"All web providers failed. Advanced: {str(e)}, Simple: {str(fallback_error)}")

    def _query_simple_web_ai(self, prompt: str) -> str:
        """Query using simple web AI as fallback"""
        try:
            from simple_web_ai import get_simple_web_ai

            ai = get_simple_web_ai()

            # Try providers in order
            providers_to_try = ["perplexity", "you_chat"]

            for provider in providers_to_try:
                result = ai.send_query(provider, prompt)
                if result["success"]:
                    return result["response"]

            raise Exception("All simple web AI providers failed")

        except ImportError:
            raise Exception("Simple web AI not available")
        except Exception as e:
            raise Exception(f"Simple web AI failed: {str(e)}")
    
    def _parse_component_analysis(self, response: str, original_query: str) -> ComponentAnalysis:
        """Parse LLM response into ComponentAnalysis"""
        try:
            # Try to extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
                
                return ComponentAnalysis(
                    component_type=data.get("component_type", "unknown"),
                    manufacturer=data.get("manufacturer", ""),
                    part_number=data.get("part_number", original_query),
                    specifications=data.get("specifications", {}),
                    package_options=data.get("package_options", ["Any"]),
                    alternatives=data.get("alternatives", []),
                    datasheet_url=None,
                    confidence_score=float(data.get("confidence_score", 0.5)),
                    analysis_notes=data.get("analysis_notes", "")
                )
            else:
                raise ValueError("No JSON found in response")
        
        except Exception as e:
            # Fallback parsing
            return ComponentAnalysis(
                component_type="unknown",
                manufacturer="",
                part_number=original_query,
                specifications={},
                package_options=["Any"],
                alternatives=[],
                datasheet_url=None,
                confidence_score=0.3,
                analysis_notes=f"Parsing failed: {str(e)}. Raw response: {response[:200]}..."
            )

# Global instance
ai_analyzer = AIAnalyzer()

def get_ai_analyzer() -> AIAnalyzer:
    """Get the global AI analyzer instance"""
    return ai_analyzer

if __name__ == "__main__":
    # Test the AI analyzer
    analyzer = get_ai_analyzer()
    
    # Test provider connections
    print("Testing AI providers...")
    for provider in analyzer.providers:
        result = analyzer.test_provider_connection(provider)
        print(f"{provider}: {result}")
    
    # Test component analysis
    if analyzer.active_provider:
        print(f"\nTesting component analysis with {analyzer.active_provider}...")
        try:
            analysis = analyzer.analyze_component_query("arduino uno")
            print(f"Analysis: {analysis}")
        except Exception as e:
            print(f"Analysis failed: {e}")
