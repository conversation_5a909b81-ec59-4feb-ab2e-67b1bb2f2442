:root {
    --bg-color: #1a202c;
    --card-bg-color: #2d3748;
    --border-color: #4a5568;
    --text-color: #e2e8f0;
    --text-color-muted: #a0aec0;
    --header-color: #ffffff;
    --input-bg-color: #2d3748;
    --input-border-color: #4a5568;
    --button-bg-color: #4a5568;
    --button-hover-bg-color: #2d3748; 
    --hover-row-bg: #374151; 
    --accent-color: #63b3ed; 
    --scan-box-bg-color: #1f2937; 
    --error-text-color: #f87171; 
}
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    margin: 0;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.container {
    width: 100%;
    max-width: 1200px;
    background-color: var(--card-bg-color);
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
}
.header {
    display: flex;
    justify-content: space-between; /* Pushes .header-main-title and .tool-version-info apart */
    align-items: flex-start; /* Align items to the top */
    padding-bottom: 20px;
    margin-bottom: 25px;
    border-bottom: 1px solid var(--border-color);
}

/* --- MODIFIED/ADDED STYLES for header content --- */
.header-main-title {
    /* This div will contain the H1 and the credits line */
    display: flex;
    flex-direction: column; /* Stack H1 and credits vertically */
    align-items: flex-start; /* Align them to the left */
}
.header h1 { /* Main "KiCad Project Browser" title */
    font-size: 1.8em;
    color: var(--header-color);
    margin: 0 0 5px 0; /* Add some bottom margin */
    display: flex;
    align-items: center;
}
.header h1 svg {
    width: 30px;
    height: 30px;
    margin-right: 12px;
    fill: var(--accent-color);
}
.header-credits { /* "Built by..." line */
    font-size: 0.75em; /* Smaller font */
    font-style: italic;
    color: var(--text-color-muted);
    margin-left: 42px; /* Align roughly under the text part of H1 (30px icon + 12px margin) */
}
.tool-version-info { /* "UI vX.X.X" line */
    font-size: 0.8em;
    color: var(--text-color-muted);
    text-align: right;
    margin-top: 5px; /* Adjust to align nicely if needed */
}
/* --- END OF MODIFIED/ADDED STYLES --- */

.footer-info { /* ... rest of CSS remains the same ... */
    margin-top: 25px; padding-top: 15px; border-top: 1px solid var(--border-color);
    font-size: 0.8em; color: var(--text-color-muted); text-align: center;
}
.title-text {
    font-size: 1.5em; color: var(--header-color); margin-bottom: 15px;
}
.search-bar-container {
    display: flex; align-items: center; background-color: var(--input-bg-color);
    border: 1px solid var(--input-border-color); border-radius: 6px;
    padding-left: 10px; margin-bottom: 20px;
}
.search-bar-container svg {
    width: 18px; height: 18px; fill: var(--text-color-muted); margin-right: 8px;
}
.search-bar {
    width: 100%; padding: 12px 10px 12px 0; background-color: transparent;
    border: none; border-radius: 6px; color: var(--text-color);
    font-size: 1em; box-sizing: border-box; outline: none;
}
.search-bar::placeholder { color: var(--text-color-muted); }
.controls {
    display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;
}
.project-count { font-size: 0.9em; color: var(--text-color-muted); }
.button {
    background-color: var(--button-bg-color); color: var(--text-color); border: none;
    padding: 10px 18px; border-radius: 6px; cursor: pointer; font-size: 0.9em;
    transition: background-color 0.2s; display: flex; align-items: center;
}
.button svg { width: 16px; height: 16px; margin-right: 8px; fill: currentColor; }
.button:hover { background-color: var(--button-hover-bg-color); }
.button:disabled { background-color: #374151; color: var(--text-color-muted); cursor: not-allowed; }
table {
    width: 100%; border-collapse: collapse; border-radius: 8px; overflow: hidden; 
}
th, td {
    text-align: left; padding: 14px 18px; border-bottom: 1px solid var(--border-color);
    font-size: 0.95em; 
}
th {
    background-color: #374151; color: var(--text-color); font-weight: 600;
    font-size: 0.8em; text-transform: uppercase; letter-spacing: 0.05em;
}
tbody tr { cursor: pointer; transition: background-color 0.15s ease-in-out; }
tbody tr:hover { background-color: var(--hover-row-bg); }
tbody tr:last-child td { border-bottom: none; }
.status-message-container { border-radius: 8px; padding: 0px; }
.loading-message, .error-message, .no-projects-message, .no-results-message {
    text-align: center; padding: 30px 20px; color: var(--text-color-muted);
    font-size: 1.1em; border-top: 1px solid var(--border-color);
}
.spinner {
    border: 4px solid rgba(255, 255, 255, 0.3); border-radius: 50%;
    border-top-color: var(--accent-color); width: 24px; height: 24px;
    animation: spin 1s linear infinite; margin: 0 auto 10px auto; 
}
#loadingMessage span { display: block; }
@keyframes spin { to { transform: rotate(360deg); } }
.animate-spin-manual { animation: spin 1s linear infinite; }

th[data-sort-key] { position: relative; padding-right: 25px; }
th[data-sort-key]::after {
    content: ''; position: absolute; right: 8px; top: 50%;
    border: 4px solid transparent; opacity: 0.3; 
}
th[data-sort-key].sort-asc::after {
    border-bottom-color: var(--text-color); transform: translateY(-7px); opacity: 1;
}
th[data-sort-key].sort-desc::after {
    border-top-color: var(--text-color); transform: translateY(-1px); opacity: 1;
}

.scan-progress-section { margin-bottom: 20px; }
.scan-progress-section h4 {
    margin-top: 0; margin-bottom: 10px; color: var(--text-color); font-size: 1.1em;
}
.scan-output-box {
    background-color: var(--scan-box-bg-color); border: 1px solid var(--border-color);
    border-radius: 6px; padding: 10px 15px; max-height: 200px; 
    overflow-y: auto; font-size: 0.9em;
}
.scan-output-box ul { list-style-type: none; padding-left: 0; margin: 0; }
.scan-output-box ul li {
    padding: 5px 0; border-bottom: 1px dashed #374151; 
    color: var(--text-color-muted); word-break: break-word; 
}
.scan-output-box ul li:last-child { border-bottom: none; }
.scan-output-box ul li.status-message { font-style: italic; color: var(--accent-color); }
.scan-output-box ul li.error-message { color: var(--error-text-color); font-weight: bold; }
.scan-output-box ul li.found-project-item { color: var(--text-color); }
.scan-controls { text-align: center; margin-top: 20px; }