#!/usr/bin/env python3
"""
Fixed Workflow UI - No Text Occlusion
Proper spacing and text layout to prevent overlap
"""

import tkinter as tk
from tkinter import ttk

class FixedWorkflowUI:
    """Workflow UI with proper text spacing and no occlusion"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 Fixed Workflow UI - No Text Occlusion")
        self.root.geometry("1400x900")
        self.root.configure(bg='#ffffff')
        
        self.current_step = 1
        self.setup_ui()
    
    def setup_ui(self):
        """Setup UI with proper text spacing"""
        
        # Fixed workflow steps
        self.setup_workflow_steps_fixed()
        
        # Main content
        self.main_content = tk.Frame(self.root, bg='#ffffff')
        self.main_content.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Show demo content
        self.show_demo_content()
    
    def setup_workflow_steps_fixed(self):
        """Workflow steps with NO text occlusion"""
        
        # Larger workflow frame
        workflow_frame = tk.Frame(self.root, bg='#f8f9fa', height=120)
        workflow_frame.pack(fill='x', padx=20, pady=10)
        workflow_frame.pack_propagate(False)
        
        # Title
        tk.Label(workflow_frame, text="📋 Component Search Workflow", 
                font=('Arial', 14, 'bold'), bg='#f8f9fa', fg='#495057').pack(pady=(10,5))
        
        # Steps container with proper spacing
        steps_container = tk.Frame(workflow_frame, bg='#f8f9fa')
        steps_container.pack(expand=True, fill='x', pady=10)
        
        # Fixed step data with shorter descriptions
        steps = [
            ("1", "🔍 Search", "Enter component"),
            ("2", "🤖 AI Analysis", "Review insights"),  
            ("3", "📊 Results", "Compare options"),
            ("4", "📄 Details", "View specs"),
            ("5", "✅ Decision", "Select & export")
        ]
        
        self.step_widgets = []
        
        # Create steps with equal spacing
        for i, (num, title, desc) in enumerate(steps):
            
            # Step container with fixed width
            step_container = tk.Frame(steps_container, bg='#f8f9fa')
            step_container.pack(side='left', expand=True, fill='x', padx=5)
            
            # Step circle
            circle_bg = '#007bff' if i+1 == self.current_step else '#28a745' if i+1 < self.current_step else '#e9ecef'
            text_color = 'white' if i+1 <= self.current_step else '#6c757d'
            
            circle = tk.Label(step_container, text=num, 
                             font=('Arial', 14, 'bold'),
                             bg=circle_bg, fg=text_color,
                             width=3, height=1, relief='solid', bd=0)
            circle.pack(pady=2)
            
            # Step title
            title_color = '#007bff' if i+1 == self.current_step else '#28a745' if i+1 < self.current_step else '#6c757d'
            title_label = tk.Label(step_container, text=title, 
                                  font=('Arial', 10, 'bold'),
                                  bg='#f8f9fa', fg=title_color)
            title_label.pack()
            
            # Step description
            desc_label = tk.Label(step_container, text=desc, 
                                 font=('Arial', 9),
                                 bg='#f8f9fa', fg='#6c757d')
            desc_label.pack()
            
            self.step_widgets.append((circle, title_label, desc_label))
            
            # Arrow between steps (except last)
            if i < len(steps) - 1:
                arrow_container = tk.Frame(steps_container, bg='#f8f9fa', width=30)
                arrow_container.pack(side='left', fill='y')
                arrow_container.pack_propagate(False)
                
                tk.Label(arrow_container, text="→", font=('Arial', 16), 
                        bg='#f8f9fa', fg='#dee2e6').pack(expand=True)
    
    def update_workflow_step(self, step):
        """Update workflow step indicator"""
        self.current_step = step
        
        for i, (circle, title_label, desc_label) in enumerate(self.step_widgets):
            if i + 1 == step:
                # Current step - blue
                circle.config(bg='#007bff', fg='white')
                title_label.config(fg='#007bff')
            elif i + 1 < step:
                # Completed step - green
                circle.config(bg='#28a745', fg='white')
                title_label.config(fg='#28a745')
            else:
                # Future step - gray
                circle.config(bg='#e9ecef', fg='#6c757d')
                title_label.config(fg='#6c757d')
    
    def show_demo_content(self):
        """Show demo content for each step"""
        
        # Clear content
        for widget in self.main_content.winfo_children():
            widget.destroy()
        
        if self.current_step == 1:
            self.show_search_step()
        elif self.current_step == 2:
            self.show_ai_step()
        elif self.current_step == 3:
            self.show_results_step()
        elif self.current_step == 4:
            self.show_details_step()
        elif self.current_step == 5:
            self.show_decision_step()
    
    def show_search_step(self):
        """Step 1: Search"""
        
        # Large centered search
        center_frame = tk.Frame(self.main_content, bg='#ffffff')
        center_frame.pack(expand=True)
        
        tk.Label(center_frame, text="🔍 What component are you looking for?", 
                font=('Arial', 24, 'bold'), bg='#ffffff').pack(pady=50)
        
        # Search input
        search_frame = tk.Frame(center_frame, bg='#ffffff')
        search_frame.pack(pady=20)
        
        self.search_var = tk.StringVar(value="10k resistor")
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, 
                               font=('Arial', 16), width=40, relief='solid', bd=2)
        search_entry.pack(pady=10, ipady=10)
        
        # Action buttons
        button_frame = tk.Frame(center_frame, bg='#ffffff')
        button_frame.pack(pady=30)
        
        tk.Button(button_frame, text="🤖 Start AI Analysis", 
                 command=lambda: self.go_to_step(2),
                 bg='#007bff', fg='white', font=('Arial', 14, 'bold'),
                 padx=30, pady=15).pack(side='left', padx=10)
        
        tk.Button(button_frame, text="⚡ Quick Search", 
                 command=lambda: self.go_to_step(3),
                 bg='#28a745', fg='white', font=('Arial', 14, 'bold'),
                 padx=30, pady=15).pack(side='left', padx=10)
    
    def show_ai_step(self):
        """Step 2: AI Analysis"""
        
        tk.Label(self.main_content, text="🤖 AI Analysis in Progress", 
                font=('Arial', 20, 'bold'), bg='#ffffff').pack(pady=20)
        
        # Large analysis area
        analysis_frame = tk.Frame(self.main_content, bg='#f8f9fa', relief='solid', bd=1)
        analysis_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        analysis_text = tk.Text(analysis_frame, font=('Arial', 12), 
                               bg='#ffffff', relief='flat', padx=30, pady=30)
        analysis_text.pack(fill='both', expand=True)
        
        content = """🤖 Gemini AI Analysis Complete

COMPONENT IDENTIFIED: 10kΩ Resistor
TYPE: Carbon Film Resistor
POWER RATING: 1/4W (0.25W)
TOLERANCE: ±5%

SPECIFICATIONS:
• Resistance: 10,000 Ohms
• Power: 0.25W maximum
• Temperature Coefficient: ±200 ppm/°C
• Package: Through-hole preferred

RECOMMENDED SEARCH TERMS:
🎯 "10k ohm resistor 1/4w carbon film"
🎯 "10000 ohm resistor through hole"

TOP MANUFACTURERS:
🏭 Vishay (premium quality)
🏭 Yageo (cost effective)
🏭 Panasonic (automotive grade)

AI CONFIDENCE: 95% ✅"""
        
        analysis_text.insert('1.0', content)
        analysis_text.config(state='disabled')
        
        # Navigation
        nav_frame = tk.Frame(self.main_content, bg='#ffffff')
        nav_frame.pack(fill='x', pady=20)
        
        tk.Button(nav_frame, text="⬅️ Back", command=lambda: self.go_to_step(1),
                 bg='#6c757d', fg='white', font=('Arial', 12)).pack(side='left')
        
        tk.Button(nav_frame, text="✅ Use AI Results", command=lambda: self.go_to_step(3),
                 bg='#007bff', fg='white', font=('Arial', 12, 'bold')).pack(side='right')
    
    def show_results_step(self):
        """Step 3: Results"""
        
        tk.Label(self.main_content, text="📊 Search Results", 
                font=('Arial', 20, 'bold'), bg='#ffffff').pack(pady=20)
        
        # Large results table
        table_frame = tk.Frame(self.main_content, bg='#ffffff')
        table_frame.pack(fill='both', expand=True, padx=20)
        
        columns = ('Rank', 'Supplier', 'Component', 'Price', 'Quality')
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150 if col == 'Component' else 100)
        
        # Sample data
        results = [
            ("1", "Robu Electronics", "10kΩ Carbon Film Resistor", "₹2.00", "A+"),
            ("2", "Probots", "10K Ohm Resistor", "₹1.50", "A"),
            ("3", "Evelta", "Resistor 10kΩ 0.25W", "₹2.50", "A+"),
        ]
        
        for result in results:
            tree.insert('', 'end', values=result)
        
        tree.pack(fill='both', expand=True)
        
        # Navigation
        nav_frame = tk.Frame(self.main_content, bg='#ffffff')
        nav_frame.pack(fill='x', pady=20)
        
        tk.Button(nav_frame, text="⬅️ Back", command=lambda: self.go_to_step(2),
                 bg='#6c757d', fg='white', font=('Arial', 12)).pack(side='left')
        
        tk.Button(nav_frame, text="📄 View Details", command=lambda: self.go_to_step(4),
                 bg='#007bff', fg='white', font=('Arial', 12, 'bold')).pack(side='right')
    
    def show_details_step(self):
        """Step 4: Details"""
        
        tk.Label(self.main_content, text="📄 Component Details & Datasheets", 
                font=('Arial', 20, 'bold'), bg='#ffffff').pack(pady=20)
        
        # Large details area
        details_frame = tk.Frame(self.main_content, bg='#f8f9fa', relief='solid', bd=1)
        details_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        details_text = tk.Text(details_frame, font=('Arial', 12), 
                              bg='#ffffff', relief='flat', padx=30, pady=30)
        details_text.pack(fill='both', expand=True)
        
        content = """📄 COMPONENT DATASHEET

VISHAY CARBON FILM RESISTOR
Part Number: CFR-25JB-52-10K

SPECIFICATIONS:
• Resistance: 10kΩ ±5%
• Power Rating: 0.25W
• Temperature Range: -55°C to +155°C
• Lead Spacing: 0.4 inch
• Body Size: 6.3mm x 2.3mm

APPLICATIONS:
✓ Pull-up/pull-down resistors
✓ Voltage dividers
✓ Current limiting
✓ General purpose circuits

PRICING (India):
• 1-99 pieces: ₹2.50 each
• 100-999 pieces: ₹1.80 each
• 1000+ pieces: ₹1.20 each

AVAILABILITY: ✅ In Stock"""
        
        details_text.insert('1.0', content)
        details_text.config(state='disabled')
        
        # Navigation
        nav_frame = tk.Frame(self.main_content, bg='#ffffff')
        nav_frame.pack(fill='x', pady=20)
        
        tk.Button(nav_frame, text="⬅️ Back", command=lambda: self.go_to_step(3),
                 bg='#6c757d', fg='white', font=('Arial', 12)).pack(side='left')
        
        tk.Button(nav_frame, text="✅ Make Decision", command=lambda: self.go_to_step(5),
                 bg='#28a745', fg='white', font=('Arial', 12, 'bold')).pack(side='right')
    
    def show_decision_step(self):
        """Step 5: Decision"""
        
        tk.Label(self.main_content, text="✅ Final Decision & Export", 
                font=('Arial', 20, 'bold'), bg='#ffffff').pack(pady=20)
        
        # Decision summary
        summary_frame = tk.Frame(self.main_content, bg='#d4edda', relief='solid', bd=1)
        summary_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        summary_text = tk.Text(summary_frame, font=('Arial', 12), 
                              bg='#d4edda', relief='flat', padx=30, pady=30)
        summary_text.pack(fill='both', expand=True)
        
        content = """🎯 SOURCING DECISION

SELECTED: 10kΩ Carbon Film Resistor 1/4W ±5%

RECOMMENDED SUPPLIER:
🥇 Robu Electronics
   Price: ₹2.00 each
   Quality: A+ rating
   Stock: 500+ units
   Delivery: 24 hours

TOTAL COST: ₹47.00 (including shipping)

CONFIDENCE: 95% ✅
AI VERIFIED: ✅
DATASHEET REVIEWED: ✅

READY FOR PROCUREMENT"""
        
        summary_text.insert('1.0', content)
        summary_text.config(state='disabled')
        
        # Export buttons
        export_frame = tk.Frame(self.main_content, bg='#ffffff')
        export_frame.pack(fill='x', pady=20)
        
        tk.Button(export_frame, text="📊 Export Report", 
                 bg='#007bff', fg='white', font=('Arial', 12, 'bold'),
                 padx=20, pady=10).pack(side='left', padx=10)
        
        tk.Button(export_frame, text="🛒 Purchase", 
                 bg='#28a745', fg='white', font=('Arial', 12, 'bold'),
                 padx=20, pady=10).pack(side='left', padx=10)
        
        tk.Button(export_frame, text="🔄 New Search", 
                 command=lambda: self.go_to_step(1),
                 bg='#6c757d', fg='white', font=('Arial', 12, 'bold'),
                 padx=20, pady=10).pack(side='right', padx=10)
    
    def go_to_step(self, step):
        """Navigate to specific step"""
        self.current_step = step
        self.update_workflow_step(step)
        self.show_demo_content()
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🔧 Fixed Workflow UI - No Text Occlusion")
    print("=" * 45)
    print("✅ Proper text spacing and layout")
    print("✅ No overlapping or cut-off text")
    print("✅ Responsive design for different screen sizes")
    print("✅ Clear visual hierarchy")
    print("✅ Intuitive workflow progression")
    
    app = FixedWorkflowUI()
    app.run()
