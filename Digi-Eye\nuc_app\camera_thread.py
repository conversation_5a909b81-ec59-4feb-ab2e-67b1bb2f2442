import threading
import cv2
from PIL import Image, ImageTk
import time
from ocr_engine import OcrEngine

class CameraThread(threading.Thread):
    def __init__(self, app_instance, camera_index=0):
        """
        Initializes the camera thread.
        :param app_instance: The main application instance (for UI updates).
        :param camera_index: The index of the camera to use (default is 0).
        """
        super().__init__()
        self.daemon = True  # Thread will close when main app closes
        self.app = app_instance
        self.stop_event = app_instance.stop_event
        self.camera_index = camera_index
        self.cap = None
        self.ocr_engine = OcrEngine()

    def run(self):
        """The main loop of the camera thread."""
        print("Camera thread started.")
        self.cap = cv2.VideoCapture(self.camera_index)

        if not self.cap.isOpened():
            print(f"Error: Could not open camera with index {self.camera_index}.")
            self.app.after(0, self.update_status, "Error", "red")
            return

        self.app.after(0, self.update_status, "Running", "green")

        while not self.stop_event.is_set():
            ret, frame = self.cap.read()
            if not ret:
                print("Warning: Could not read frame from camera.")
                time.sleep(0.1)
                continue

            # --- OCR Processing ---
            recognized_value, processed_frame = self.ocr_engine.recognize(frame)

            # Update the recognized value on the UI
            if recognized_value:
                self.app.after(0, self.update_recognized_value, recognized_value)

            # Convert the processed frame (with ROI drawn on it) to a format CTk can use
            frame_rgb = cv2.cvtColor(processed_frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(frame_rgb)
            
            # Update the UI in a thread-safe way
            self.app.after(0, self.update_image, pil_image)

            time.sleep(1/30) # Limit to ~30 FPS

        self.cap.release()
        print("Camera thread stopped and camera released.")
        self.app.after(0, self.update_status, "Stopped", "orange")

    def update_image(self, pil_image):
        """Updates the camera feed label in the main UI."""
        # Resize image to fit the frame while maintaining aspect ratio
        frame_w, frame_h = self.app.camera_frame.winfo_width(), self.app.camera_frame.winfo_height()
        
        # Avoid division by zero if frame is not yet rendered
        if frame_w == 1 or frame_h == 1: 
            self.app.after(20, self.update_image, pil_image)
            return

        img_w, img_h = pil_image.size
        aspect_ratio = img_w / img_h

        new_w = frame_w
        new_h = int(new_w / aspect_ratio)

        if new_h > frame_h:
            new_h = frame_h
            new_w = int(new_h * aspect_ratio)

        resized_image = pil_image.resize((new_w, new_h), Image.LANCZOS)
        
        ctk_image = ImageTk.PhotoImage(resized_image)
        self.app.camera_label.configure(image=ctk_image, text="")
        self.app.camera_label.image = ctk_image # Keep a reference

    def update_status(self, status_text, color):
        """Updates the camera status label in the main UI."""
        self.app.camera_status_label.configure(text=status_text, text_color=color)

    def update_recognized_value(self, value):
        """Updates the recognized value label in the main UI."""
        self.app.recognized_value_label.configure(text=value)